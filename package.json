{"name": "cdtvn_v2", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "dev-host": "adb reverse tcp:9090 tcp:9090", "lint": "eslint .", "start": "react-native start", "test": "jest", "prepare": "husky", "commitlint": "commitlint --edit", "push-android": "appcenter codepush release-react -a GTO-Software/eCDT-UAT -d Production", "gen-changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "version:up": "node ./node_modules/react-native-version-up/index.js", "storybook-generate": "sb-rn-get-stories --config-path .ondevice", "storybook": "cross-env STORYBOOK_ENABLED='true' yarn start", "storybook:ios": "cross-env STORYBOOK_ENABLED='true' yarn start --ios", "storybook:android": "cross-env STORYBOOK_ENABLED='true' yarn start --android", "storybook:web": "start-storybook -p 6006 -c .storybook-web", "build-storybook": "cross-env NODE_OPTIONS=--openssl-legacy-provider build-storybook", "optimize-images": "node scripts/optimize-images.js", "build:android:release": "cd android && ./gradlew assembleRelease", "build:android:bundle": "cd android && ./gradlew bundleRelease", "analyze:bundle": "npx react-native-bundle-visualizer", "clean:android": "cd android && ./gradlew clean", "clean:ios": "cd ios && xcodebuild clean", "clean:metro": "npx react-native start --reset-cache", "clear:cache": "yarn cache clean && npm cache clean --force && watchman watch-del-all", "clear:all": "yarn clean:android && yarn clean:ios && yarn clean:metro && yarn clear:cache"}, "dependencies": {"@gorhom/bottom-sheet": "^5.0.4", "@react-native-community/netinfo": "^11.3.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@types/react-native-modals": "^0.22.4", "@types/uuid": "^10.0.0", "axios": "^1.6.8", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "18.2.0", "react-hook-form": "^7.51.3", "react-native": "0.73.7", "react-native-alert-notification": "^0.4.0", "react-native-animatable": "^1.4.0", "react-native-barcode-mask": "^1.2.4", "react-native-bootsplash": "^5.5.3", "react-native-calendars": "^1.1309.1", "react-native-camera": "^4.2.1", "react-native-code-push": "^8.2.2", "react-native-config": "^1.5.1", "react-native-date-picker": "^5.0.10", "react-native-device-info": "13.1.0", "react-native-dropdown-picker": "^5.4.6", "react-native-element-dropdown": "^2.12.4", "react-native-elements": "^3.4.3", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.16.0", "react-native-get-random-values": "^1.11.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-spacer": "^0.4.1", "react-native-linear-gradient": "^2.8.3", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^17.1.0", "react-native-modalize": "^2.1.1", "react-native-modals": "^0.22.3", "react-native-notificated": "^0.1.5", "react-native-pager-view": "^6.3.1", "react-native-portalize": "^1.0.7", "react-native-progress": "^5.0.1", "react-native-qrcode-svg": "^6.3.0", "react-native-reanimated": "^3.11.0", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "^3.30.1", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.1.0", "react-native-tab-view": "^3.5.2", "react-native-vector-icons": "^10.0.3", "react-native-version-up": "^1.0.9", "react-native-wifi-reborn": "^4.13.5", "react-query": "^3.39.3", "storybook": "^8.0.8", "toggle-switch-react-native": "^3.3.0", "uuid": "^10.0.0", "uuid-random": "^1.3.2", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-transform-runtime": "^7.27.3", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@chromatic-com/storybook": "^1", "@commitlint/cli": "^19.2.2", "@commitlint/config-conventional": "^19.2.2", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/datetimepicker": "^8.1.1", "@react-native-community/eslint-config": "^3.2.0", "@react-native-community/slider": "^4.5.2", "@react-native/babel-preset": "^0.75.0-main", "@react-native/eslint-config": "^0.74.83", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@storybook/addon-essentials": "^6.5", "@storybook/addon-ondevice-actions": "^7.6.18", "@storybook/addon-ondevice-backgrounds": "^7.6.18", "@storybook/addon-ondevice-controls": "^7.6.18", "@storybook/addon-ondevice-notes": "^7.6.18", "@storybook/addon-react-native-web": "^0.0.23", "@storybook/addon-webpack5-compiler-babel": "^3.0.3", "@storybook/builder-webpack5": "^6.5", "@storybook/manager-webpack5": "^6.5", "@storybook/react": "^6.5", "@storybook/react-native": "^7.6.18", "@storybook/react-webpack5": "^8.0.8", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/react": "^18.2.6", "@types/react-native-keyboard-spacer": "^0.4.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "babel-loader": "^8.3.0", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-react-native-web": "^0.19.10", "babel-plugin-transform-imports": "^2.0.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "babel-plugin-transform-remove-console": "^6.9.4", "conventional-changelog": "^5.1.0", "conventional-changelog-cli": "^4.1.0", "deprecated-react-native-prop-types": "^5.0.0", "eslint": "^8.19.0", "eslint-plugin-extra-rules": "^0.0.0-development", "eslint-plugin-import": "^2.29.1", "eslint-plugin-simple-import-sort": "^12.1.0", "husky": "^9.0.11", "jest": "^29.6.3", "prettier": "2.8.8", "react-docgen-typescript": "^2.2.2", "react-dom": "^18.2.0", "react-native-svg-transformer": "^1.3.0", "react-native-web": "^0.19.10", "react-test-renderer": "18.2.0", "reactotron-react-native": "^5.1.6", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "resolutions": {"@storybook/react-docgen-typescript-plugin": "1.0.6--canary.9.cd77847.0"}}