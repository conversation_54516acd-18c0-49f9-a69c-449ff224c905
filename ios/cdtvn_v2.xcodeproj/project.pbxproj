// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		004A39232C37451C8867CEC6 /* Feather.ttf in Resources */ = {isa = PBXBuildFile; fileRef = DFD5AD3D78354866851A8A09 /* Feather.ttf */; };
		00E356F31AD99517003FC87E /* cdtvn_v2Tests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* cdtvn_v2Tests.m */; };
		0677A8EDE9B543CE89EE841A /* FontAwesome5_Brands.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2726E38F46A54797BC810A08 /* FontAwesome5_Brands.ttf */; };
		0AD5FDF7D4C74F4997E34A89 /* MaterialIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 1B180311C47745D3AA15F524 /* MaterialIcons.ttf */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		1A18CEBCE208C5E3E82E4EAB /* libPods-cdtvn_v2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BFF6C070474EAFBD185A59AD /* libPods-cdtvn_v2.a */; };
		2718E33211F74D10A63D3328 /* FontAwesome.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 03C54DE9DD784F7694EA02A8 /* FontAwesome.ttf */; };
		2DA9B8DE00A24FF2963B3C01 /* Zocial.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 63B67FBBFBA241ABA9AA9364 /* Zocial.ttf */; };
		3C58BDF02D970968001F4E41 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 3C58BDEF2D970968001F4E41 /* PrivacyInfo.xcprivacy */; };
		3CF343C32CCBD31A0097A395 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		3CF343C42CCBDD9F0097A395 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 3CF343C62CCBDD9F0097A395 /* LaunchScreen.storyboard */; };
		417D438F32F049568F91FDC0 /* EvilIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = DABF196766B0472B86EA696D /* EvilIcons.ttf */; };
		45489F459591E1A06E0D2B49 /* libPods-cdtvn_v2-cdtvn_v2Tests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2DC791E36F43801B43B6EA /* libPods-cdtvn_v2-cdtvn_v2Tests.a */; };
		56EF105D4C31498BAE8CC8C4 /* FontAwesome5_Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 81E4E392E15042CE8AE0B832 /* FontAwesome5_Regular.ttf */; };
		5C71255C2CAB4CEDB0585AD1 /* AntDesign.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 607A0175C207405590CB14F3 /* AntDesign.ttf */; };
		5FCF12E147B34F948B6FEB26 /* Foundation.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F0C49C43379B433683681079 /* Foundation.ttf */; };
		64EE5B97205D4B55BDB3F156 /* Fontisto.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 38B451A7C3E3400482C4F92B /* Fontisto.ttf */; };
		70CC9EF1758A46F6B01E61C0 /* BeVietnamPro-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 742E3AC282E94CFEAD26D42B /* BeVietnamPro-Regular.ttf */; };
		7C6E4CA082B44D249929DDCB /* BeVietnamPro-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7AFB674E843E4870845EFFBB /* BeVietnamPro-Medium.ttf */; };
		9E0EA621216649729EAB7FAA /* FontAwesome6_Brands.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8AB2F8A4D3114104BD8B66F0 /* FontAwesome6_Brands.ttf */; };
		A0A166EBEF2C40E7BE0A12D0 /* FontAwesome6_Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D141FBF4D58B46C49A0D0640 /* FontAwesome6_Regular.ttf */; };
		A67366DF075647B8989932E8 /* Entypo.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA3DA8F9FF124E56991601D6 /* Entypo.ttf */; };
		A8D36EE071574ED5B964CC43 /* BeVietnamPro-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CCFA54D94F9F4D57982838E1 /* BeVietnamPro-SemiBold.ttf */; };
		A95295FAF1034CC8B1D2FC6F /* Ionicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AAACED557902410CBB2E1DB9 /* Ionicons.ttf */; };
		AC63A4A2C1DF402BB088BB45 /* MaterialCommunityIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 450DC3570DBB41B6947980B9 /* MaterialCommunityIcons.ttf */; };
		AF2F89E5FEED4E1CBB9B4F3F /* BeVietnamPro-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3BF035B53423437D82874A07 /* BeVietnamPro-Italic.ttf */; };
		C1460ACB6FE143D8A3D037C1 /* FontAwesome5_Solid.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B9F9039F6EDB415899F95423 /* FontAwesome5_Solid.ttf */; };
		DBAC7E01C3234F919C0669B7 /* BeVietnamPro-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BACAB065A3374E40A84CC528 /* BeVietnamPro-Light.ttf */; };
		DCA0B99BDA6F429391331912 /* FontAwesome6_Solid.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5A9EE2A7637F4CC0A72DD1D2 /* FontAwesome6_Solid.ttf */; };
		DDFA47FB8C644BA795B624E3 /* SimpleLineIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = DEFF603D6E84481790555D78 /* SimpleLineIcons.ttf */; };
		EAC876D68AC34F53938EE040 /* BeVietnamPro-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 51F596DAF51B4E94BE96FB66 /* BeVietnamPro-Thin.ttf */; };
		F2FB8446F38F48C5876C7D0D /* Octicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B7AF9FB1D18249709D523CB7 /* Octicons.ttf */; };
		FF160796FC9C4B4AAD802E86 /* BeVietnamPro-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 47622A2D4B0A485EBE0B4CAB /* BeVietnamPro-Bold.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = cdtvn_v2;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* cdtvn_v2Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = cdtvn_v2Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* cdtvn_v2Tests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = cdtvn_v2Tests.m; sourceTree = "<group>"; };
		03C54DE9DD784F7694EA02A8 /* FontAwesome.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome.ttf; path = ../assets/fonts/FontAwesome.ttf; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* cdtvn_v2.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = cdtvn_v2.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = cdtvn_v2/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = cdtvn_v2/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = cdtvn_v2/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = cdtvn_v2/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = cdtvn_v2/main.m; sourceTree = "<group>"; };
		1B180311C47745D3AA15F524 /* MaterialIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialIcons.ttf; path = ../assets/fonts/MaterialIcons.ttf; sourceTree = "<group>"; };
		2726E38F46A54797BC810A08 /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Brands.ttf; path = ../assets/fonts/FontAwesome5_Brands.ttf; sourceTree = "<group>"; };
		2890708C0F5F994645471371 /* Pods-cdtvn_v2-cdtvn_v2Tests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-cdtvn_v2-cdtvn_v2Tests.debug.xcconfig"; path = "Target Support Files/Pods-cdtvn_v2-cdtvn_v2Tests/Pods-cdtvn_v2-cdtvn_v2Tests.debug.xcconfig"; sourceTree = "<group>"; };
		38B451A7C3E3400482C4F92B /* Fontisto.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Fontisto.ttf; path = ../assets/fonts/Fontisto.ttf; sourceTree = "<group>"; };
		3BF035B53423437D82874A07 /* BeVietnamPro-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnamPro-Italic.ttf"; path = "../assets/fonts/BeVietnamPro-Italic.ttf"; sourceTree = "<group>"; };
		3C58BDEF2D970968001F4E41 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		3CF343C52CCBDD9F0097A395 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		3CF343C72CCBDD9F0097A395 /* mul */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; name = mul; path = mul.lproj/LaunchScreen.xcstrings; sourceTree = "<group>"; };
		450DC3570DBB41B6947980B9 /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialCommunityIcons.ttf; path = ../assets/fonts/MaterialCommunityIcons.ttf; sourceTree = "<group>"; };
		47622A2D4B0A485EBE0B4CAB /* BeVietnamPro-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnamPro-Bold.ttf"; path = "../assets/fonts/BeVietnamPro-Bold.ttf"; sourceTree = "<group>"; };
		492E1FE9353FC42D8C57CA27 /* Pods-cdtvn_v2.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-cdtvn_v2.debug.xcconfig"; path = "Target Support Files/Pods-cdtvn_v2/Pods-cdtvn_v2.debug.xcconfig"; sourceTree = "<group>"; };
		51F596DAF51B4E94BE96FB66 /* BeVietnamPro-Thin.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnamPro-Thin.ttf"; path = "../assets/fonts/BeVietnamPro-Thin.ttf"; sourceTree = "<group>"; };
		5610F1BF11CD234112CED475 /* Pods-cdtvn_v2-cdtvn_v2Tests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-cdtvn_v2-cdtvn_v2Tests.release.xcconfig"; path = "Target Support Files/Pods-cdtvn_v2-cdtvn_v2Tests/Pods-cdtvn_v2-cdtvn_v2Tests.release.xcconfig"; sourceTree = "<group>"; };
		5A9EE2A7637F4CC0A72DD1D2 /* FontAwesome6_Solid.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome6_Solid.ttf; path = ../assets/fonts/FontAwesome6_Solid.ttf; sourceTree = "<group>"; };
		607A0175C207405590CB14F3 /* AntDesign.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = AntDesign.ttf; path = ../assets/fonts/AntDesign.ttf; sourceTree = "<group>"; };
		63B67FBBFBA241ABA9AA9364 /* Zocial.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Zocial.ttf; path = ../assets/fonts/Zocial.ttf; sourceTree = "<group>"; };
		742E3AC282E94CFEAD26D42B /* BeVietnamPro-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnamPro-Regular.ttf"; path = "../assets/fonts/BeVietnamPro-Regular.ttf"; sourceTree = "<group>"; };
		7AFB674E843E4870845EFFBB /* BeVietnamPro-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnamPro-Medium.ttf"; path = "../assets/fonts/BeVietnamPro-Medium.ttf"; sourceTree = "<group>"; };
		81E4E392E15042CE8AE0B832 /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Regular.ttf; path = ../assets/fonts/FontAwesome5_Regular.ttf; sourceTree = "<group>"; };
		8AB2F8A4D3114104BD8B66F0 /* FontAwesome6_Brands.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome6_Brands.ttf; path = ../assets/fonts/FontAwesome6_Brands.ttf; sourceTree = "<group>"; };
		AA3DA8F9FF124E56991601D6 /* Entypo.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Entypo.ttf; path = ../assets/fonts/Entypo.ttf; sourceTree = "<group>"; };
		AAACED557902410CBB2E1DB9 /* Ionicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Ionicons.ttf; path = ../assets/fonts/Ionicons.ttf; sourceTree = "<group>"; };
		B7AF9FB1D18249709D523CB7 /* Octicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Octicons.ttf; path = ../assets/fonts/Octicons.ttf; sourceTree = "<group>"; };
		B9F9039F6EDB415899F95423 /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Solid.ttf; path = ../assets/fonts/FontAwesome5_Solid.ttf; sourceTree = "<group>"; };
		BACAB065A3374E40A84CC528 /* BeVietnamPro-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnamPro-Light.ttf"; path = "../assets/fonts/BeVietnamPro-Light.ttf"; sourceTree = "<group>"; };
		BFF6C070474EAFBD185A59AD /* libPods-cdtvn_v2.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-cdtvn_v2.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		CCFA54D94F9F4D57982838E1 /* BeVietnamPro-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "BeVietnamPro-SemiBold.ttf"; path = "../assets/fonts/BeVietnamPro-SemiBold.ttf"; sourceTree = "<group>"; };
		D141FBF4D58B46C49A0D0640 /* FontAwesome6_Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome6_Regular.ttf; path = ../assets/fonts/FontAwesome6_Regular.ttf; sourceTree = "<group>"; };
		DABF196766B0472B86EA696D /* EvilIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = EvilIcons.ttf; path = ../assets/fonts/EvilIcons.ttf; sourceTree = "<group>"; };
		DEFF603D6E84481790555D78 /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SimpleLineIcons.ttf; path = ../assets/fonts/SimpleLineIcons.ttf; sourceTree = "<group>"; };
		DFD5AD3D78354866851A8A09 /* Feather.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Feather.ttf; path = ../assets/fonts/Feather.ttf; sourceTree = "<group>"; };
		ECAE04C1383FD737A2FDBD25 /* Pods-cdtvn_v2.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-cdtvn_v2.release.xcconfig"; path = "Target Support Files/Pods-cdtvn_v2/Pods-cdtvn_v2.release.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		EF2DC791E36F43801B43B6EA /* libPods-cdtvn_v2-cdtvn_v2Tests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-cdtvn_v2-cdtvn_v2Tests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		F0C49C43379B433683681079 /* Foundation.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Foundation.ttf; path = ../assets/fonts/Foundation.ttf; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				45489F459591E1A06E0D2B49 /* libPods-cdtvn_v2-cdtvn_v2Tests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A18CEBCE208C5E3E82E4EAB /* libPods-cdtvn_v2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* cdtvn_v2Tests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* cdtvn_v2Tests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = cdtvn_v2Tests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* cdtvn_v2 */ = {
			isa = PBXGroup;
			children = (
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				3CF343C62CCBDD9F0097A395 /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				3C58BDEF2D970968001F4E41 /* PrivacyInfo.xcprivacy */,
			);
			name = cdtvn_v2;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				BFF6C070474EAFBD185A59AD /* libPods-cdtvn_v2.a */,
				EF2DC791E36F43801B43B6EA /* libPods-cdtvn_v2-cdtvn_v2Tests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		43E8B6689EBD40C8A5638CC8 /* Resources */ = {
			isa = PBXGroup;
			children = (
				47622A2D4B0A485EBE0B4CAB /* BeVietnamPro-Bold.ttf */,
				3BF035B53423437D82874A07 /* BeVietnamPro-Italic.ttf */,
				BACAB065A3374E40A84CC528 /* BeVietnamPro-Light.ttf */,
				7AFB674E843E4870845EFFBB /* BeVietnamPro-Medium.ttf */,
				742E3AC282E94CFEAD26D42B /* BeVietnamPro-Regular.ttf */,
				CCFA54D94F9F4D57982838E1 /* BeVietnamPro-SemiBold.ttf */,
				51F596DAF51B4E94BE96FB66 /* BeVietnamPro-Thin.ttf */,
				607A0175C207405590CB14F3 /* AntDesign.ttf */,
				AA3DA8F9FF124E56991601D6 /* Entypo.ttf */,
				DABF196766B0472B86EA696D /* EvilIcons.ttf */,
				DFD5AD3D78354866851A8A09 /* Feather.ttf */,
				03C54DE9DD784F7694EA02A8 /* FontAwesome.ttf */,
				2726E38F46A54797BC810A08 /* FontAwesome5_Brands.ttf */,
				81E4E392E15042CE8AE0B832 /* FontAwesome5_Regular.ttf */,
				B9F9039F6EDB415899F95423 /* FontAwesome5_Solid.ttf */,
				8AB2F8A4D3114104BD8B66F0 /* FontAwesome6_Brands.ttf */,
				D141FBF4D58B46C49A0D0640 /* FontAwesome6_Regular.ttf */,
				5A9EE2A7637F4CC0A72DD1D2 /* FontAwesome6_Solid.ttf */,
				38B451A7C3E3400482C4F92B /* Fontisto.ttf */,
				F0C49C43379B433683681079 /* Foundation.ttf */,
				AAACED557902410CBB2E1DB9 /* Ionicons.ttf */,
				450DC3570DBB41B6947980B9 /* MaterialCommunityIcons.ttf */,
				1B180311C47745D3AA15F524 /* MaterialIcons.ttf */,
				B7AF9FB1D18249709D523CB7 /* Octicons.ttf */,
				DEFF603D6E84481790555D78 /* SimpleLineIcons.ttf */,
				63B67FBBFBA241ABA9AA9364 /* Zocial.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* cdtvn_v2 */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* cdtvn_v2Tests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				43E8B6689EBD40C8A5638CC8 /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* cdtvn_v2.app */,
				00E356EE1AD99517003FC87E /* cdtvn_v2Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				492E1FE9353FC42D8C57CA27 /* Pods-cdtvn_v2.debug.xcconfig */,
				ECAE04C1383FD737A2FDBD25 /* Pods-cdtvn_v2.release.xcconfig */,
				2890708C0F5F994645471371 /* Pods-cdtvn_v2-cdtvn_v2Tests.debug.xcconfig */,
				5610F1BF11CD234112CED475 /* Pods-cdtvn_v2-cdtvn_v2Tests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* cdtvn_v2Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "cdtvn_v2Tests" */;
			buildPhases = (
				248D62A082AFE21517FC2853 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				40B030D72F5786F3FADB29C7 /* [CP] Embed Pods Frameworks */,
				9D7DD27349C0F7CA8131911C /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = cdtvn_v2Tests;
			productName = cdtvn_v2Tests;
			productReference = 00E356EE1AD99517003FC87E /* cdtvn_v2Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* cdtvn_v2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "cdtvn_v2" */;
			buildPhases = (
				08CDE6125C73F25DD457F3F4 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				D0FBC0B35AF6B201B60C1742 /* [CP] Embed Pods Frameworks */,
				53E4828AA66D2344EE806571 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = cdtvn_v2;
			productName = cdtvn_v2;
			productReference = 13B07F961A680F5B00A75B9A /* cdtvn_v2.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "cdtvn_v2" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* cdtvn_v2 */,
				00E356ED1AD99517003FC87E /* cdtvn_v2Tests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3CF343C32CCBD31A0097A395 /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3CF343C42CCBDD9F0097A395 /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				3C58BDF02D970968001F4E41 /* PrivacyInfo.xcprivacy in Resources */,
				FF160796FC9C4B4AAD802E86 /* BeVietnamPro-Bold.ttf in Resources */,
				AF2F89E5FEED4E1CBB9B4F3F /* BeVietnamPro-Italic.ttf in Resources */,
				DBAC7E01C3234F919C0669B7 /* BeVietnamPro-Light.ttf in Resources */,
				7C6E4CA082B44D249929DDCB /* BeVietnamPro-Medium.ttf in Resources */,
				70CC9EF1758A46F6B01E61C0 /* BeVietnamPro-Regular.ttf in Resources */,
				A8D36EE071574ED5B964CC43 /* BeVietnamPro-SemiBold.ttf in Resources */,
				EAC876D68AC34F53938EE040 /* BeVietnamPro-Thin.ttf in Resources */,
				5C71255C2CAB4CEDB0585AD1 /* AntDesign.ttf in Resources */,
				A67366DF075647B8989932E8 /* Entypo.ttf in Resources */,
				417D438F32F049568F91FDC0 /* EvilIcons.ttf in Resources */,
				004A39232C37451C8867CEC6 /* Feather.ttf in Resources */,
				2718E33211F74D10A63D3328 /* FontAwesome.ttf in Resources */,
				0677A8EDE9B543CE89EE841A /* FontAwesome5_Brands.ttf in Resources */,
				56EF105D4C31498BAE8CC8C4 /* FontAwesome5_Regular.ttf in Resources */,
				C1460ACB6FE143D8A3D037C1 /* FontAwesome5_Solid.ttf in Resources */,
				9E0EA621216649729EAB7FAA /* FontAwesome6_Brands.ttf in Resources */,
				A0A166EBEF2C40E7BE0A12D0 /* FontAwesome6_Regular.ttf in Resources */,
				DCA0B99BDA6F429391331912 /* FontAwesome6_Solid.ttf in Resources */,
				64EE5B97205D4B55BDB3F156 /* Fontisto.ttf in Resources */,
				5FCF12E147B34F948B6FEB26 /* Foundation.ttf in Resources */,
				A95295FAF1034CC8B1D2FC6F /* Ionicons.ttf in Resources */,
				AC63A4A2C1DF402BB088BB45 /* MaterialCommunityIcons.ttf in Resources */,
				0AD5FDF7D4C74F4997E34A89 /* MaterialIcons.ttf in Resources */,
				F2FB8446F38F48C5876C7D0D /* Octicons.ttf in Resources */,
				DDFA47FB8C644BA795B624E3 /* SimpleLineIcons.ttf in Resources */,
				2DA9B8DE00A24FF2963B3C01 /* Zocial.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		08CDE6125C73F25DD457F3F4 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-cdtvn_v2-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		248D62A082AFE21517FC2853 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-cdtvn_v2-cdtvn_v2Tests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		40B030D72F5786F3FADB29C7 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2-cdtvn_v2Tests/Pods-cdtvn_v2-cdtvn_v2Tests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2-cdtvn_v2Tests/Pods-cdtvn_v2-cdtvn_v2Tests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2-cdtvn_v2Tests/Pods-cdtvn_v2-cdtvn_v2Tests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		53E4828AA66D2344EE806571 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2/Pods-cdtvn_v2-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2/Pods-cdtvn_v2-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2/Pods-cdtvn_v2-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9D7DD27349C0F7CA8131911C /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2-cdtvn_v2Tests/Pods-cdtvn_v2-cdtvn_v2Tests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2-cdtvn_v2Tests/Pods-cdtvn_v2-cdtvn_v2Tests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2-cdtvn_v2Tests/Pods-cdtvn_v2-cdtvn_v2Tests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		D0FBC0B35AF6B201B60C1742 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2/Pods-cdtvn_v2-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2/Pods-cdtvn_v2-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-cdtvn_v2/Pods-cdtvn_v2-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00E356F31AD99517003FC87E /* cdtvn_v2Tests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* cdtvn_v2 */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		3CF343C62CCBDD9F0097A395 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				3CF343C52CCBDD9F0097A395 /* Base */,
				3CF343C72CCBDD9F0097A395 /* mul */,
			);
			name = LaunchScreen.storyboard;
			path = cdtvn_v2;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2890708C0F5F994645471371 /* Pods-cdtvn_v2-cdtvn_v2Tests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = cdtvn_v2Tests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/cdtvn_v2.app/cdtvn_v2";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5610F1BF11CD234112CED475 /* Pods-cdtvn_v2-cdtvn_v2Tests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = cdtvn_v2Tests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/cdtvn_v2.app/cdtvn_v2";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 492E1FE9353FC42D8C57CA27 /* Pods-cdtvn_v2.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 58GKP5FJV2;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = cdtvn_v2/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Hỗ trợ NKĐT";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3.13;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.gto-software.cdtnkht";
				PRODUCT_NAME = cdtvn_v2;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ECAE04C1383FD737A2FDBD25 /* Pods-cdtvn_v2.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 58GKP5FJV2;
				INFOPLIST_FILE = cdtvn_v2/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Hỗ trợ NKĐT";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3.13;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.gto-software.cdtnkht";
				PRODUCT_NAME = cdtvn_v2;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "cdtvn_v2Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "cdtvn_v2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "cdtvn_v2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
