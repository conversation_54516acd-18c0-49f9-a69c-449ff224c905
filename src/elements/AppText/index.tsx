import COLORS from '../../assets/theme/colors';
import {FONT_TYPE} from '../../utils';
import React, {memo} from 'react';
import {Text, TextStyle} from 'react-native';

type AppTextProps = {
  text: string | undefined;
  size?: number;
  font?: string;
  color?: string;
  data?: any;
  customStyle?: TextStyle;
};

export const AppText = ({
  text,
  font = FONT_TYPE.REGULAR,
  size,
  color = COLORS.MAIN_COLOR,
  customStyle = {},
  data = {},
  ...props
}: AppTextProps) => {
  const textStyle: TextStyle = {
    fontSize: size,
    fontFamily: FONTS[font as keyof typeof FONTS],
    color: color,
    ...customStyle,
  };
  if (data.local != null) {
    return (
      <Text {...props} style={textStyle}>
        {'('} <Text style={{color: COLORS.RED}}>{data.local}</Text>
        {'/' + data.total + ' )'}
      </Text>
    );
  }

  return (
    <Text {...props} style={textStyle}>
      {text}
    </Text>
  );
};

const FONTS = {
  BOLD: 'BeVietnamPro-Bold',
  ITALIC: 'BeVietnamPro-Italic',
  LIGHT: 'BeVietnamPro-Light',
  MEDIUM: 'BeVietnamPro-Medium',
  REGULAR: 'BeVietnamPro-Regular',
  SEMI_BOLD: 'BeVietnamPro-SemiBold',
  THIN: 'BeVietnamPro-Thin',
};

export default memo(AppText);
