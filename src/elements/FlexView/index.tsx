import React from 'react';
import {View, ViewStyle} from 'react-native';

type FlexViewProps = {
  flex: number;
  backgroundColor?: string;
  customStyle?: ViewStyle;
};

const FlexView = ({flex, backgroundColor, customStyle}: FlexViewProps) => {
  const flexViewStyle = {
    flex: flex,
    backgroundColor: backgroundColor,
    ...customStyle,
  };
  return <View style={flexViewStyle} />;
};

export default FlexView;
