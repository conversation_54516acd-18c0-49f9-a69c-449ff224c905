import COLORS from '../../assets/theme/colors';
import React, {memo} from 'react';
import {StyleSheet, View, ViewStyle} from 'react-native';
import {Edges, SafeAreaView} from 'react-native-safe-area-context';

type BaseScreenProps = {
  children: any;
  edges?: Edges | undefined;
};

export const BaseScreen = ({children, edges = ['top']}: BaseScreenProps) => {
  const baseScreenStyle: ViewStyle = {
    flex: 1,
    ...styles.container,
  };
  return (
    <>
      <SafeAreaView edges={edges} style={baseScreenStyle}>
        <View style={{backgroundColor: COLORS.BACKGROUND, flex: 1}}>
          {children}
        </View>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.MAIN_COLOR,
  },
});

export default memo(BaseScreen);
