import {AppIcon} from '.';
import COLORS from '../assets/theme/colors';
import StatefulComponent from '../lib/StatefulComponent';
import {globalStyles} from '../utils';
import {scaleBaseOnScreenHeight, scaleBaseOnScreenWidth} from '../utils/layout';
import {get, isEmpty} from 'lodash';
import React from 'react';
import {
  Animated,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Keyboard,
  KeyboardAvoidingView,
} from 'react-native';

export default class CustomTextField extends StatefulComponent {
  static defaultProps = {
    lightLabel: false,
    shadow: false,
    required: false,
    autoCompleteType: 'off',
    autoCorrect: false,
    editable: true,
    multiline: false,
  };

  state = {
    isFocus: false,
    showPassword: false,
    autoFocus: true,
  };

  get inputStyle() {
    const {isFocus} = this.state;
    const {
      value,
      placeholderText,
      textColor = COLORS.MEDIUM_BLACK,
      multiline,
      editable = true,
      fontSize = scaleBaseOnScreenWidth(16),
    } = this.props;
    return [
      styles.inputStyle,
      (isFocus || !isEmpty(value)) &&
        isEmpty(placeholderText) &&
        styles.marginTop,
      {color: textColor},
      {fontSize},
      !editable && styles.disabled,
      multiline && styles.multilineInput,
    ];
  }

  animation = new Animated.Value(20);

  doActive = () => {
    const {value, autoFocus} = this.props;
    const {isFocus} = this.state;
    const isActive = autoFocus || isFocus || !isEmpty(value);

    Animated.timing(this.animation, {
      toValue: scaleBaseOnScreenHeight(isActive ? 0 : 20),
      useNativeDriver: true,
      duration: 200,
    }).start();
  };

  componentDidMount() {
    if (Platform.OS === 'android') {
      Keyboard.dismiss();
    }
    this.doActive();
    if (this.props.autoFocus) {
      setTimeout(() => {
        this.input.blur();
        this.input.focus();
      }, 200);
    }
  }

  async componentDidUpdate(prevProps, prevState) {
    if (
      (this.state?.isFocus || !isEmpty(this.props?.value)) !==
      (prevState?.isFocus || !isEmpty(prevProps?.value))
    ) {
      // await wait(300);
      this.doActive();
    }
  }

  get label() {
    const {
      placeholderText,
      required,
      prependIcon,
      labelFontSize = scaleBaseOnScreenWidth(13),
    } = this.props;
    return (
      <Animated.View
        pointerEvents="none"
        style={[
          styles.labelContainer,
          prependIcon && {left: scaleBaseOnScreenWidth(38)},
          {
            transform: [
              {
                translateY: this.animation,
              },
            ],
          },
        ]}>
        <Text
          numberOfLines={1}
          style={[styles.label, {fontSize: labelFontSize}]}>
          {placeholderText}
          {required && <Text style={styles.required}>{' (❊)'}</Text>}
        </Text>
      </Animated.View>
    );
  }

  showPassword = () => {
    const {showPassword} = this.state;
    this.setState({showPassword: !showPassword});
  };

  clear = () => {
    if (this.input) {
      this.input.clear();
    }
  };

  onFocus = () => {
    const {onSuggest, onFocus} = this.props;
    if (onFocus) {
      onFocus();
    }
    if (onSuggest) {
      onSuggest();
    } else {
      this.toggleBooleanState('isFocus', true)();
    }
  };

  onBlur = () => {
    if (this.props?.onBlur) {
      this.props?.onBlur();
    }
    this.toggleBooleanState('isFocus', false)();
  };

  renderTextInput = () => {
    const {
      value,
      autoCapitalize = 'none',
      editable = true,
      multiline,
      placeholder,
      autoFocus,
    } = this.props;
    const isPassword = get(this.props, 'isPassword', false);
    const {showPassword} = this.state;
    const {isFocus} = this.state;
    const isActive = isFocus || !isEmpty(value);
    if (!editable) {
      return (
        <Text numberOfLines={multiline ? 3 : 1} style={this.inputStyle}>
          {this.props?.value}
        </Text>
      );
    }
    const {width: _width, ...rest} = this.props;

    return (
      <KeyboardAvoidingView
        style={{flex: 1, justifyContent: 'center', alignItems: 'flex-start'}}>
        <TextInput
          ref={c => (this.input = c)}
          autoCapitalize={autoCapitalize}
          editable={editable}
          multiline={multiline}
          numberOfLines={multiline ? 2 : 1}
          placeholderTextColor={COLORS.TRANSPARENT_GREY}
          renderToHardwareTextureAndroid
          returnKeyType="go"
          style={this.inputStyle}
          value={value}
          {...rest}
          autoFocus={true}
          autoCompleteType={'off'}
          autoCorrect={false}
          placeholder={isActive ? placeholder : ''}
          secureTextEntry={isPassword && !showPassword}
          onBlur={this.onBlur}
          onFocus={this.onFocus}
        />
      </KeyboardAvoidingView>
    );
  };

  render() {
    const {
      containerStyle,
      append,
      shadow,
      width,
      marginHorizontal = scaleBaseOnScreenWidth(4),
      multiline,
      appendIcon,
      unitSize,
      appendIconColor,
      appendIconType,
      onPressAppend,
      appendIconSize,
      unit,
      editable = true,
      prependIconType,
      prependIconSize,
      prependIconColor,
      prependIcon,
      noborder,
      row,
      styleCustom,
    } = this.props;
    const isPassword = get(this.props, 'isPassword', false);
    const {showPassword} = this.state;
    return (
      <View
        pointerEvents={editable ? 'auto' : 'none'}
        style={[
          styles.containerStyle,
          {width},
          {marginHorizontal},
          multiline && styles.multiline,
          row && globalStyles.flex1,
          shadow ? styles.shadow : styles.bottomBorder,
          prependIcon && {paddingLeft: scaleBaseOnScreenWidth(38)},
          noborder && styles.noborder,
          containerStyle,
          // style,
          styleCustom,
          this.state?.isFocus && styles.focus,
        ]}>
        {prependIcon ? (
          <AppIcon
            color={prependIconColor || '#7a8691'}
            name={prependIcon}
            size={prependIconSize || scaleBaseOnScreenWidth(20)}
            style={[styles.prependIcon, multiline && styles.multilineIcon]}
            type={prependIconType}
          />
        ) : null}
        {this.label}
        {this.renderTextInput()}
        {isPassword && (
          <TouchableOpacity
            activeOpacity={1}
            style={styles.eyeStyle}
            onPressIn={this.showPassword}
            onPressOut={this.showPassword}>
            <AppIcon
              color={'#7a8691'}
              name={showPassword ? 'eye' : 'eye-with-line'}
              size={20}
              type="Entypo"
            />
          </TouchableOpacity>
        )}
        {unit ? (
          <Text
            numberOfLines={1}
            style={[styles.unit, unitSize && {fontSize: unitSize}]}>
            {unit}
          </Text>
        ) : null}
        {append}
        {!isEmpty(appendIcon) ? (
          <TouchableOpacity style={styles.eyeStyle} onPress={onPressAppend}>
            <AppIcon
              color={appendIconColor || '#7a8691'}
              name={appendIcon}
              size={appendIconSize || scaleBaseOnScreenWidth(20)}
              type={appendIconType}
            />
          </TouchableOpacity>
        ) : null}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  bottomBorder: {
    borderBottomWidth: 0.5,
    borderColor: COLORS.SMOKE,
    borderRadius: scaleBaseOnScreenHeight(0),
  },
  containerStyle: {
    marginTop: scaleBaseOnScreenHeight(2),
    // backgroundColor: COLORS.WHITE,
    marginHorizontal: scaleBaseOnScreenWidth(4),
    marginBottom: scaleBaseOnScreenHeight(8),
    height: scaleBaseOnScreenHeight(54),
    justifyContent: 'center',
    borderRadius: scaleBaseOnScreenHeight(27),
    paddingLeft: scaleBaseOnScreenWidth(4),
    paddingRight: scaleBaseOnScreenWidth(34),
    overflow: 'hidden',
  },
  disabled: {
    paddingTop: Platform.select({
      ios: scaleBaseOnScreenHeight(15),
      android: 0,
    }),
    textAlignVertical: 'center',
  },
  eyeStyle: {
    position: 'absolute',
    right: 35,
  },
  focus: {borderColor: COLORS.SECONDARY},
  inputStyle: {
    ...globalStyles.textField,
    textAlign: 'left',
    paddingLeft: 0,
    textAlignVertical: 'bottom',
    height: '100%',
    color: COLORS.GREY,
    marginTop: scaleBaseOnScreenHeight(20),
    fontWeight: 'bold',
    width: '90%',
  },
  label: {
    ...globalStyles.textCaption,
    color: COLORS.GREY,
    fontSize: scaleBaseOnScreenWidth(13),
    // fontWeight: 'bold',
  },
  labelContainer: {
    backgroundColor: 'transparent',
    height: scaleBaseOnScreenHeight(30),
    left: scaleBaseOnScreenWidth(4),
    position: 'absolute',
    right: 0,
    top: scaleBaseOnScreenHeight(0),
    zIndex: 2,
  },
  marginTop: {
    paddingTop: scaleBaseOnScreenHeight(20),
    ...globalStyles.textField,
  },
  multiline: {
    height: scaleBaseOnScreenHeight(95),
  },
  multilineIcon: {
    top: 10,
  },
  multilineInput: {
    paddingTop: scaleBaseOnScreenHeight(12),
    textAlignVertical: 'top',
  },
  noborder: {
    borderBottomWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  prependIcon: {
    bottom: 10,
    left: 10,
    position: 'absolute',
  },
  required: {
    color: COLORS.BITTER_GRAY,
  },
  shadow: {
    borderColor: COLORS.SILVER,
    borderRadius: 10,
    borderWidth: 2,
  },
  unit: {
    ...globalStyles.textCaption,
    alignSelf: 'center',
    bottom: 5,
    position: 'absolute',
    right: 5,
    zIndex: 3,
    // top: -5,
    //  bottom: scaleBaseOnScreenHeight(10),
  },
});
