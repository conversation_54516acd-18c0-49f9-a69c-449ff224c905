import {COLORS} from 'assets';
import {AppText, SizeBox} from 'elements';
import React from 'react';
import {TouchableOpacity, View, ViewStyle} from 'react-native';
import {FONT_TYPE, METRICS} from 'utils';

type AppButtonProps = {
  text: string;
  type?: 'primary' | 'secondary';
  textColor?: string;
  size?: number[];
  onPress?: () => void;
  onPressLeft?: () => void;
  onPressRight?: () => void;
  textRight?: string | undefined;
  customStyle?: ViewStyle;
};

const AppButton = ({
  text,
  textColor = COLORS.WHITE,
  textRight,
  type,
  size = [1, 2],
  onPress = () => {},
  onPressLeft = () => {},
  onPressRight = () => {},
  customStyle,
}: AppButtonProps) => {
  if (type === 'secondary') {
    const buttonStyle1: ViewStyle = {
      height: 50,
      backgroundColor: COLORS.MAIN_COLOR,
      borderTopRightRadius: 20,
      borderBottomRightRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
    };

    const buttonStyle2: ViewStyle = {
      height: 50,
      backgroundColor: COLORS.WHITE,
      borderWidth: 2,
      borderColor: COLORS.MAIN_COLOR,
      borderTopLeftRadius: 20,
      borderBottomLeftRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
    };
    return (
      <View style={{flexDirection: 'row', ...customStyle}}>
        <TouchableOpacity
          style={[buttonStyle2, {flex: size[0]}]}
          onPress={onPressLeft}>
          <AppText
            size={METRICS.text_12}
            color={COLORS.MAIN_COLOR}
            font={FONT_TYPE.BOLD}
            text={text}
          />
        </TouchableOpacity>
        <SizeBox w={METRICS.tiny_4} />
        <TouchableOpacity
          style={[buttonStyle1, {flex: size[1]}]}
          onPress={onPressRight}>
          <AppText
            color={textColor}
            font={FONT_TYPE.BOLD}
            text={textRight}
            size={METRICS.text_12}
          />
        </TouchableOpacity>
      </View>
    );
  }

  const buttonStyle: ViewStyle = {
    height: 50,
    backgroundColor: COLORS.MAIN_COLOR,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    ...customStyle,
  };

  return (
    <TouchableOpacity style={buttonStyle} onPress={onPress}>
      <AppText
        color={textColor}
        font={FONT_TYPE.BOLD}
        size={METRICS.text_12}
        text={text}
      />
    </TouchableOpacity>
  );
};

export default AppButton;
