import React from 'react';
import {ViewStyle} from 'react-native';
import FastImage, {ResizeMode, Source} from 'react-native-fast-image';

type AppImageProps = {
  src: number | Source | undefined;
  customStyle?: ViewStyle;
  resizeMode?: ResizeMode | undefined;
};

const AppImage = ({
  src,
  customStyle,
  resizeMode = FastImage.resizeMode.contain,
  ...props
}: AppImageProps) => {
  const appImageStyle: any = {
    ...customStyle,
  };
  return (
    <FastImage
      resizeMode={resizeMode}
      source={src}
      style={appImageStyle}
      {...props}
    />
  );
};

export default AppImage;
