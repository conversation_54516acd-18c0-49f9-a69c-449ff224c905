import COLORS from '../../assets/theme/colors';
import React from 'react';
import {GestureResponderEvent, ViewStyle} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import Feather from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Fontisto from 'react-native-vector-icons/Fontisto';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcon from 'react-native-vector-icons/MaterialIcons';

export const IconType = {
  fontisto: 'fontisto',
  material: 'material',
  fontawesome: 'fontawesome',
  feather: 'feather',
  ionicons: 'ionicons',
  entypo: 'entypo',
  antDesign: 'antDesign',
  // Add other icon types here
};

const IconComponents = {
  material: MaterialIcon,
  fontawesome: FontAwesome,
  feather: Feather,
  ionicons: Ionicons,
  entypo: Entypo,
  antDesign: AntDesign,
  fontisto: Fontisto,
  // Add other icon types here
};

type AppIcon = {
  type: string;
  name: string;
  size?: number;
  style?: ViewStyle;
  color?: string;
  onPress?: (event: GestureResponderEvent) => void;
};

const AppIcon = ({
  type = 'material',
  name = '',
  size = 12,
  style = {},
  color = COLORS.MAIN_COLOR,
  onPress,
}: AppIcon) => {
  const Icon =
    IconComponents[type as keyof typeof IconComponents] || MaterialIcon; // Fallback to MaterialIcon if type is not found
  return (
    <Icon
      color={color}
      name={name}
      size={size}
      style={style}
      onPress={onPress} // Update the type of onPress
    />
  );
};

export default AppIcon;
