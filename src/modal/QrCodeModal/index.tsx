import IMAGES from '../../assets/images';
import {AppButton, AppImage, SizeBox} from '../../elements';
import {METRICS} from '../../utils';
import React, {useEffect, useRef} from 'react';
import {Modalize} from 'react-native-modalize';

const QrCodeModal = ({onClosed, isOpen = false}) => {
  const modalizeRef = useRef<Modalize>(null);

  useEffect(() => {
    if (isOpen) {
      modalizeRef.current?.open();
    } else {
      modalizeRef.current?.close();
    }
  }, [isOpen]);

  return (
    <Modalize
      adjustToContentHeight={true}
      childrenStyle={{alignItems: 'center', paddingTop: METRICS.nomal}}
      ref={modalizeRef}
      onClosed={onClosed}>
      <AppImage
        customStyle={{width: 300, height: 300}}
        src={IMAGES.IMG_QR_CODE}
      />
      <SizeBox h={METRICS.nomal} />
      <AppButton text="Đã xong" />
      <SizeBox h={METRICS.nomal} />
    </Modalize>
  );
};

export default QrCodeModal;
