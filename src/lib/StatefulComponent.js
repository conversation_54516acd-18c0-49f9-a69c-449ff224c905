import {isEqual} from 'lodash';
import React from 'react';
import {InteractionManager} from 'react-native';

export default class StatefulComponent extends React.Component {
  state = {
    isFocus: false,
  };

  shouldComponentUpdate(nextProps, nextState) {
    if (this.unit !== this.userConfig?.unit) {
      this.unit = this.userConfig?.unit;
    }
    const rs =
      !isEqual(nextProps, this.props) || !isEqual(nextState, this.state);
    return rs;
  }

  onChangeState = name => value => {
    if (typeof name === 'string') {
      if (Math.abs(parseInt(value)) > 200000 && name === 'weight') {
        this.setState({[name]: '200000'});
        return;
      }
      this.setState({[name]: value});
    }
    if (Array.isArray(name)) {
      const newState = {};
      name.forEach(el => {
        newState[el] = value;
      });
      this.setState({
        ...newState,
      });
    }
  };

  preventMultipleInvoke = async action => {
    if (this.blocked) {
      return;
    }
    this.blocked = true;
    await action();
    InteractionManager.runAfterInteractions(async () => {
      this.blocked = false;
    });
  };

  toggleBooleanState =
    (name, value = undefined) =>
    () => {
      clearTimeout(this[name + 'Timeout']);
      this[name + 'Timeout'] = setTimeout(() => {
        this.setState(state => ({
          [name]: value !== undefined ? value : !state[name],
        }));
      }, 100);
    };

  delayRender = (duaration = 300) =>
    setTimeout(() => this.onChangeState('isFocus')(true), duaration);
}
