import AsyncStorage from '@react-native-async-storage/async-storage';
import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';

const initialState = {
  fishData: [],
};

const useFishStore = create(
  persist(
    (set, get) => ({
      fishData: initialState.fishData,
      setListFish: (listFish: any) => {
        set(state => ({
          ...state,
          fishData: listFish
            .filter((ele: any) => ele.isFavorite === true)
            ?.concat(listFish.filter((ele: any) => ele.isFavorite !== true)),
        }));
      },

      setFavorite: (item: any) => {
        set(state => {
          const _ls =
            state?.fishData?.map(ele => {
              if (ele._id === item._id) {
                return {...ele, isFavorite: item.isFavorite};
              }
              return ele;
            }) || [];
          return {
            ...state,
            fishData: _ls
              .filter((ele: any) => ele.isFavorite === true)
              ?.concat(_ls.filter((ele: any) => ele.isFavorite !== true)),
          };
        });
      },

      setFavoriteArr: (arrFishId: string[]) => {
        set(state => {
          const updatedFishData = (state.fishData || []).map(fish => ({
            ...fish,
            isFavorite: arrFishId.includes(fish._id),
          }));
          // Sắp xếp: isFavorite true lên trước
          updatedFishData.sort((a, b) => {
            if (a.isFavorite === b.isFavorite) {
              return 0;
            }
            return a.isFavorite ? -1 : 1;
          });

          return {
            ...state,
            fishData: updatedFishData,
          };
        });
      },
      getFish: user_id => {
        // AsyncStorage.getItem('fish-store').then(data => {});
        // useFishStore.persist.setOptions({
        //   name: 'fish-' + user_id,
        // });
        // useFishStore.persist.rehydrate();
        // to rehydrate the zustand store using the new name
        // AsyncStorage.getItem('fish-' + user_id).then(data => {
        AsyncStorage.getItem('fish-store')
          .then(data => {
            const json = JSON.parse(data);
            set(state => ({...json.state}));
          })
          .catch(err => console.log(err));
        //
      },
    }),
    {
      name: 'fish-store', // unique name
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);

export default useFishStore;
