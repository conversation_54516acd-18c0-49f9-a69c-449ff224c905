import {configDiarySlice, ConfigDiarySlice} from './configDiarySlice';
import {currentDiarySlice, CurrentDiarySlice} from './currentDiarySlice';
import {diaryHistorySlice, DiaryHistorySlice} from './diaryHistorySlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {DiaryState} from 'types';
import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';

const initialState: DiaryState = {
  lastDiary: {
    id_tau_ca: null,
    so_dang_ky: null,
    thoi_gian_boc_do: null,
    thoi_gian_cap_cang: null,
    thoi_gian_xuat_cang: null,
    tinh_trang: null,
    start_diary: false,
  },
  diaryCurrent: {
    thoi_gian_tha: null,
    thoi_gian_thu: null,
    vi_tri_tha: null,
    vi_tri_thu: null,
    trang_thai: 0,
    san_luong: [],
  },
  diaryLocal: [],
  diaryServer: [],
  diaryDelete: [],
  configQuickDiary: {
    autoTp: 0,
    listFish: [],
    dateDiary: {
      from: null,
      to: null,
    },
    autoDay: {
      timeDiary: {
        from: {
          start: null,
          end: null,
        },
        to: {
          start: null,
          end: null,
        },
      },
      numDayOnDiary: 1,
    },
    autoHours: {
      waitTime: {
        from: null,
        to: null,
      },
      releaseTime: {
        from: null,
        to: null,
      },
      retrieveTime: {
        from: null,
        to: null,
      },
      processTime: {
        from: null,
        to: null,
      },
    },
  },
};

type DiaryStore = {
  diary: DiaryState;
} & CurrentDiarySlice &
  DiaryHistorySlice &
  ConfigDiarySlice;

const useDiaryStore = create<DiaryStore>()(
  persist(
    (...a) => ({
      diary: initialState,
      ...currentDiarySlice(...a),
      ...diaryHistorySlice(...a),
      ...configDiarySlice(...a),
    }),
    {
      name: 'diary-store',
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);

export default useDiaryStore;
