import {DiaryEntry, DiaryState} from 'types';
import {StateCreator} from 'zustand';

export interface CurrentDiarySlice {
  setDiary: (diary: Partial<DiaryEntry>) => void;
  clearDiary: () => void;
}

const initialDiaryCurrent: DiaryEntry = {
  thoi_gian_tha: null,
  thoi_gian_thu: null,
  vi_tri_tha: null,
  vi_tri_thu: null,
  trang_thai: 0,
  san_luong: [],
};

export const currentDiarySlice: StateCreator<
  DiaryState & CurrentDiarySlice,
  [],
  [],
  CurrentDiarySlice
> = set => ({
  setDiary: (diary: Partial<DiaryEntry>) => {
    set(state => ({
      ...state,
      diary: {
        ...state.diary,
        diaryCurrent: {
          ...initialDiaryCurrent,
          ...diary,
        },
      },
    }));
  },

  clearDiary: () => {
    set(state => ({
      ...state,
      diary: {
        ...state.diary,
        diaryCurrent: {...initialDiaryCurrent},
      },
    }));
  },
});
