import {DiaryConfig, DiaryState} from 'types';
import {StateCreator} from 'zustand';

export interface ConfigDiarySlice {
  setConfigQuickDiary: (config: Partial<DiaryConfig>) => void;
  resetConfigQuickDiary: () => void;
  setLastDiary: (lastDiary: any) => void;
  onClearAll: () => void;
}

const initialConfigQuickDiary: DiaryConfig = {
  autoTp: 0,
  listFish: [],
  dateDiary: {
    from: null,
    to: null,
  },
  autoDay: {
    timeDiary: {
      from: {
        start: null,
        end: null,
      },
      to: {
        start: null,
        end: null,
      },
    },
    numDayOnDiary: 1,
  },
  autoHours: {
    waitTime: {
      from: null,
      to: null,
    },
    releaseTime: {
      from: null,
      to: null,
    },
    retrieveTime: {
      from: null,
      to: null,
    },
    processTime: {
      from: null,
      to: null,
    },
  },
};

const initialLastDiary = {
  id_tau_ca: null,
  so_dang_ky: null,
  thoi_gian_boc_do: null,
  thoi_gian_cap_cang: null,
  thoi_gian_xuat_cang: null,
  tinh_trang: null,
  start_diary: false,
};

export const configDiarySlice: StateCreator<
  DiaryState & ConfigDiarySlice,
  [],
  [],
  ConfigDiarySlice
> = set => ({
  setConfigQuickDiary: (config: Partial<DiaryConfig>) => {
    set(state => ({
      ...state,
      diary: {
        ...state.diary,
        configQuickDiary: {
          ...state.diary.configQuickDiary,
          ...config,
        },
      },
    }));
  },

  resetConfigQuickDiary: () => {
    set(state => ({
      ...state,
      diary: {
        ...state.diary,
        configQuickDiary: initialConfigQuickDiary,
      },
    }));
  },

  setLastDiary: (lastDiary: any) => {
    set(state => ({
      ...state,
      diary: {
        ...state.diary,
        lastDiary: {
          ...state.diary.lastDiary,
          ...lastDiary,
        },
      },
    }));
  },

  onClearAll: () => {
    set(state => ({
      ...state,
      diary: {
        lastDiary: initialLastDiary,
        diaryCurrent: {
          thoi_gian_tha: null,
          thoi_gian_thu: null,
          vi_tri_tha: null,
          vi_tri_thu: null,
          trang_thai: 0,
          san_luong: [],
        },
        diaryLocal: [],
        diaryServer: [],
        diaryDelete: [],
        configQuickDiary: initialConfigQuickDiary,
      },
    }));
  },
});
