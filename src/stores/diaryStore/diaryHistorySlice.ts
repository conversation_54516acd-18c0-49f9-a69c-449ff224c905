import AsyncStorage from '@react-native-async-storage/async-storage';
import {DiaryEntry, DiaryState, User} from 'types';
import {StateCreator} from 'zustand';

export interface DiaryHistorySlice {
  setDiaryLocal: (list: DiaryEntry[]) => void;
  setDiaryServer: (list: DiaryEntry[]) => void;
  setDiaryDelete: (list: DiaryEntry[]) => void;
  clearDiaryDelete: () => void;
  getDiary: (user_id: string) => void;
  fetchDiary: (
    data: any,
    type: boolean,
    user: User,
    clearOld?: boolean,
  ) => Promise<void>;
}

export const diaryHistorySlice: StateCreator<
  {diary: DiaryState} & DiaryHistorySlice,
  [],
  [],
  DiaryHistorySlice
> = (set, get) => ({
  setDiaryLocal: (list: DiaryEntry[]) => {
    set((state: any) => ({
      ...state,
      diary: {
        ...state.diary,
        diaryLocal: list,
      },
    }));
  },

  setDiaryServer: (list: DiaryEntry[]) => {
    set((state: any) => ({
      ...state,
      diary: {
        ...state.diary,
        diaryServer: list,
      },
    }));
  },

  setDiaryDelete: (list: DiaryEntry[]) => {
    set((state: any) => ({
      ...state,
      diary: {
        ...state.diary,
        diaryDelete: list,
      },
    }));
  },

  clearDiaryDelete: () => {
    set((state: any) => ({
      ...state,
      diary: {
        ...state.diary,
        diaryDelete: [],
      },
    }));
  },

  getDiary: (user_id: string) => {
    // @ts-ignore - Need to access persist methods
    get().persist.setOptions({
      name: 'diary-' + user_id,
    });
    // @ts-ignore - Need to access persist methods
    get().persist.rehydrate();

    AsyncStorage.getItem('diary-' + user_id)
      .then(data => {
        if (data) {
          const json = JSON.parse(data);
          set(() => ({...json.state}));
        }
      })
      .catch(err => console.log(err));
  },

  fetchDiary: async (data, type, _user, _clearOld = false) => {
    // const {_id, id_tau_ca} = user;
    const {diary} = get();
    const {diaryLocal} = diary;

    try {
      const FishStoreData = await AsyncStorage.getItem('fish-store');
      if (!FishStoreData) {
        return;
      }

      JSON.parse(FishStoreData);
      // const {fishData} = FishStore.state;

      // const thoi_gian_xuat_cang = lastDiary?.thoi_gian_xuat_cang ?? null;
      // const arrIdDelete = diaryDelete.map((ele: any) => ele?._id) ?? [];

      // Dữ liệu server có trạng_thai = 0
      const ls_state_0 =
        data?.data
          ?.filter((ele: any) => ele.trang_thai === 0)
          .map((ele: any) => ({
            ...ele,
            trang_thai: 3,
            san_luong: ele.san_luong?.map((ele1: any) => ({
              ...ele1,
              id: ele1.id_nhom_loai,
              _id: ele1.id_nhom_loai,
            })),
          })) ?? [];

      // Dữ liệu server có trạng_thai != 0
      const ls_state_1234 =
        data?.data
          ?.filter((ele: any) => ele.trang_thai !== 0)
          ?.map((ele: any) => ({
            ...ele,
            san_luong: ele.san_luong?.map((ele1: any) => ({
              ...ele1,
              id: ele1.id_nhom_loai,
              _id: ele1.id_nhom_loai,
            })),
          })) ?? [];

      // Dữ liệu server
      const listdata_server =
        (ls_state_0.length > 0
          ? ls_state_0.concat(ls_state_1234)
          : data?.data) ?? [];

      // Dữ liệu local chưa gửi server
      let _diaryLocal =
        type == true
          ? diaryLocal?.filter((ele: any) => ele?.is_online == true)
          : diaryLocal;

      // Cập nhật state
      set(state => ({
        ...state,
        diary: {
          ...state.diary,
          diaryServer: listdata_server,
          diaryLocal: _diaryLocal,
        },
      }));
    } catch (error) {
      console.error('Error in fetchDiary:', error);
    }
  },
});
