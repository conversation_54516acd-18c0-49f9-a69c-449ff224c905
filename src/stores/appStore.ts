import {AppState} from 'types';
import {create} from 'zustand';

type AppActions = {
  showLoading: (status: boolean) => Promise<void>;
  hideLoading: () => Promise<void>;
};

const initialState: AppState = {
  loading: false,
};

const useAppStore = create<AppState & AppActions>(set => ({
  loading: initialState.loading,

  showLoading: async (status: boolean) => {
    set(state => ({...state, loading: status}));
  },

  hideLoading: async () => {
    set(state => ({...state, loading: false}));
  },
}));

export default useAppStore;
