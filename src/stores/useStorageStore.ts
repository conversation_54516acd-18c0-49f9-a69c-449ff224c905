import useDiaryStore from './diaryStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Credential, User} from 'types';
import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';

interface AccountCredentials {
  username: string;
  password: string;
  province?: string;
}

interface StorageState {
  credential: {
    token: string;
    user: Partial<User>;
    account: AccountCredentials;
  };
  error: string;
}

interface StorageActions {
  setCredential: (
    token: string,
    user: User,
    account?: AccountCredentials,
  ) => void;
  getCredential: () => Promise<string | null>;
  onClearnStorage: () => void;
}

const initialState: StorageState = {
  credential: {
    token: ' ',
    user: {},
    account: {
      username: '',
      password: '',
      province: '',
    },
  },
  error: '',
};

const useStorageStore = create<StorageState & StorageActions>(
  persist(
    (set, get) => ({
      credential: initialState.credential,
      error: initialState.error,

      setCredential: (
        token: string,
        user: User,
        account?: AccountCredentials,
      ) => {
        useDiaryStore.persist.setOptions({
          name: 'diary-' + user._id,
        });
        // to rehydrate the zustand store using the new name
        useDiaryStore.persist.rehydrate();
        set(state => ({
          ...state,
          credential: {
            token,
            user,
            account: account || {username: '', password: '', province: ''},
          },
        }));
      },

      getCredential: async () => {
        return await AsyncStorage.getItem('credential-store');
      },

      onClearnStorage: () => {
        set(state => ({
          ...state,
          credential: {...state?.credential, token: null as unknown as string},
        }));
      },
    }),
    {
      name: 'credential-store', // unique name
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);

export default useStorageStore;
