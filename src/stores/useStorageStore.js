import useDiaryStore from './diaryStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';

const initialState = {
  credential: {
    token: ' ',
    user: {},
    account: {
      username: '',
      password: '',
    },
  },
  error: '',
};

const useStorageStore = create(
  persist(
    (set, get) => ({
      credential: initialState.credential,
      setCredential: (token, user, account) => {
        useDiaryStore.persist.setOptions({
          name: 'diary-' + user._id,
        });
        // to rehydrate the zustand store using the new name
        useDiaryStore.persist.rehydrate();
        set(state => ({
          ...state,
          credential: {
            token,
            user,
            account,
          },
        }));
      },
      getCredential: async () => {
        return await AsyncStorage.getItem('credential-store');
      },
      onClearnStorage: () => {
        set(state => ({
          ...state,
          credential: {...state?.credential, token: null},
        }));
      },
    }),
    {
      name: 'credential-store', // unique name
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);

export default useStorageStore;
