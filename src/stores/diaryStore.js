import AsyncStorage from '@react-native-async-storage/async-storage';
import {isEmpty} from 'lodash';
import moment from 'moment';
import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';

const initialState = {
  diaryInit: {
    lastDiary: {
      id_tau_ca: null,
      so_dang_ky: null,
      thoi_gian_boc_do: null,
      thoi_gian_cap_cang: null,
      thoi_gian_xuat_cang: null,
      tinh_trang: null,
      start_diary: false,
    },
    diaryCurrent: {
      thoi_gian_tha: null,
      thoi_gian_thu: null,
      vi_tri_tha: null,
      vi_tri_thu: null,
      trang_thai: 0,
      san_luong: [],
    },
    diaryLocal: [], // trang_thai :[0, 1, 2]
    diaryServer: [], // trang_thai :[3]};
    diaryDelete: [],
    configQuickDiary: {
      autoTp: 0,
      listFish: [],
      dateDiary: {
        from: null,
        to: null,
      },
      autoDay: {
        timeDiary: {
          from: {
            start: null,
            end: null,
          },
          to: {
            start: null,
            end: null,
          },
        },
        numDayOnDiary: 1,
      },
      autoHours: {
        waitTime: {
          from: null,
          to: null,
        },
        releaseTime: {
          from: null,
          to: null,
        },
        retrieveTime: {
          from: null,
          to: null,
        },
        processTime: {
          from: null,
          to: null,
        },
      },
    },
  },
  error: '',
};
const useDiaryStore = create(
  persist(
    (set, get) => ({
      diary: initialState.diaryInit,
      setDiary: (diary: any) => {
        set(state => ({
          ...state,
          diary: {
            ...state.diary,
            diaryCurrent: {
              ...initialState.diaryInit.diaryCurrent,
              // ...state.diary.diaryCurrent,
              ...diary,
            },
          },
        }));
      },
      setDiaryServer: (list: any) => {
        set(state => ({
          ...state,
          diary: {
            ...state.diary,
            diaryServer: list,
          },
        }));
      },
      setDiaryLocal: (list: any) => {
        set(state => ({
          ...state,
          diary: {
            ...state.diary,
            diaryCurrent: {...initialState.diaryInit?.diaryCurrent},
            diaryLocal: list,
          },
        }));
      },
      getDiary: (user_id: string) => {
        useDiaryStore.persist.setOptions({
          name: 'diary-' + user_id,
        });
        useDiaryStore.persist.rehydrate();
        // to rehydrate the zustand store using the new name
        AsyncStorage.getItem('diary-' + user_id)
          .then(data => {
            const json = JSON.parse(data);
            set(state => ({...json.state}));
          })
          .catch(err => console.log(err));
      },
      clearDiary: () => {
        set(state => ({
          ...state,
          diary: {
            ...state.diary,
            diaryCurrent: {...initialState.diaryInit?.diaryCurrent},
          },
        }));
      },
      onClearAll: () => {
        set(state => ({
          ...state,
          diary: initialState.diaryInit,
        }));
      },
      // thêm mới
      setDiaryDelete: (list: any) => {
        set(state => ({
          ...state,
          diary: {
            ...state.diary,
            diaryDelete: list,
          },
        }));
      },
      // thêm mới
      clearDiaryDelete: () => {
        set(state => ({
          ...state,
          diary: {
            ...state.diary,
            diaryDelete: [],
          },
        }));
      },
      /**
       *
       * @param {*} data : danh sách response trả về
       * @param {*} type: true lấy online  -> false: lấy hiện tại
       * @param {*} user: lấy mã tàu để quản lý
       * @param {*} clearOld: xoá danh sách cũ
       */
      fetchDiary: async (data, type, user, clearOld = false) => {
        // const {id_tau_ca} = user;
        const FishStore = JSON.parse(await AsyncStorage.getItem('fish-store'));
        const {fishData} = FishStore.state;
        const {diaryLocal, diaryCurrent, diaryDelete, lastDiary} = get().diary;
        const thoi_gian_xuat_cang = lastDiary?.thoi_gian_xuat_cang ?? null;
        const arrIdDelete = diaryDelete.map((ele: any) => ele?._id) ?? [];
        // dư liệu server có trạng_thai = 0
        const ls_state_0 =
          data?.data
            ?.filter((ele: any) => ele.trang_thai === 0)
            .map((ele: any) => ({
              ...ele,
              trang_thai: 3,
              san_luong: ele.san_luong?.map(ele1 => ({
                ...ele1,
                id: ele1.id_nhom_loai,
                _id: ele1.id_nhom_loai,
              })),
            })) ?? [];
        // dư liệu server có trạng_thai != 0
        const ls_state_1234 =
          data?.data
            ?.filter((ele: any) => ele.trang_thai != 0)
            ?.map((ele: any) => ({
              ...ele,
              san_luong: ele.san_luong?.map(ele1 => ({
                ...ele1,
                id: ele1.id_nhom_loai,
                _id: ele1.id_nhom_loai,
              })),
            })) ?? [];
        // dữ liệu server
        const listdata_server =
          (ls_state_0.length > 0
            ? ls_state_0.concat(ls_state_1234)
            : data?.data) ?? [];
        // dữ liệu local chưa gửi server
        let _diaryLocal =
          type === true
            ? diaryLocal?.filter((ele: any) => ele?.is_online === true)
            : diaryLocal;
        const notsync =
          _diaryLocal?.filter((ele: any) => ele?.item_id || ele?.id_item) ?? [];

        // dữ liệu đã gửi server | Nếu trường hợp chỉnh sửa record is_online = true => chuyển sang is_online = false
        const arrIdsynced =
          listdata_server
            ?.map((ele: any) => ele._id)
            ?.filter((ele: any) => ele) ?? [];
        const arrEdit =
          notsync?.filter((ele: any) => arrIdsynced?.includes(ele?._id)) ?? [];
        // console.log('listdata_server: ', listdata_server.length);
        // console.log('diaryLocal: ', diaryLocal.length);
        // console.log('_diaryLocal: ', _diaryLocal.length);
        // console.log('notsync: ', notsync.length);
        // console.log('arrEdit: ', arrEdit.length);

        if (arrEdit.length > 0) {
          // TH2: bỏ qua record của listdata_server nếu trùng trong notsync  xong rồi merge lại
          const arrIdEdit = arrEdit?.map((ele: any) => ele?._id) ?? [];
          const arr_remove_eidt =
            listdata_server
              ?.filter((ele: any) => !arrIdEdit?.includes(ele?._id))
              ?.sort(
                (a: any, b: any) =>
                  new Date(a?.thoi_gian_tha) - new Date(b?.thoi_gian_tha),
              ) ?? [];

          let arrMapFish = notsync
            ?.concat(arr_remove_eidt)
            ?.map((ele: any) => ({...ele, is_online: !isEmpty(ele?._id)}))
            ?.sort(
              (a: any, b: any) =>
                new Date(a?.thoi_gian_tha) - new Date(b?.thoi_gian_tha),
            );
          // ?.filter((ele:any)=> ele?._id && !arrIdDelete?.includes(ele?._id) )

          if (arrIdDelete?.length > 0) {
            arrMapFish = arrMapFish?.filter(
              (ele: any) => ele?._id && !arrIdDelete?.includes(ele?._id),
            );
          }

          if (clearOld === true && thoi_gian_xuat_cang) {
            arrMapFish = arrMapFish?.filter(
              (ele: any) =>
                !moment(thoi_gian_xuat_cang).isAfter(ele?.thoi_gian_tha),
            );
          }
          const final = arrMapFish.map((ele: any) => {
            const san_luong = ele?.san_luong?.map((ele1: any) => {
              return {
                ...ele1,
                ten_dia_phuong:
                  fishData?.find((ele2: any) => ele2._id === ele1?._id)
                    ?.ten_dia_phuong || ele1?.ten,
              };
            });
            ele.san_luong = san_luong;
            return ele;
          });

          set(state => ({
            ...state,
            diary: {
              ...state.diary,
              diaryCurrent:
                diaryCurrent?.thoi_gian_tha != null
                  ? diaryCurrent
                  : {...initialState.diaryInit?.diaryCurrent},
              diaryLocal: final,
            },
          }));
          // setDiaryLocal(final)
        } else {
          // TH1: final = notsync + listdata_server
          let arrMapFish = notsync
            ?.concat(listdata_server)
            ?.map((ele: any) => ({...ele, is_online: !isEmpty(ele?._id)}))
            ?.sort(
              (a: any, b: any) =>
                new Date(a?.thoi_gian_tha) - new Date(b?.thoi_gian_tha),
            );
          // ?.filter((ele:any)=> ele?._id && !arrIdDelete?.includes(ele?._id) )

          if (arrIdDelete?.length > 0) {
            arrMapFish = arrMapFish?.filter(
              (ele: any) => ele?._id && !arrIdDelete?.includes(ele?._id),
            );
          }
          if (clearOld.toString() === 'true') {
            arrMapFish = arrMapFish?.filter(
              (ele: any) =>
                !moment(thoi_gian_xuat_cang).isAfter(ele?.thoi_gian_tha),
            );
          }

          const final = arrMapFish?.map((ele: any) => {
            const san_luong = ele?.san_luong?.map((ele1: any) => {
              return {
                ...ele1,
                ten_dia_phuong:
                  fishData?.find((ele2: any) => ele2._id === ele1?._id)
                    ?.ten_dia_phuong || ele1?.ten,
              };
            });
            ele.san_luong = san_luong;
            return ele;
          });

          set(state => ({
            ...state,
            diary: {
              ...state.diary,
              diaryCurrent:
                diaryCurrent?.thoi_gian_tha != null
                  ? diaryCurrent
                  : {...initialState.diaryInit?.diaryCurrent},
              diaryLocal: final,
            },
          }));
        }
      },
      // lưu thời gian xuất chuyến
      setLastDiary: data => {
        set(state => ({
          ...state,
          diary: {
            ...state.diary,
            lastDiary: data,
          },
        }));
      },
      // thêm cấu hình tạo nhanh chuyến biển
      setConfigQuickDiary: data => {
        set(state => ({
          ...state,
          diary: {
            ...state.diary,
            configQuickDiary: {
              ...state.diary.configQuickDiary,
              ...data,
            },
          },
        }));
      },
      setConfigQuickListFish: data => {
        set(state => ({
          ...state,
          diary: {
            ...state.diary,
            configQuickDiary: {
              ...state.diary.configQuickDiary,
              listFish: data,
            },
          },
        }));
      },
    }),
    {
      name: 'diary-store', // unique name
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);

export default useDiaryStore;
