import React, {useState} from 'react';
import {View, Text, TouchableOpacity, Modal, StyleSheet} from 'react-native';
import DatePicker from 'react-native-date-picker';
import {Button} from 'react-native-elements';

const CalendarModal = ({visible, onClose, onConfirm}) => {
  const [fromDate, setFromDate] = useState(new Date());
  const [toDate, setToDate] = useState(new Date());
  const [openFrom, setOpenFrom] = useState(false);
  const [openTo, setOpenTo] = useState(false);

  return (
    <Modal visible={visible} transparent animationType="slide">
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.title}>📅 Chọn khoảng thời gian</Text>

          {/* Chọn Ngày + Giờ Bắt Đầu */}
          <TouchableOpacity
            onPress={() => setOpenFrom(true)}
            style={styles.dateButton}>
            <Text>🕒 Từ: {fromDate.toLocaleString()}</Text>
          </TouchableOpacity>
          <DatePicker
            modal
            open={openFrom}
            date={fromDate}
            mode="datetime" // Chọn ngày + giờ
            minimumDate={new Date()} // Không cho chọn ngày quá khứ
            onConfirm={(date: any) => {
              setFromDate(date);
              setOpenFrom(false);
            }}
            onCancel={() => setOpenFrom(false)}
          />

          {/* Chọn Ngày + Giờ Kết Thúc */}
          <TouchableOpacity
            onPress={() => setOpenTo(true)}
            style={styles.dateButton}>
            <Text>🕒 Đến: {toDate.toLocaleString()}</Text>
          </TouchableOpacity>
          <DatePicker
            modal
            open={openTo}
            date={toDate}
            mode="datetime"
            minimumDate={fromDate} // Chỉ cho phép chọn từ ngày bắt đầu trở đi
            onConfirm={(date: any) => {
              setToDate(date);
              setOpenTo(false);
            }}
            onCancel={() => setOpenTo(false)}
          />

          {/* Nút Xác Nhận & Hủy */}
          <View style={styles.buttonContainer}>
            <Button
              title="❌ Hủy"
              type="outline"
              onPress={onClose}
              buttonStyle={styles.cancelButton}
            />
            <Button
              title="✅ Xác nhận"
              onPress={() => onConfirm(fromDate, toDate)}
              buttonStyle={styles.confirmButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: 320,
    backgroundColor: '#FFF',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 5,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  dateButton: {
    padding: 10,
    backgroundColor: '#f0f0f0',
    marginVertical: 10,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 20,
  },
  cancelButton: {
    backgroundColor: '#FF5C5C',
  },
  confirmButton: {
    backgroundColor: '#4CAF50',
  },
});

export default CalendarModal;
