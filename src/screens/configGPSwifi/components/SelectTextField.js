import Icon from './Icon';
import {colors} from 'assets';
import {StatefulComponent} from 'lib';
import {isEmpty} from 'lodash';
import moment from 'moment';
import React from 'react';
import {
  Animated,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import {scaleBaseOnScreenWidth, scaleBaseOnScreenHeight} from 'utils';
import {globalStyles} from 'utils/index';

export default class SelectTextField extends StatefulComponent {
  static defaultProps = {
    lightLabel: false,
    required: false,
    shadow: false,
    labelFontSize: scaleBaseOnScreenWidth(13),
    fontSize: scaleBaseOnScreenWidth(15),
  };

  state = {
    showPassword: false,
  };

  get inputStyle() {
    const {value, placeholderText, fontSize} = this.props;
    return [
      styles.inputStyle,
      !isEmpty(value) && isEmpty(placeholderText) && styles.marginTop,
      {fontSize},
    ];
  }

  animation = new Animated.Value(0);

  doActive = () => {
    const {value} = this.props;
    const isActive = !isEmpty(value);
    Animated.timing(this.animation, {
      toValue: scaleBaseOnScreenHeight(isActive ? 0 : 20),
      useNativeDriver: true,
      duration: 200,
    }).start();
  };

  componentDidMount() {
    this.doActive();
  }

  componentDidUpdate(prevProps) {
    if (isEmpty(this.props?.value) !== isEmpty(prevProps?.value)) {
      this.doActive();
    }
  }

  get label() {
    const {placeholderText, required, labelFontSize, prependIcon} = this.props;
    return (
      <Animated.View
        pointerEvents="none"
        style={[
          styles.labelContainer,
          prependIcon && {left: scaleBaseOnScreenWidth(38)},
          {
            transform: [
              {
                translateY: this.animation,
              },
            ],
          },
        ]}>
        <Text style={[styles.label, {fontSize: labelFontSize}]}>
          {placeholderText}
          {required && <Text style={styles.required}>{' (❊)'}</Text>}
        </Text>
      </Animated.View>
    );
  }

  get append() {
    const {
      iconName,
      iconType,
      iconSize,
      iconColor,
      append = (
        <Icon
          name={iconName || 'select-arrows'}
          type={iconType || 'Entypo'}
          style={styles.append}
          size={iconSize || scaleBaseOnScreenWidth(15)}
          color={iconColor || colors.GREY}
        />
      ),
    } = this.props;
    if (this.props.disabled) {
      return null;
    }
    if (this.props?.clearable && !isEmpty(this.props.value)) {
      return (
        <Icon
          onPress={this.props.onClear}
          name={'closecircle'}
          type={'AntDesign'}
          style={styles.append}
          size={scaleBaseOnScreenWidth(15)}
          color={colors.GREY}
        />
      );
    }
    return append;
  }

  render() {
    const {
      containerStyle,

      value,
      style,
      width,
      onPress,
      appendIcon,
      onPressAppend,
      appendIconType,
      shadow,
      disabled,
      prependIcon,
      prependIconType,
      prependIconSize,
      editable = true,
      appendIconSize,
      prependIconColor,
      area,
      unit,
      isDate,
    } = this.props;
    return (
      <TouchableOpacity
        disabled={disabled || !editable}
        onPress={onPress}
        style={[
          styles.containerStyle,
          {width},
          containerStyle,
          shadow ? styles.shadow : styles.bottomBorder,
          style,
          containerStyle,
          area && styles.ereaContainer,
          disabled && (this.props?.disabledStyle || {}),
          prependIcon && {paddingLeft: scaleBaseOnScreenWidth(38)},
        ]}>
        {prependIcon ? (
          <Icon
            type={prependIconType}
            name={prependIcon}
            size={prependIconSize || scaleBaseOnScreenWidth(20)}
            color={prependIconColor || '#7a8691'}
            style={styles.prependIcon}
          />
        ) : null}
        {this.label}
        {isDate && value ? (
          <Text
            numberOfLines={area ? 2 : 1}
            style={[this.inputStyle, {fontSize: scaleBaseOnScreenWidth(14)}]}>
            <Text style={{fontSize: scaleBaseOnScreenWidth(20)}}>
              {moment(value, 'HH:mm:SS - DD/MM/YYYY').format('HH:mm')}
            </Text>{' '}
            {moment(value, 'HH:mm:SS - DD/MM/YYYY').format('- DD/MM/YYYY')}
          </Text>
        ) : (
          <Text numberOfLines={area ? 2 : 1} style={this.inputStyle}>
            {value}
          </Text>
        )}

        {editable && isEmpty(appendIcon) ? this.append : null}
        {unit ? <Text style={styles.unit}>{unit}</Text> : null}
        {!isDate && !isEmpty(appendIcon) ? (
          <TouchableOpacity onPress={onPressAppend} style={styles.append}>
            <Icon
              type={appendIconType}
              name={appendIcon}
              size={appendIconSize || 20}
              color={'#7a8691'}
            />
          </TouchableOpacity>
        ) : null}
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  shadow: {
    borderColor: colors.SILVER,
    borderWidth: 2,
  },
  bottomBorder: {
    borderBottomWidth: 0.5,
    borderRadius: scaleBaseOnScreenHeight(0),
    borderColor: colors.SILVER,
  },
  containerStyle: {
    // backgroundColor: colors.WHITE,
    marginHorizontal: scaleBaseOnScreenWidth(4),
    marginBottom: scaleBaseOnScreenHeight(8),
    marginTop: scaleBaseOnScreenHeight(2),
    height: scaleBaseOnScreenHeight(54),
    justifyContent: 'center',
    borderRadius: scaleBaseOnScreenHeight(27),
    paddingLeft: scaleBaseOnScreenWidth(4),
    paddingRight: scaleBaseOnScreenWidth(34),
    overflow: 'hidden',
  },
  ereaContainer: {
    height: scaleBaseOnScreenHeight(78),
  },
  label: {
    ...globalStyles.textCaption,
    color: colors.GREY,
    fontSize: scaleBaseOnScreenWidth(13),
  },
  labelContainer: {
    position: 'absolute',
    right: scaleBaseOnScreenWidth(34),
    zIndex: 2,
    top: scaleBaseOnScreenHeight(-2),
    height: scaleBaseOnScreenHeight(30),
    left: scaleBaseOnScreenWidth(4),
    backgroundColor: 'transparent',
  },
  inputStyle: {
    ...globalStyles.textField,
    paddingLeft: 0,
    color: colors.BLACK,
    textAlignVertical: 'bottom',
    marginTop: Platform.select({
      ios: scaleBaseOnScreenHeight(20),
      android: scaleBaseOnScreenHeight(24),
    }),
    // flex: 1,
  },
  marginTop: {
    paddingTop: scaleBaseOnScreenHeight(22),
  },
  append: {
    position: 'absolute',
    right: 15,
  },
  unit: {
    ...globalStyles.textCaption,
    position: 'absolute',
    right: 25,
  },
  required: {
    color: colors.BITTER_GREY,
  },
  prependIcon: {
    position: 'absolute',
    left: 10,
    bottom: 10,
  },
});
