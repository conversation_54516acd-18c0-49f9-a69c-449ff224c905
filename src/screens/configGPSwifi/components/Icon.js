import {StatefulComponent} from 'lib';
import {get} from 'lodash';
import React from 'react';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Feather from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Fontisto from 'react-native-vector-icons/Fontisto';
import Foundation from 'react-native-vector-icons/Foundation';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Octicons from 'react-native-vector-icons/Octicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import Zocial from 'react-native-vector-icons/Zocial';

export default class VectorIcon extends StatefulComponent {
  get Icon() {}

  render() {
    const iconType = get(this.props, 'type', 'Ionicons');
    switch (iconType) {
      case 'AntDesign':
        return <AntDesign {...this.props} />;
      case 'Entypo':
        return <Entypo {...this.props} />;
      case 'EvilIcons':
        return <EvilIcons {...this.props} />;
      case 'Feather':
        return <Feather {...this.props} />;
      case 'FontAwesome':
        return <FontAwesome {...this.props} />;
      case 'FontAwesome5':
        return <FontAwesome5 {...this.props} />;
      case 'Foundation':
        return <Foundation {...this.props} />;
      case 'Ionicons':
        return <Ionicons {...this.props} />;
      case 'MaterialCommunityIcons':
        return <MaterialCommunityIcons {...this.props} />;
      case 'MaterialIcons':
        return <MaterialIcons {...this.props} />;
      case 'Octicons':
        return <Octicons {...this.props} />;
      case 'SimpleLineIcons':
        return <SimpleLineIcons {...this.props} />;
      case 'Zocial':
        return <Zocial {...this.props} />;
      case 'Fontisto':
        return <Fontisto {...this.props} />;
      default:
        return <Ionicons {...this.props} />;
    }
  }
}
