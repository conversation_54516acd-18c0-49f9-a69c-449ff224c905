import CalendarModal from './CalendarViewModal';
import {useNavigation} from '@react-navigation/native';
import {COLORS} from 'assets';
import {DelayRenderComponent} from 'components';
import Header from 'components/Header';
import {AppButton, AppIcon, AppText, BaseScreen, FlexView, SizeBox} from 'elements';
import moment from 'moment';
import SCREENS from 'navigation/Screens';
import React, {useState} from 'react';
import {View, StyleSheet, TouchableOpacity, Alert, Dimensions, Text} from 'react-native';
// import {Dropdown} from 'react-native-element-dropdown';
import {ScrollView} from 'react-native-gesture-handler';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {SelectFishRow} from 'screens/catchDiary/components/SelectFishRow';
import useDiaryStore from 'stores/diaryStore';
import useStorageStore from 'stores/useStorageStore';
import {FONT_TYPE, globalStyles, METRICS} from 'utils';
import { DM_NGANHNGHE, listHourOfDay } from 'utils/dm_nganhnghe';
import {setValue} from 'utils/layout';
import uuid from 'uuid-random';

import DropDownPicker from 'react-native-dropdown-picker';


// import { apiServices } from 'services';
import { IconType } from 'elements/AppIcon';
import { ALERT_TYPE, Toast } from 'react-native-alert-notification';
import { FlexAlignType } from 'react-native';
import SelectDate from 'components/SelectDate';


const QuickCatchDiary = () => {
  const {navigate, goBack} = useNavigation();

  // const {goBack} = useNavigation();
  // const [modalVisible, setModalVisible] = useState(false);
  // const [selectedDates, setSelectedDates] = useState({ from: null, to: null });

  // const handleConfirm = (from:any, to:any) => {
  //   setSelectedDates({ from, to });
  //   setModalVisible(false);
  // };
  
  // const initPage = () =>{
  //   const id_tau_ca = user?.id_tau_ca?._id;
  //   apiServices.Post({
  //     url: '/token/dm_nghekhaithac',
  //     body: {
  //       "filter": {},
  //       "sort": [
  //           [
  //               "ten",
  //               "asc"
  //           ]
  //       ],
  //       "select": [
  //           "ten",
  //           "nhom_nghe",
  //           "ma_code"
  //       ]
  //     },
  //   })
  // }

  const {
    setConfigQuickDiary,
    diary: {configQuickDiary},
  } = useDiaryStore();
  const {
    setDiaryLocal,
    diary: {diaryLocal},
  } = useDiaryStore();

  const {
    credential: {user},
  } = useStorageStore();

  const [modalVisible, setModalVisible] = useState(false);

  const [selectedDates, setSelectedDates] = useState({
    from: configQuickDiary?.dateDiary?.from ?? null,
    to: configQuickDiary?.dateDiary?.to ?? null,
  });

  const [selectedTimes, setSelectedTimes] = useState({
    from: {
      start: configQuickDiary?.autoDay?.timeDiary?.from?.start ?? null,
      end: configQuickDiary?.autoDay?.timeDiary?.from?.end ?? null,
    },
    to:  {
      start: configQuickDiary?.autoDay?.timeDiary?.to?.start ?? null,
      end: configQuickDiary?.autoDay?.timeDiary?.to?.end ?? null,
    },
  });

  const [openReleaseTime, setOpenReleaseTime] = useState({
    from: false,
    to: false,
  });

  const [openRetrieveTime, setOpenRetrieveTime] = useState({
    from: false, 
    to: false
  });

  const [numDay, setNumday] = useState(configQuickDiary?.autoDay?.numDayOnDiary ?? 1);
  const [listFish, setListFish] = useState<any[]>(
    configQuickDiary?.listFish ?? [],
  );

  const [nganhnghe, setNganhnghe] = useState<any>(DM_NGANHNGHE?.find(ele => ele.ma_code === user?.id_tau_ca?.id_nghe_chinh?.ma_code)?.value);
  const [openNganhnghe, setOpenNganhnghe] = useState<boolean>(false);



  // thời gian 
  // const [nganhnghe, setNganhnghe] = useState<any>(DM_NGANHNGHE?.find(ele => ele.ma_code === user?.id_tau_ca?.id_nghe_chinh?.ma_code)?.value);

  // cấu hình loại thời gian khai thác mẻ lứoi
  // const [selectlDiaryTp, setSelectlDiaryTp] = useState(configQuickDiary.autoTp ?? 0);
  const [selectlDiaryTp, setSelectlDiaryTp] = useState(1);
  

  const handleConfirm = (from: any, to: any) => {
    setSelectedDates({from, to});
    setModalVisible(false);
  };

  const onConfirmRelease = (datetime: any, type: any) => {
    setOpenReleaseTime({from: false, to: false});
    if(selectedTimes.from.start && type == 2){
      if(moment(selectedTimes.from.start,'HH:mm').isAfter(moment(datetime))){
        Alert.alert('Thông báo', 'Thời gian thả mẻ lưới bắt đầu phải sau kết thúc');
        return
      }
      // return
    }

    setSelectedTimes({
      ...selectedTimes,
      from :{
        start: type == 1 ? moment(datetime).format('HH:mm') : selectedTimes.from.start,
        end: type == 2 ? moment(datetime).format('HH:mm') : selectedTimes.from.end,
      }
    });

  };

  const onConfirmRetrieve = (datetime: any, type: any) => {
    setOpenRetrieveTime({from: false, to: false});
    if(numDay <= 1){
      // thời gian kết thúc
      if(type == 1){
        if(moment(selectedTimes.from.end,'HH:mm').isAfter(moment(datetime))){
          Alert.alert('Thông báo', 'TG bắt đầu thu PHẢI sau TG kết thúc thả(Vì số ngày khai thác là 1)');
          return
        }
      } else if(type == 2){
        if(moment(selectedTimes.from.end,'HH:mm').isAfter(moment(datetime))){
          Alert.alert('Thông báo', 'TG kết thúc thu PHẢI sau TG kết thúc thả(Vì số ngày khai thác là 1)');
          return
        }
      }
    }

    if(selectedTimes.to.start && type == 2){
      if(moment(selectedTimes.to.start,'HH:mm').isAfter(moment(datetime))){
        Alert.alert('Thông báo', 'Thời gian thu mẻ lưới bắt đầu phải sau kết thúc');
        return
      }
    }
    setSelectedTimes({
      ...selectedTimes,
      to :{
        start: type == 1 ? moment(datetime).format('HH:mm') : selectedTimes.to.start,
        end: type == 2 ? moment(datetime).format('HH:mm') : selectedTimes.to.end,
      }
    });

  };

  const openListFish = () => {
    navigate(SCREENS.SUGGEST_KIND as never, {
      type: 'quickCatchDiary',
      callback: callback,
    });
  };  

  const callback = (data: any) => {
    let _exist = listFish.find((ele: any) => ele._id === data._id);
    if (_exist) {
      return;
    }
    let list = [...listFish, data];
    setListFish(list);
  };

  const onRemoveFish = (id: string) => {
    let list = listFish.filter((ele: any) => ele._id !== id);
    setListFish(list);
  };

  const validateSubmit = () => {
    // if(selectlDiaryTp == 0){
    //   if (!selectedDates.from || !selectedDates.to) {
    //     Alert.alert('Thông báo', 'Vui lòng chọn thời gian chuyến biển');
    //     return false;
    //   }
    //   if (!selectedTimes.from.start ||!selectedTimes.from .end || !selectedTimes.to.start|| !selectedTimes.to.end) {
    //     Alert.alert('Thông báo', 'Vui lòng chọn thời gian mẻ lưới');
    //     return false;
    //   }
    //   if(numDay <= 1){
    //     if(moment(selectedTimes.from.end,'HH:mm').isAfter(moment(selectedTimes.to.start,'HH:mm'))){
    //       Alert.alert('Thông báo', 'TG bắt đầu thu PHẢI sau TG kết thúc thả(Vì số ngày khai thác là 1)');
    //       return false
    //     }
    //     if(moment(selectedTimes.from.end,'HH:mm').isAfter(moment(selectedTimes.to.end,'HH:mm'))){
    //       Alert.alert('Thông báo', 'TG kết thúc thu PHẢI sau TG kết thúc thả(Vì số ngày khai thác là 1)');
    //       return false
    //     }
    //   }
    //   return true;
    // } else 
    
    if (!hourReleaseTime.from || !hourReleaseTime.to) {
      Alert.alert('Thông báo', 'VUI LÒNG CHỌN THỜI GIAN THẢ LƯỚI');
      return false;
    }
    // if (!hourProcessTime.from || !hourProcessTime.to) {
    //   Alert.alert('Thông báo', 'VUI LÒNG CHỌN KHOẢN THỜI GIAN KHAI THÁC MẺ LƯỚI');
    //   return false;
    // }
    // if (!hourWaitTime.from || !hourWaitTime.to) {
    //   Alert.alert('Thông báo', 'VUI LÒNG CHỌN KHOẢNG THỜI GIAN KHAI THÁC MẺ LƯỚI KẾ TIẾP');
    //   return false;
    // }
    if(expectedFrom > expectedTo){
      Alert.alert('Thông báo', '[4] Thời gian kết thúc phải sau thời gian bắt đầu.');
      return false;
    }
    if(waitFrom > waitTo){
      Alert.alert('Thông báo', '[5] Thời gian kết thúc phải sau thời gian bắt đầu.');
      return false;
    }
    return true;
    
  };

  const onSaveAndCreate = () => {
    if (!validateSubmit()) {
      return;
    }
    // thực hiện check
    


    console.log('hourReleaseTime: ', hourReleaseTime);

    let inputDefault:any = {
      date: {
        start: moment(selectedDates.from, 'YYYY-MM-DD'),
        end: moment(selectedDates.to, 'YYYY-MM-DD').clone().add(moment.duration({days: 1}))
      },
      releaseTime :{
        from: moment(`${moment(selectedDates.from, 'YYYY-MM-DD').format('YYYY-MM-DD')} ${hourReleaseTime.from}`,'YYYY-MM-DD HH:mm'),  //moment('2025-03-01 00:00','YYYY-MM-DD HH:mm'),
        to: moment(`${moment(selectedDates.from, 'YYYY-MM-DD').format('YYYY-MM-DD')} ${hourReleaseTime.to}`,'YYYY-MM-DD HH:mm'),  //moment('2025-03-01 014:00','YYYY-MM-DD HH:mm'),
      },
      retrieveTime :{
        from: moment(`${moment(selectedDates.from, 'YYYY-MM-DD').format('YYYY-MM-DD')} ${hourRetrieveTime.from}`,'YYYY-MM-DD HH:mm'),  //moment('2025-03-01 05:00','YYYY-MM-DD HH:mm'),
        to: moment(`${moment(selectedDates.from, 'YYYY-MM-DD').format('YYYY-MM-DD')} ${hourRetrieveTime.to}`,'YYYY-MM-DD HH:mm') , //moment('2025-03-01 06:00','YYYY-MM-DD HH:mm'),
      },
      executionsTime :{
        from: listHourOfDay.find(ele=> ele.value == expectedFrom)?.param  ?? '00:00', // '04:00',
        to: listHourOfDay.find(ele=> ele.value == expectedTo)?.param  ?? '00:00', // '06:00',
      },
      waitTime :{
        from: listHourOfDay.find(ele=> ele.value == waitFrom)?.param ?? '00:00', // '01:00',
        to: listHourOfDay.find(ele=> ele.value == waitTo)?.param  ?? '00:00', //'02:00',
      },
      next: null
    }
    // Tạo danh sách mẻ lưới.
    // let list_days:any = {}
    // const isListValid = moment(inputDefault.date.start)
    // while(isListValid.isBefore(moment(inputDefault.date.end))){
    //   list_days[`${moment(isListValid).format('YYYY-MM-DD')}`] = false
    //   // list_days.push({
    //   //   date: moment(isListValid).format('YYYY-MM-DD'),
    //   //   isCreated: false,
    //   // })
    //   isListValid.add({days: 1})
    // }
    // console.log('list_days: ', list_days);

    // Kiểm tra dữ liệu thả lưới và thu lưới
    if(moment(inputDefault.releaseTime.from).isAfter(inputDefault.retrieveTime.from)
      || moment(inputDefault.releaseTime.to).isAfter(inputDefault.retrieveTime.from)
    ){
      // nếu thời gian nhỏ hơn thả 
      const add_one_day= moment.duration({days: 1})
      inputDefault = {
        ...inputDefault,
        retrieveTime:{
          from: inputDefault.retrieveTime.from.clone().add(add_one_day),
          to: inputDefault.retrieveTime.to.clone().add(add_one_day)
        }
      }
    }

    // Định nghĩa thời gian bắt đầu và kết thúc
    const hourReleaseTimeFrom = moment(inputDefault.releaseTime.from);
    const hourRetrieveTimeTo = moment(inputDefault.retrieveTime.to);
    // Tính số giờ giữa hai thời điểm
    const durationInHours = hourRetrieveTimeTo.diff(hourReleaseTimeFrom, 'hours', false); // true để lấy giá trị float
    // Định nghĩa số giờ cần so sánh
    const hourExpendTime = (listHourOfDay.find(ele=> ele.value == expectedTo)?.param ?? '00:00').split(':') ;
    let max_expected_temp = moment.duration({ hours: Number(hourExpendTime[0]), minutes: 0 })
    let _durationInHours = durationInHours
    if(durationInHours % 1 !== 0 ){
      _durationInHours = durationInHours - 0.5
    }
    if(moment.duration({hours: Number( durationInHours)}).asMinutes() > max_expected_temp.asMinutes()){
      // Toast.show({
      //   type: ALERT_TYPE.WARNING,
      //   title: 'Thông báo: ',
      //   textBody: `Vui lòng cài đặt khoảng thời gian khai thác lớn nhất không thấp hơn: ${_durationInHours} tiếng`,
      // });
      // return;
    }

    const hourReleaseTimeTo = moment(inputDefault.releaseTime.to);
    const hourRetrieveTimeFrom = moment(inputDefault.retrieveTime.from);
    // Tính số giờ giữa hai thời điểm
    const durationInHoursToFrom = hourRetrieveTimeFrom.diff(hourReleaseTimeTo, 'hours', true); // true để lấy giá trị float
    // Định nghĩa số giờ cần so sánh
    const hourExpendTimeToFrom = (listHourOfDay.find(ele=> ele.value == expectedFrom)?.param ?? '00:00').split(':') ;
    let max_expected_temp_tf = moment.duration({ hours: Number(hourExpendTimeToFrom[0]), minutes: 0 })
    let _durationInHoursToFrom = durationInHoursToFrom
    if(durationInHoursToFrom % 1 !== 0 ){
      _durationInHoursToFrom = durationInHoursToFrom - 0.5
    }
    if(moment.duration({hours: Number(_durationInHoursToFrom)}).asMinutes() < max_expected_temp_tf.asMinutes()){
      // Toast.show({
      //   type: ALERT_TYPE.WARNING,
      //   title: 'Thông báo: ',
      //   textBody: `Vui lòng cài đặt khoảng thời gian khai thác thấp nhất không cao hơn: ${_durationInHoursToFrom} tiếng`,
      // });
      // return;
    }
    // 
    let listExecutions =[]
    let listExecutionsError =[]
    // Số tiếng của biên độ thả release
    let spendTimeRelease = moment(inputDefault.releaseTime.to).diff(moment(inputDefault.releaseTime.from), 'minute');
    console.log('spendTimeRelease: ', spendTimeRelease);
    // Số tiếng của biên độ thu retrieve
    let spendTimeRetrieve = moment(inputDefault.retrieveTime.to).diff(moment(inputDefault.retrieveTime.from), 'minute');
    console.log('spendTimeRetrieve: ', spendTimeRetrieve); 
    
    // Số tiếng của biên độ min release - retrieve
    let spendTimeMin = moment(inputDefault.retrieveTime.from).diff(moment(inputDefault.releaseTime.to), 'minute');
    console.log('spendTimeMin: ', spendTimeMin);

    // Số tiếng của biên độ max release - retrieve
    let spendTimeMax = moment(inputDefault.retrieveTime.to).diff(moment(inputDefault.releaseTime.from), 'minute');
    console.log('spendTimeMax: ', spendTimeMax);

    // Thực hiện bổ sung + đồn
    let spendRetrieveMin = moment(inputDefault.retrieveTime.from).diff(moment(inputDefault.releaseTime.from), 'minute');
    let spendRetrieveMax = moment(inputDefault.retrieveTime.to).diff(moment(inputDefault.releaseTime.to), 'minute');
    console.log('From + rd_retreive | spendRetrieveMin: ', spendRetrieveMin);
    console.log('To   + rd_retreive | spendRetrieveMax: ', spendRetrieveMax);

    // Khoảng thời gian thực hiện mẻ lưới hợp lệ
    const min_execution = inputDefault.executionsTime.from.split(':')
    const max_execution = inputDefault.executionsTime.to.split(':')
    let min_expected = moment.duration({ hours: Number(min_execution[0]), minutes: 0 })
    let max_expected = moment.duration({ hours: Number(max_execution[0]), minutes: 0 })
    console.log('Thời gian thực hiện min | min_expected: ', min_expected.asMinutes());
    console.log('Thời gian thực hiện max | max_expected: ', max_expected.asMinutes());

    // Khoảng thời gian  chờ mẻ lưới hợp lệ
    const min_wait = inputDefault.waitTime.from.split(':')
    const max_wait = inputDefault.waitTime.to.split(':')
    let min_waited= moment.duration({ hours: Number(min_wait[0]), minutes: 0 })
    let max_waited = moment.duration({ hours: Number(max_wait[0]), minutes: 0 })
    console.log('Thời gian chờ min | min_waited: ', min_waited.asMinutes());
    console.log('Thời gian chờ max | max_waited: ', max_waited.asMinutes());

    let date_temp = inputDefault.retrieveTime.to
    let count = 0
    while (date_temp.isBefore(inputDefault.date.end)) { 
      count++
      console.log('')
      console.log('')
      console.log(`>>>>>>>>>>>>>>>>>>>> Ban đầu ${count}  >>>>>>>>>>>>>>>>>>>>>>>`)
      console.log('Ngày khai thác  : ', inputDefault.date.start.format('YYYY-MM-DD HH:mm') + " | " + inputDefault.date.end.format('YYYY-MM-DD HH:mm'));
      console.log('Thả lưới        : ', inputDefault.releaseTime.from.format('YYYY-MM-DD HH:mm') + " | " + inputDefault.releaseTime.to.format('YYYY-MM-DD HH:mm'));
      console.log('Thu lưới        : ', inputDefault.retrieveTime.from.format('YYYY-MM-DD HH:mm') + " | " + inputDefault.retrieveTime.to.format('YYYY-MM-DD HH:mm'));
      console.log('>>>>>>>>>>>>>>>>>>>> Kết thúc >>>>>>>>>>>>>>>>>>>>>>>')
      console.log('')
      console.log('')
      if(listExecutions.length < 1){
        // thực hiện 
        console.log('>>>>>>>>>>>>>>>>>>>> Tính toán thả lưới lần 1 >>>>>>>>>>>>>>>>>>>>>>>')
        const rd_release = getRandomDateTimeBetween(moment(inputDefault.releaseTime.from,'YYYY-MM-DD HH:mm'), moment(inputDefault.releaseTime.to,'YYYY-MM-DD HH:mm'))
        console.log('Tạo ngẫu nhiên thả | rd_release: ', rd_release);
        console.log('>>>>>>>>>>>>>>>>>>>> Tính toán thu lưới lần 1  >>>>>>>>>>>>>>>>>>>>>>>')
        const rd_retrieve = getRandomDateTimeBetween(moment(inputDefault.retrieveTime.from,'YYYY-MM-DD HH:mm'), moment(inputDefault.retrieveTime.to,'YYYY-MM-DD HH:mm'))
        console.log('rd_retrieve: ', rd_retrieve);
        console.log('>>>>>>>>>>>>>>>>>>>> Tính toán thực hiện lần 1  >>>>>>>>>>>>>>>>>>>>>>>')
        let duration_execution = moment.duration(moment(rd_retrieve,'YYYY-MM-DD HH:mm').diff(moment(rd_release,'YYYY-MM-DD HH:mm')));
        console.log('Thời gian thực hiện     | duration_execution: ', duration_execution.asMinutes());


      
        // const rd_wait = getRandomDuration(min_waited, max_waited);
        // console.log('Thời gian chờ:    | rd_wait   : ',  rd_wait.asMinutes());

        // So sánh thời gian thực hiện thực tế với dự kiến 
        console.log('duration_execution.asMinutes() < min_expected.asMinutes(): ', duration_execution.asMinutes() < min_expected.asMinutes());
        console.log('duration_execution.asMinutes() >  max_expected.asMinutes(): ', duration_execution.asMinutes() >  max_expected.asMinutes());
        
        if(duration_execution.asMinutes() >= min_expected.asMinutes() &&  duration_execution.asMinutes() <=  max_expected.asMinutes()){
          console.log('Thả lưới - thả        : ', moment(rd_release).format('YYYY-MM-DD HH:mm') + " | " + moment(rd_retrieve).format('YYYY-MM-DD HH:mm'));
          console.log('>>>>>>>>>>>>>>>>>>>> HỢP LỆ >>>>>>>>>>>>>>>>>>>>>>>')
          //  Thông tin mẻ lưới
          const id_item = uuid();
          const input = {
            auto: true,
            trang_thai: 2,
            id: id_item,RenderTextDate
            item_id: id_item,
            thoi_gian_tha: rd_release,
            thoi_gian_thu: rd_retrieve,
            time_execution: `${duration_execution.hours()}:${duration_execution.minutes() > 9? duration_execution.minutes():'0'+ duration_execution.minutes() }`,
            san_luong:
              listFish?.map((ele: any) => ({...ele, khoi_luong: '0'})) || [],
          };
          listExecutions.push(input)

          console.log('>>>>>>>>>>>>>>> THỰC HIỆN CỘNG DỒN >>>>>>>>>>>>>>>>')
          // const rd_execution_next = getRandomDuration(min_expected, max_expected);
          // min = 600 max = 900
          // điều kiện tha luoi du kien 
          const est_release_min = moment(`${moment(rd_retrieve,'YYYY-MM-DD HH:mm').format('YYYY-MM-DD')} ${hourReleaseTime.from}`)
          const est_release_max = moment(`${moment(rd_retrieve,'YYYY-MM-DD HH:mm').format('YYYY-MM-DD')} ${hourReleaseTime.to}`)

          // khoản thu cài đặt
          console.log('Thời gian chờ min | min_waited: ', min_waited.asMinutes());
          console.log('Thời gian chờ max | max_waited: ', max_waited.asMinutes());

          const minA = moment(est_release_min,'YYYY-MM-DD HH:mm').diff(moment(rd_retrieve,'YYYY-MM-DD HH:mm'), 'minute')
          console.log('minA: ', minA);
          const maxA = moment(est_release_max,'YYYY-MM-DD HH:mm').diff(moment(rd_retrieve,'YYYY-MM-DD HH:mm'), 'minute')
          console.log('maxA: ', maxA);
         
          // const dur_valid_min = moment(rd_retrieve,'YYYY-MM-DD HH:mm').diff(moment(est_release_min,'YYYY-MM-DD HH:mm'), 'minute')
          // const dur_valid_max = moment(rd_retrieve,'YYYY-MM-DD HH:mm').diff(moment(est_release_max,'YYYY-MM-DD HH:mm'), 'minute')
          let rd_wait = 0
          if( minA < min_waited.asMinutes() && maxA > max_waited.asMinutes()){
            // không hợp lệ 
            // cộng thêm 1 ngày
            let getDate = moment(rd_retrieve).add({days: 1}).format('YYYY-MM-DD')
            let minDate = moment(`${getDate} ${hourReleaseTime.from}`)
            let maxDate = moment(`${getDate} ${hourReleaseTime.to}`)
            let random_release = moment(getRandomDateTimeBetween(minDate, maxDate), 'YYYY-MM-DD HH:mm')
            date_temp = random_release
            inputDefault = { 
              ...inputDefault,
              next: random_release
            }

          } else{
            if(minA >= min_waited.asMinutes()){
              if(maxA <= max_waited.asMinutes()){
                // hợp lệ 
                rd_wait = getRandomMinute(minA, maxA);
              }else{
                rd_wait = getRandomMinute(minA, max_waited.asMinutes());
              }
            }else{
              if(maxA <= max_waited.asMinutes()){
                rd_wait = getRandomMinute(min_waited.asMinutes(), maxA);
              }else{
                rd_wait = getRandomMinute(min_waited.asMinutes(), min_waited.asMinutes());
              }
            }
            console.log('Thời gian chờ:    | rd_wait   : ',  rd_wait);
            let random_release = moment(rd_retrieve).add(moment.duration({minute: rd_wait}), 'minute')
            console.log('moment(rd_retrieve): ', moment(rd_retrieve).format('YYYY-MM-DD HH:mm'));
            console.log('random_release: ', random_release.format('YYYY-MM-DD HH:mm'));
            date_temp = random_release
            inputDefault = { 
              ...inputDefault,
              next: random_release
            }
          }

          // //end
          // // Thời gian thả kế tiếp
          // console.log('Thời gian chờ:    | rd_wait   : ',  rd_wait);
          // let random_release = moment(rd_retrieve).add(rd_wait, 'minute')
          
          // console.log('---------- rd_retrieve: ', moment(rd_retrieve).format('YYYY-MM-DD HH:mm'));
          // console.log('---------- random_release: ', random_release.format('YYYY-MM-DD HH:mm'));

          // kiểm hợp lệ 
          // let getDate = random_release.format('YYYY-MM-DD')
          // let minDate = moment(`${getDate} ${hourReleaseTime.from}`)
          // let maxDate = moment(`${getDate} ${hourReleaseTime.to}`)
          // if( random_release.isBetween(minDate, maxDate)){
          //   // mẻ kế tiếp hợp lệ 
          //   date_temp = random_release
          //   inputDefault = { 
          //     ...inputDefault,
          //     next: random_release
          //   }
          // }else {
          //   // thực hiện cộng thêm ngày 
          //   if(random_release.isBefore(minDate)){
          //     // Thời gian nghỉ 10 -14 tiếng
          //     // mẻ lưới thả 17:30 -> 19:30 -> ngày 01.03.2025 18:00 thả 
          //     // | thu ngày 02.03.2025 7:30 + nghỉ 14 tiếng -> next-release: 21:30 quá giờ 
          //     // | thu ngày 02.03.2025 7:30 + nghỉ 12 tiếng -> next-release: 19:30 đúng khoảng khai thác
          //     // | thu ngày 02.03.2025 7:30 + nghỉ 10 tiếng -> next-release: 17:30 đúng khoảng khai thác
          //     // | thu ngày 02.03.2025 7:30 + nghỉ  9 tiếng -> next-release: 16:30 đúng khoảng khai thác
          //     random_release = moment(getRandomDateTimeBetween(minDate, maxDate), 'YYYY-MM-DD HH:mm')
          //   }else{
          //     getDate = random_release.add({days: 1}).format('YYYY-MM-DD')
          //     minDate = moment(`${getDate} ${hourReleaseTime.from}`)
          //     maxDate = moment(`${getDate} ${hourReleaseTime.to}`)
          //     random_release = moment(getRandomDateTimeBetween(minDate, maxDate), 'YYYY-MM-DD HH:mm')
          //   }
          //   date_temp = random_release
          //     inputDefault = { 
          //       ...inputDefault,
          //       next: random_release
          //     }
          // } 
          // date_temp = random_release
        }else {
          const duration_execution_temp = moment.duration({minutes: 30})
          date_temp = date_temp.clone().add(duration_execution_temp, 'minute')
          console.log('date_temp: ', date_temp);
          listExecutionsError.push(inputDefault.date.start.format('YYYY-MM-DD HH:mm'))
          console.log('')
          console.log('>>>>>>>>>>>>>>>[1] KHÔNG HỢP LỆ >>>>>>>>>>>>>>>>')
          console.log('')
        }
      }else{
        console.log(`[2]>>>>>>>>>>>>>>>>>>>> KHAI THÁC MẺ KẾ TIẾP  >>>>>>>>>>>>>>>>>>>>>>>`)
        const next = inputDefault.next;
        const _retrieve_from = moment(next,'YYYY-MM-DD HH:mm').clone().add(spendRetrieveMin,'minutes')
        let _retrieve_to = moment(next, 'YYYY-MM-DD HH:mm').clone().add(spendRetrieveMax,'minutes').add(spendTimeRetrieve,'minutes')
        if(_retrieve_to.isAfter(moment(`${_retrieve_to.format('YYYY-MM-DD')} ${moment(hourRetrieveTime.to,'HH:mm').format('HH:mm')}`))){
          _retrieve_to = moment(`${_retrieve_to.format('YYYY-MM-DD')} ${moment(hourRetrieveTime.to,'HH:mm').format('HH:mm')}`)
        }
        console.log('[2] spendRetrieveMin | spendRetrieveMax: ', spendRetrieveMin + " | " + spendRetrieveMax);
        console.log('[2] _retrieve_from: ', _retrieve_from.format('YYYY-MM-DD HH:mm')); 
        console.log('[2] _retrieve_to: ', _retrieve_to.format('YYYY-MM-DD HH:mm'));
        // kiểm tra thời gian thu mẻ  tối đa.


        const rd_retrieve = getRandomDateTimeBetween(moment(_retrieve_from,'YYYY-MM-DD HH:mm'), moment(_retrieve_to,'YYYY-MM-DD HH:mm'))
        if(moment(rd_retrieve,'YYYY-MM-DD HH:mm').isBefore(inputDefault.date.end)){
          let duration_execution = moment.duration(moment(rd_retrieve,'YYYY-MM-DD HH:mm').diff(moment(next,'YYYY-MM-DD HH:mm')));
          // kiểm tra thực hiện 
          const rd_wait = getRandomDuration(min_waited, max_waited);
          console.log('[2] Thời gian thực hiện     | duration_execution: ', duration_execution.asMinutes());
          console.log('[2] Thời gian chờ:          | rd_wait   : ',  rd_wait.asMinutes());
          if(duration_execution.asMinutes() >= min_expected.asMinutes() &&  duration_execution.asMinutes() <=  max_expected.asMinutes()){
            console.log('[2]>>>>>>>>>>>>>>>>>>>>  HỢP LỆ >>>>>>>>>>>>>>>>>>>>>>>')
            //  Thông tin mẻ lưới 
            const id_item = uuid();
            const input = {
              auto: true,
              trang_thai: 2,
              id: id_item,
              item_id: id_item,
              thoi_gian_tha: next,
              thoi_gian_thu: rd_retrieve,
              time_execution: `${duration_execution.hours()}:${duration_execution.minutes() > 9? duration_execution.minutes():'0'+ duration_execution.minutes() }`,
              san_luong:
                listFish?.map((ele: any) => ({...ele, khoi_luong: '0'})) || [],
            };
            listExecutions.push(input)
            console.log('[2]>>>>>>>>>>>>>>> THỰC HIỆN CỘNG DỒN >>>>>>>>>>>>>>>>')
            // const rd_execution_next = getRandomDuration(min_expected, max_expected);
          // min = 600 max = 900
          // điều kiện tha luoi du kien 
          const est_release_min = moment(`${moment(rd_retrieve,'YYYY-MM-DD HH:mm').format('YYYY-MM-DD')} ${hourReleaseTime.from}`)
          const est_release_max = moment(`${moment(rd_retrieve,'YYYY-MM-DD HH:mm').format('YYYY-MM-DD')} ${hourReleaseTime.to}`)

          // khoản thu cài đặt
          console.log('Thời gian chờ min | min_waited: ', min_waited.asMinutes());
          console.log('Thời gian chờ max | max_waited: ', max_waited.asMinutes());

          const minA = moment(est_release_min,'YYYY-MM-DD HH:mm').diff(moment(rd_retrieve,'YYYY-MM-DD HH:mm'), 'minute')
          console.log('minA: ', minA);
          const maxA = moment(est_release_max,'YYYY-MM-DD HH:mm').diff(moment(rd_retrieve,'YYYY-MM-DD HH:mm'), 'minute')
          console.log('maxA: ', maxA);
         
          // const dur_valid_min = moment(rd_retrieve,'YYYY-MM-DD HH:mm').diff(moment(est_release_min,'YYYY-MM-DD HH:mm'), 'minute')
          // const dur_valid_max = moment(rd_retrieve,'YYYY-MM-DD HH:mm').diff(moment(est_release_max,'YYYY-MM-DD HH:mm'), 'minute')
          let rd_wait = 0
          if( minA < min_waited.asMinutes() && maxA > max_waited.asMinutes()){
            // không hợp lệ 
            // cộng thêm 1 ngày
            let getDate = moment(rd_retrieve).add({days: 1}).format('YYYY-MM-DD')
            let minDate = moment(`${getDate} ${hourReleaseTime.from}`)
            let maxDate = moment(`${getDate} ${hourReleaseTime.to}`)
            let random_release = moment(getRandomDateTimeBetween(minDate, maxDate), 'YYYY-MM-DD HH:mm')
            date_temp = random_release
            inputDefault = { 
              ...inputDefault,
              next: random_release
            }

          } else{
            if(minA >= min_waited.asMinutes()){
              if(maxA <= max_waited.asMinutes()){
                // hợp lệ 
                rd_wait = getRandomMinute(minA, maxA);
              }else{
                rd_wait = getRandomMinute(minA, max_waited.asMinutes());
              }
            }else{
              if(maxA <= max_waited.asMinutes()){
                rd_wait = getRandomMinute(min_waited.asMinutes(), maxA);
              }else{
                rd_wait = getRandomMinute(min_waited.asMinutes(), min_waited.asMinutes());
              }
            }
            console.log('Thời gian chờ:    | rd_wait   : ',  rd_wait);
            let random_release = moment(rd_retrieve).add(moment.duration({minute: rd_wait}), 'minute')
            date_temp = random_release
            inputDefault = { 
              ...inputDefault,
              next: random_release
            }
          }
            // const rd_execution_next = getRandomDuration(min_expected, max_expected);
            // Thời gian thả kế tiếp
            // let random_release = moment(rd_retrieve).clone().add(rd_wait, 'minute')
            // console.log('[3]random_release: ', random_release.format('YYYY-MM-DD HH:mm'));
             
            // // kiểm hợp lệ 
            // let getDate = random_release.format('YYYY-MM-DD')
            // let minDate = moment(`${getDate} ${hourReleaseTime.from}`)
            // let maxDate = moment(`${getDate} ${hourReleaseTime.to}`)
            // if( random_release.isBetween(minDate, maxDate)){
            //   // mẻ kế tiếp hợp lệ 
            //   date_temp = random_release
            //   inputDefault = { 
            //     ...inputDefault,
            //     next: random_release
            //   }
            // }else {
            //   // thực hiện cộng thêm ngày 
            //   if(random_release.isBefore(minDate)){
            //     // Thời gian nghỉ 10 -14 tiếng
            //     // mẻ lưới thả 17:30 -> 19:30 -> ngày 01.03.2025 18:00 thả 
            //     // | thu ngày 02.03.2025 7:30 + nghỉ 14 tiếng -> next-release: 21:30 quá giờ 
            //     // | thu ngày 02.03.2025 7:30 + nghỉ 12 tiếng -> next-release: 19:30 đúng khoảng khai thác
            //     // | thu ngày 02.03.2025 7:30 + nghỉ 10 tiếng -> next-release: 17:30 đúng khoảng khai thác
            //     // | thu ngày 02.03.2025 7:30 + nghỉ  9 tiếng -> next-release: 16:30 đúng khoảng khai thác
            //     random_release = moment(getRandomDateTimeBetween(minDate, maxDate), 'YYYY-MM-DD HH:mm')
            //   }else{
            //     getDate = random_release.add({days: 1}).format('YYYY-MM-DD')
            //     minDate = moment(`${getDate} ${hourReleaseTime.from}`)
            //     maxDate = moment(`${getDate} ${hourReleaseTime.to}`)
            //     random_release = moment(getRandomDateTimeBetween(minDate, maxDate), 'YYYY-MM-DD HH:mm')
            //   }
            //   date_temp = random_release
            //   inputDefault = { 
            //     ...inputDefault,
            //     next: random_release
            //   }
            // } 
            // date_temp = random_release
          }else{
            const duration_execution_temp = moment.duration({minutes: 30})
            date_temp = date_temp.clone().add(duration_execution_temp, 'minute')
            listExecutionsError.push(inputDefault.date.start.format('YYYY-MM-DD HH:mm'))
            console.log('[2]')
            console.log('[2]>>>>>>>>>>>>>>> KHÔNG HỢP LỆ MẺ >>>>>>>>>>>>>>>>')
            console.log('[2] time current: ', moment(date_temp).format('YYYY-MM-DD HH:mm'));
            console.log('[2]')
          }
          console.log('[2]')
          console.log('[2]>>>>>>>>>>>>>>>>>>>> KẾT THÚC >>>>>>>>>>>>>>>>>>>>')
          console.log('[2]')
        }else{
          date_temp = moment(rd_retrieve)
        }
      }
    }
    // lưu cấu hình
    // Toast.show({
    //   type: ALERT_TYPE.INFO,
    //   title: 'Thông báo: ',
    //   textBody: `Thành công: ${listExecutions.length} | Thất bại: ${listExecutionsError.length}`,
    // });
    const config = {
      autoTp: selectlDiaryTp,
      listFish: listFish,
      dateDiary: {
        from: selectedDates.from,
        to: selectedDates.to,
      },
      autoHours: {
        releaseTime: hourReleaseTime,
        retrieveTime: hourRetrieveTime,
        waitTime: {
          from : listHourOfDay?.find(ele => ele.value === waitFrom)?.param ,
          to : listHourOfDay?.find(ele => ele.value === waitTo)?.param ,
        },
        processTime: {
          from:  listHourOfDay?.find(ele => ele.value === expectedFrom)?.param,
          to: listHourOfDay?.find(ele => ele.value === expectedTo)?.param 
        },
      },
      autoDay: {
        numDayOnDiary: numDay,
        timeDiary: {
          from: selectedTimes.from,
          to: selectedTimes.to,
        },
      },
    };
    setConfigQuickDiary(config);
    const listClean = diaryLocal?.filter((ele:any) => !ele.auto) ?? []
    // lưu mẻ lưới
    // thực hiện tạo mẻ lưới local
    const list = [...listClean, ...listExecutions];
    setDiaryLocal(
      list?.sort(
        (a: any, b: any) =>
          new Date(a?.thoi_gian_tha) - new Date(b?.thoi_gian_tha),
      ),
    );
    console.log('date_temp: ', date_temp);

    goBack();
  };

  const onCancel = () => {
    Alert.alert('Thông báo', 'Bạn có muốn bỏ qua cài đặt tạo nhanh?', [
      {
        text: 'Huỷ',
        onPress: () => {},
      },
      {
        text: 'Đồng ý',
        onPress: () => {
          goBack();
        },
      },
    ]);
  };
  // lỗi qua ngày của loại thu lưới
  const getRandomTimeBetween = (startDate:any, endDate:any) => {
    const startTimestamp = moment(startDate).valueOf();
    const endTimestamp = moment(endDate).valueOf();
    const randomTimestamp = Math.floor(Math.random() * (endTimestamp - startTimestamp) + startTimestamp);
    return moment(randomTimestamp).format('HH:mm');
  };

  const getRandomDateTimeBetween = (startDate:any, endDate:any) => {
    const startTimestamp = moment(startDate).valueOf();
    const endTimestamp = moment(endDate).valueOf();
    const randomTimestamp = Math.floor(Math.random() * (endTimestamp - startTimestamp) + startTimestamp);
    return moment(randomTimestamp).format('YYYY-MM-DD HH:mm');
  };

  function getRandomDuration(minDuration:any, maxDuration:any) {
    const minMillis = minDuration.asMilliseconds(); // Chuyển min thành milliseconds
    const maxMillis = maxDuration.asMilliseconds(); // Chuyển max thành milliseconds
    const randomMillis = Math.random() * (maxMillis - minMillis) + minMillis; // Random giá trị
  
    return moment.duration(randomMillis, 'milliseconds'); // Chuyển lại thành duration
  }
  function getRandomMinute(minDuration:any, maxDuration:any) {

    const randomMinute = Math.random() * (maxDuration - minDuration) + minDuration; // Random giá trị
  
    return randomMinute; // Chuyển lại thành duration
  }
  
  // KHOẢNG THỜI GIAN THẢ MỘT MẺ LƯỚI
  const [ hourReleaseTime, setHourReleaseTime] = useState({
    from: configQuickDiary?.autoHours?.releaseTime?.from ?? null,
    to: configQuickDiary?.autoHours?.releaseTime?.to ?? null,
    date_from: configQuickDiary?.autoHours?.releaseTime?.date_from ?? null,
    date_to: configQuickDiary?.autoHours?.releaseTime?.date_to ?? null,
  });
  
  const [ hourRetrieveTime, setHourRetrieveTime] = useState({
    from: configQuickDiary?.autoHours?.retrieveTime?.from ?? null,
    to: configQuickDiary?.autoHours?.retrieveTime?.to ?? null,
    date_from: configQuickDiary?.autoHours?.retrieveTime?.date_from ?? null,
    date_to: configQuickDiary?.autoHours?.retrieveTime?.date_to ?? null,
  });

  const [ hourProcessTime, setHourProcessTime] = useState({
    from: configQuickDiary?.autoHours?.processTime?.from ?? null,
    to: configQuickDiary?.autoHours?.processTime?.to ?? null,
    
  });

  const [ hourWaitTime, setHourWaitTime] = useState({
    from: configQuickDiary?.autoHours?.waitTime?.from ?? null,
    to: configQuickDiary?.autoHours?.waitTime?.to ?? null,
  });

  const [openPickerTimeTpHour, setOpenPickerTimeTpHour] = useState<number>(0)

  // open 
  const [openExpectedFrom, setOpenExpectedFrom] = useState(false);
  const [expectedFrom, setExpectedFrom] = useState<any>(listHourOfDay?.find(ele => ele.param === configQuickDiary?.autoHours?.processTime?.from)?.value ?? 0);


  const [openExpectedTo, setOpenExpectedTo] = useState(false);
  const [expectedTo, setExpectedTo] = useState<any>(listHourOfDay?.find(ele => ele.param === configQuickDiary?.autoHours?.processTime?.to)?.value ?? 0);

  const [openWaitFrom, setOpenWaitFrom] = useState(false)
  const [waitFrom, setWaitFrom] = useState<any>(listHourOfDay?.find(ele => ele.param === configQuickDiary?.autoHours?.waitTime?.from)?.value ?? 0);


  const [openWaitTo, setOpenWaitTo] = useState(false);

  const [waitTo, setWaitTo] = useState<any>(listHourOfDay?.find(ele => ele.param === configQuickDiary?.autoHours?.waitTime?.to)?.value ?? 0);

  const validateChooseHours =(datetime:any)=>{
    if(openPickerTimeTpHour == 1 ){
      // if( !hourReleaseTime.from && moment(hourReleaseTime.to,'HH:mm').isBefore(moment(datetime))){
      //   // Alert.alert('Thông báo', '[1]Thời gian bắt đầu phải trước thời gian kết thúc.');
      //   setOpenPickerTimeTpHour(0)
      //   return false;
      // }
      // if( hourReleaseTime.from && moment(hourReleaseTime.from,'HH:mm').isBefore(moment(datetime))){
      //   // thêm 1h
      //   // Alert.alert('Thông báo', '[2]Thời gian bắt đầu phải trước thời gian kết thúc.');
      //   const dur = moment.duration({minutes: 1})
      //   if( moment(datetime).hours() == 23 && moment(datetime).minutes() == 59 ){
      //     console.log('moment: lỗi');
      //     return true
      //   }
      //   const temp = moment(datetime).clone().add(dur).format('HH:mm')
      //   setOpenPickerTimeTpHour(0)
      //   setHourReleaseTime({
      //     ...hourReleaseTime,
      //     to : temp,
      //     date_to: moment(datetime).clone().add(dur)
      //   })
      //   return true
      // }
      
    }else if(openPickerTimeTpHour == 2 ){
      // if( hourReleaseTime.from && moment(hourReleaseTime.from,'HH:mm').isAfter(moment(datetime))){
      //   Alert.alert('Thông báo', '[3] Thời gian kết thúc phải sau thời gian bắt đầu.');
      //   setOpenPickerTimeTpHour(0)
      //   return false;
      // }
    }
   
    // else if(openPickerTimeTpHour == 3 ){
    //   if( !hourProcessTime.from && moment(hourProcessTime.to,'HH:mm').isBefore(moment(datetime))){
    //     Alert.alert('Thông báo', '[4]Thời gian bắt đầu phải trước thời gian kết thúc.');
    //     setOpenPickerTimeTpHour(0)
    //     return false;
    //   }
    //   if( hourProcessTime.from && moment(hourProcessTime.to,'HH:mm').isBefore(moment(datetime))){
    //     Alert.alert('Thông báo', '[5]Thời gian bắt đầu phải trước thời gian kết thúc.');
    //     setOpenPickerTimeTpHour(0)
    //     return false;
    //   }
      
    // }else if(openPickerTimeTpHour == 4 ){
    //   if( hourProcessTime.from && moment(hourProcessTime.from,'HH:mm').isAfter(moment(datetime))){
    //     Alert.alert('Thông báo', '[6] Thời gian kết thúc phải sau thời gian bắt đầu.');
    //     setOpenPickerTimeTpHour(0)
    //     return false;
    //   }
    // }else if(openPickerTimeTpHour == 5 ){
    //   if( !hourWaitTime.from && moment(hourWaitTime.to,'HH:mm').isBefore(moment(datetime))){
    //     Alert.alert('Thông báo', '[4]Thời gian bắt đầu phải trước thời gian kết thúc.');
    //     setOpenPickerTimeTpHour(0)
    //     return false;
    //   }
    //   if( hourWaitTime.from && moment(hourWaitTime.from,'HH:mm').isBefore(moment(datetime))){
    //     Alert.alert('Thông báo', '[5]Thời gian bắt đầu phải trước thời gian kết thúc.');
    //     setOpenPickerTimeTpHour(0)
    //     return false;
    //   }
    // }else if(openPickerTimeTpHour == 6 ){
    //   if( hourWaitTime.from && moment(hourWaitTime.from,'HH:mm').isAfter(moment(datetime))){
    //     Alert.alert('Thông báo', '[9] Thời gian kết thúc phải sau thời gian bắt đầu.');
    //     setOpenPickerTimeTpHour(0)
    //     return false;
    //   }
    // }
    return true
  }

  const onConfirmTpHour = (datetime:any) =>{
    if(!validateChooseHours(datetime)) return

    if(openPickerTimeTpHour == 1){
      let param = hourReleaseTime
      if( hourReleaseTime.from && moment(hourReleaseTime.from,'HH:mm').isBefore(moment(datetime))){
        // thêm 1h
        // Alert.alert('Thông báo', '[2]Thời gian bắt đầu phải trước thời gian kết thúc.');
        const dur = moment.duration({minutes: 1})
        if( moment(datetime).hours() == 23 && moment(datetime).minutes() == 59 ){
        }else{
          const temp = moment(datetime).clone().add(dur)
          setOpenPickerTimeTpHour(0)
          param = {
            ...hourReleaseTime,
            to : temp.format('HH:mm'),
            date_to: temp
          }            
        }        
      }
      setHourReleaseTime({...hourReleaseTime, ...param, from: moment(datetime).format('HH:mm'), date_from:  moment(datetime)  })
    }else if(openPickerTimeTpHour == 2){
      let param = hourReleaseTime
      if( hourReleaseTime.from && moment(hourReleaseTime.from,'HH:mm').isAfter(moment(datetime))){
        // Alert.alert('Thông báo', '[3] Thời gian kết thúc phải sau thời gian bắt đầu.');
        setOpenPickerTimeTpHour(0)
        const dur = moment.duration({minutes: -1})
        if( moment(datetime).hours() == 0 && moment(datetime).minutes() == 0 ){
        }else{
          const temp = moment(datetime).clone().add(dur)
          setOpenPickerTimeTpHour(0)
          param = {
            ...hourReleaseTime,
            from : temp.format('HH:mm'),
            date_from: temp
          }            
        }        
      }
      setHourReleaseTime({...hourReleaseTime,...param, to: moment(datetime).format('HH:mm'), date_to:  moment(datetime), })
    // }else if(openPickerTimeTpHour == 3){
    //   setHourProcessTime({...hourProcessTime, from: moment(datetime).format('HH:mm')})
    // }else if(openPickerTimeTpHour == 4){
    //   setHourProcessTime({...hourProcessTime, to: moment(datetime).format('HH:mm')})
    // }else if(openPickerTimeTpHour == 5){
    //   setHourWaitTime({...hourWaitTime, from: moment(datetime).format('HH:mm')})
    // }else if(openPickerTimeTpHour == 6){
    //   setHourWaitTime({...hourWaitTime, to: moment(datetime).format('HH:mm')})
    }else if(openPickerTimeTpHour == 7){
      let param = hourRetrieveTime
      if( hourRetrieveTime.from && moment(hourRetrieveTime.from,'HH:mm').isBefore(moment(datetime))){
        // thêm 1h
        // Alert.alert('Thông báo', '[2]Thời gian bắt đầu phải trước thời gian kết thúc.');
        const dur = moment.duration({minutes: 1})
        if( moment(datetime).hours() == 23 && moment(datetime).minutes() == 59 ){
        }else{
          const temp = moment(datetime).clone().add(dur)
          setOpenPickerTimeTpHour(0)
          param = {
            ...hourRetrieveTime,
            to : temp.format('HH:mm'),
            date_to: temp
          }            
        }        
      }
      setHourRetrieveTime({...hourRetrieveTime, ...param, from: moment(datetime).format('HH:mm'), date_from:  moment(datetime),  })
    }else if(openPickerTimeTpHour == 8){
      let param = hourRetrieveTime
      if( hourRetrieveTime.from && moment(hourRetrieveTime.from,'HH:mm').isAfter(moment(datetime))){
      const dur = moment.duration({minutes: -1})
        if( moment(datetime).hours() == 0 && moment(datetime).minutes() == 0 ){
        }else{
          const temp = moment(datetime).clone().add(dur)
          setOpenPickerTimeTpHour(0)
          param = {
            ...hourRetrieveTime,
            from : temp.format('HH:mm'),
            date_from: temp
          }            
        }      
      }
      setHourRetrieveTime({...hourRetrieveTime, ...param, to: moment(datetime).format('HH:mm'), date_to:  moment(datetime), })
    }
    setOpenPickerTimeTpHour(0)
  }

  const RenderByHour = () =>{
    return <View> 
          {/* Thời gian thả */}
          <View style={[styles.view_header]}>
            <AppText
              customStyle={styles.txt_header}
              text={'KHOẢNG THỜI GIAN THẢ LƯỚI\n(Giờ bắt đầu)'}
            />
          </View>
          <View style={[styles.view_content,{height: 'auto', flexDirection:'column', marginBottom: 10  }]}>
            <SelectDate
              clearable
              isDate ={false}
              required = {true}
              mode ='time'
              onSubmit={(values:any) => {         
                setHourReleaseTime({...hourReleaseTime, date_from: values, from: moment(values).format('HH:mm')  })
              }}
              value={hourReleaseTime.from && moment(hourReleaseTime.from, 'HH:mm')}
              width="100%"
              marginHorizontal={0}
              placeholderText={'BẮT ĐẦU'}
            /> 
            <SelectDate
              clearable
              isDate ={false}
              required = {true}
              mode ='time'
              onSubmit={(values:any) => {         
                setHourReleaseTime({...hourReleaseTime, date_to: values, to: moment(values).format('HH:mm')  })
              }}
              value={hourReleaseTime.to &&  moment(hourReleaseTime.to, 'HH:mm')}            
              width="100%"
              marginHorizontal={0}
              placeholderText={'KẾT THÚC'}
            />
          </View>
          {/* Thời gian thu */}
          <View style={[styles.view_header]}>
            <AppText
              customStyle={styles.txt_header}
              text={'KHOẢNG THỜI GIAN THU LƯỚI\n(Giờ kết thúc)'}
            />
          </View>
          <View style={[styles.view_content, {height: 'auto', flexDirection:'column', marginBottom: 10}]}>
            <SelectDate
              clearable
              isDate ={false}
              required = {true}
              mode ='time'
              onSubmit={(values:any) => {         
                setHourRetrieveTime({...hourRetrieveTime, date_from: values, from: moment(values).format('HH:mm')  })
              }}
              value={hourRetrieveTime.from &&  moment(hourRetrieveTime.from, 'HH:mm')}
              width="100%"
              marginHorizontal={0}
              placeholderText={'BẮT ĐẦU'}
            /> 
            <SelectDate
              isDate ={false}
              required = {true}
              mode ='time'
              onSubmit={(values:any) => {         
                setHourRetrieveTime({...hourRetrieveTime, date_to: values, to: moment(values).format('HH:mm')  })
              }}
              value={hourRetrieveTime.to && moment(hourRetrieveTime.to, 'HH:mm')}            
              width="100%"
              marginHorizontal={0}
              placeholderText={'KẾT THÚC'}
            />
          </View>

          {/* Khoảng thời gian khai thác dự kiến */}
          <View style={[styles.view_header]}>
            <AppText
              customStyle={styles.txt_header}
              text={'KHOẢNG THỜI GIAN KHAI THÁC MẺ LƯỚI\n(Số tiếng)'}
            />
          </View>
          <View style={[styles.view_content,{height:'auto', flexDirection:'column', flex: 1 , marginBottom: 10 }]}>
            <View style={{ flex: 1, height: expectedFrom ? 80: 'auto', flexDirection: expectedFrom ? 'column':'row', 
              alignItems: expectedFrom ? 'flex-start' : 'center',
              borderColor: expectedFrom ? COLORS.SILVER : COLORS.RED,  
              borderBottomWidth: expectedFrom? 0 : 1, marginBottom: 5
              
              }}> 
              <Text style={{fontSize: METRICS.text_12, color: COLORS.LIGHT_GREY, paddingBottom: 0, marginBottom: 0 }}>{'BẮT ĐẦU'}</Text>
              <View style={{flex: expectedFrom ? 0: 1 }}>
                <DropDownPicker
                  listMode = "MODAL"
                  style={{ 
                    borderColor: COLORS.SILVER, 
                    borderWidth: 0, 
                    borderBottomWidth: expectedFrom?  1 : 0,                   
                    // borderRadius: 8,
                    // flex: 1
                  }}
                  textStyle={{fontWeight:"bold"}}
                  open={openExpectedFrom}
                  value={expectedFrom}
                  items={listHourOfDay}
                  maxHeight={300}
                  setOpen={setOpenExpectedFrom}
                  setValue={setExpectedFrom}
                  placeholder=''
                  // placeholderStyle={{
                  //   color : COLORS.LIGHT_GREY,
                  //   fontWeight:'normal',
                  //   fontSize: METRICS.text_12
                  // }}
                  multiple={false}
                  modalTitle="Chọn số tiếng thời gian khai thác bắt đầu"
                  modalTitleStyle={{
                    fontSize: METRICS.text_16,
                    justifyContent: 'center',
                    fontWeight:"bold",
                    padding:0,
                    margin:0,
                    color: COLORS.WHITE,
                    alignSelf:"center"
                  }}
                  modalAnimationType= 'slide'
                  modalContentContainerStyle={{
                    backgroundColor: COLORS.MAIN_COLOR,
                  }}
                  listItemContainerStyle={{
                    backgroundColor: COLORS.WHITE,

                  }}
                />
              </View>
              
            </View>
            <View style={{ flex: 1, height: expectedTo ? 80: 'auto', flexDirection: expectedTo ? 'column':'row', alignItems: expectedTo ? 'flex-start' : 'center',
              borderColor: expectedTo ? COLORS.SILVER : COLORS.RED,  
              borderBottomWidth: expectedTo? 0 : 1, marginBottom: 5}}> 
              <Text style={{fontSize: METRICS.text_12, color: COLORS.LIGHT_GREY, paddingBottom: 0, marginBottom: 0 }}>{'KẾT THÚC'}</Text>
              <View style={{flex: expectedTo ? 0: 1 }}>
              <DropDownPicker
                listMode = "MODAL"
                style={{ 
                  marginTop:0,
                  paddingTop: 0,
                  borderColor: COLORS.SILVER, 
                  borderWidth: 0, 
                  borderBottomWidth: expectedTo?  1:0,       

                }}
                textStyle={{fontWeight:"bold"}}
                open={openExpectedTo}
                value={expectedTo}
                items={listHourOfDay}
                maxHeight={300}
                setOpen={setOpenExpectedTo}
                setValue={setExpectedTo}
                placeholder=''
                placeholderStyle={{
                  color : COLORS.LIGHT_GREY,
                  fontWeight:'normal',
                  fontSize: METRICS.text_12
                }}
                multiple={false}
                modalTitle="Chọn số tiếng thời gian khai thác kết thúc"
                modalTitleStyle={{
                  fontSize: METRICS.text_16,
                  justifyContent: 'center',
                  fontWeight:"bold",
                  padding:0,
                  margin:0,
                  color: COLORS.WHITE,
                  alignSelf:"center"
                }}
                modalAnimationType= 'slide'
                modalContentContainerStyle={{
                  backgroundColor: COLORS.MAIN_COLOR,
                }}
                listItemContainerStyle={{
                  backgroundColor: COLORS.WHITE,

                }}
              />
              </View>
            </View>
          </View>
          {/* Thời gian nghỉ */}
          <View style={[styles.view_header]}>
            <AppText
              customStyle={styles.txt_header}
              text={'KHOẢNG THỜI GIAN KHAI THÁC MẺ LƯỚI KẾ TIẾP\n(Số tiếng)'}
            />
          </View>
          <View style={[styles.view_content,{height:'auto', flexDirection:'column', flex: 1 , marginBottom: 10 }]}>
            <View style={{ flex: 1, height: waitFrom ? 80: 'auto', flexDirection: waitFrom ? 'column':'row', alignItems: waitFrom ? 'flex-start' : 'center', 
              borderColor: waitFrom ? COLORS.SILVER : COLORS.RED, 
              
              borderBottomWidth: waitFrom? 0 : 1, marginBottom: 5}}> 
              <Text style={{fontSize: METRICS.text_12, color: COLORS.LIGHT_GREY, paddingBottom: 0, marginBottom: 0 }}>{'BẮT ĐẦU'}</Text>
              <View style={{flex: waitFrom ? 0: 1 }}>
              <DropDownPicker
                listMode = "MODAL"
                style={{ 
                  borderColor: COLORS.SILVER, 
                  borderWidth: 0, 
                  borderBottomWidth: waitFrom?  1:0,       
                  // borderRadius: 8,
                }}
                textStyle={{fontWeight:"bold"}}
                open={openWaitFrom}
                value={waitFrom}
                items={listHourOfDay}
                maxHeight={300}
                setOpen={setOpenWaitFrom}
                setValue={setWaitFrom}
                placeholder=''
                placeholderStyle={{
                  color : COLORS.LIGHT_GREY,
                  fontWeight:'normal',
                  fontSize: METRICS.text_12
                }}
                multiple={false}
                modalTitle="Chọn số tiếng thời gian chờ bắt đầu"
                modalTitleStyle={{
                  fontSize: METRICS.text_16,
                  justifyContent: 'center',
                  fontWeight:"bold",
                  padding:0,
                  margin:0,
                  color: COLORS.WHITE,
                  alignSelf:"center",
                  borderColor: COLORS.SILVER, 
                }}
                modalAnimationType= 'slide'
                modalContentContainerStyle={{
                  backgroundColor: COLORS.MAIN_COLOR,
                }}

                listItemContainerStyle={{
                  backgroundColor: COLORS.WHITE,
                }}
              />
              </View>
            </View>
            {/* <FlexView flex={0.5} /> */}
            <View style={{ flex: 1, height: waitTo ? 80: 'auto', flexDirection: waitTo ? 'column':'row', alignItems: waitTo ? 'flex-start' : 'center', 
              borderColor: waitTo ? COLORS.SILVER : COLORS.RED, 
              borderBottomWidth: waitTo? 0 : 1, marginBottom: 5}}> 
              <Text style={{fontSize: METRICS.text_12, color: COLORS.LIGHT_GREY, paddingBottom: 0, marginBottom: 0 }}>{'KẾT THÚC'}</Text>
              <View style={{flex: waitTo ? 0: 1 }}>
              <DropDownPicker
                listMode = "MODAL"
                style={{ 
                  borderColor: COLORS.SILVER, 
                  borderWidth: 0, 
                  borderBottomWidth: waitTo?  1:0,       
                }}
                textStyle={{fontWeight:"bold"}}
                placeholderStyle={{
                  color : COLORS.LIGHT_GREY,
                  fontWeight:'normal',
                  fontSize: METRICS.text_12
                }}
                placeholder=''
                open={openWaitTo}
                value={waitTo}
                items={listHourOfDay}
                maxHeight={300}
                setOpen={setOpenWaitTo}
                setValue={setWaitTo}
                // placeholder='KẾT THÚC'
                multiple={false}
                modalTitle="Chọn số tiếng thời gian chờ kết thúc"
                modalTitleStyle={{
                  fontSize: METRICS.text_16,
                  justifyContent: 'center',
                  fontWeight:"bold",
                  padding:0,
                  margin:0,
                  color: COLORS.WHITE,
                  alignSelf:"center"
                }}
                modalAnimationType= 'slide'
                modalContentContainerStyle={{
                  backgroundColor: COLORS.MAIN_COLOR,
                }}
                listItemContainerStyle={{
                  backgroundColor: COLORS.WHITE,

                }}
              />
            </View>
            </View>
          </View>
    </View>
  }

  return (
    <BaseScreen>
      <Header
        backgroundColor={COLORS.MAIN_COLOR}
        title={'THIẾT LẬP NHANH MẺ LƯỚI'}
      />
      <DelayRenderComponent
        style={[
          globalStyles.flex1,
          {padding: setValue(5), backgroundColor: COLORS.WHITE},
        ]}>
          <ScrollView >
          <View style={[styles.view_header]}>
            <AppText
              customStyle={styles.txt_header}
              text={'KHOẢNG THỜI GIAN KHAI THÁC'}
            />
          </View>
          <View style={[styles.view_content,{height: 'auto', flexDirection:'column', marginBottom: 10 }]}>
          <View style={[globalStyles.flex1,{ width: '100%', height: 60, flexDirection:'row', alignItems:'center',
               borderBottomWidth: selectedDates.from ? 0 :1, borderColor: selectedDates.from ? COLORS.SILVER: COLORS.RED, paddingRight:15}]}>
              <TouchableOpacity
                style={{flex: 1}}
                activeOpacity={0.5}
                onPress={() => setModalVisible(true)}>
                <RenderTextDate
                  datetime={selectedDates.from}
                  placeholderText={'BẮT ĐẦU'}
                />
              </TouchableOpacity>
              <AppIcon
                  color={'#7a8691'}
                  name={"calendar"}
                  size={20}
                  type={IconType.antDesign}
                />
              
            </View>
            <View style={[globalStyles.flex1,{ width: '100%', height: 60, flexDirection:'row', alignItems:'center', 
              borderBottomWidth: selectedDates.to ? 0 :1, borderColor: selectedDates.to ? COLORS.SILVER: COLORS.RED, marginBottom: 10, paddingRight: 15 }]}>
            <TouchableOpacity
                style={{flex: 1}}
                activeOpacity={0.5}
                onPress={() => setModalVisible(true)}>
                <RenderTextDate
                  datetime={selectedDates.to}
                  placeholderText={'KẾT THÚC'}
                />
              </TouchableOpacity>
              <AppIcon
                  color={'#7a8691'}
                  name={"calendar"}
                  size={20}
                  type={IconType.antDesign}
                />
              
            </View>
          </View>
          <RenderByHour/>
          <View style={styles.view_header}>
            <AppText customStyle={styles.txt_header} text={'LOÀI KHAI THÁC'} />
            <AppIcon
              color={COLORS.BLUE_6C}
              name={'add-circle-outline'}
              size={40}
              type={'ionicons'}
              onPress={openListFish}
            />
          </View>
          <View style={{ borderWidth: 1, borderColor: COLORS.SILVER, paddingBottom: 150 }}>
            <View style={styles.bodyRow}>
              {listFish.map((fish, idx) => (
                <View key={idx ?? fish.fishId ?? fish.id} style={styles.box}>
                  <SelectFishRow
                    border={false}
                    end={idx === listFish?.length - 1}
                    remove={true}
                    style={styles.row}
                    typequick={true}
                    onRemove={() => onRemoveFish(fish._id)}
                    {...fish}
                  />
                </View>
              ))}
            </View>
          </View>
          <CalendarModal
            visible={modalVisible}
            onClose={() => setModalVisible(false)}
            onConfirm={handleConfirm}
          />
          {openReleaseTime.from && (
            <DateTimePickerModal
              isVisible={true}
              mode="time"
              onConfirm={(from)=>onConfirmRelease(from, 1)}
              onCancel={() => setOpenReleaseTime({...openReleaseTime, from: false})}
            />
          )}

          {openReleaseTime.to && (
            <DateTimePickerModal
            isVisible={true}
              mode="time"
              onConfirm={(from)=>onConfirmRelease(from, 2)}
              onCancel={() => setOpenReleaseTime({...openReleaseTime, to: false})}
            />
          )}
          {openRetrieveTime.from && (
            <DateTimePickerModal
              isVisible={true}
              mode="time"
              onConfirm={(to)=>onConfirmRetrieve(to, 1)}
              onCancel={() => setOpenRetrieveTime({...openRetrieveTime, from: false})}
            />
          )}
          {openRetrieveTime.to && (
            <DateTimePickerModal
              isVisible={true}
              mode="time"
              onConfirm={(to)=>onConfirmRetrieve(to, 2)}
              onCancel={() => setOpenRetrieveTime({...openRetrieveTime, to: false})}
            />
          )}
            {/* Loại theo giờ..... */}
          {
            openPickerTimeTpHour != 0  && <DateTimePickerModal
              isVisible={true}
              mode= { [7,8,'7','8'].includes(openPickerTimeTpHour)? "time" :"time"}
              onConfirm={(time)=>onConfirmTpHour(time)}
              onCancel={() => setOpenPickerTimeTpHour(0)}
            />
          }

          {/* {openRetrieveTime.to && (
            <DateTimePickerModal
              isVisible={true}
              mode="time"
              onConfirm={(to)=>onConfirmRetrieve(to, 2)}
              onCancel={() => setOpenRetrieveTime({...openRetrieveTime, to: false})}
            />
          )} */}
        </ScrollView>
        <AppButton
          type="secondary"
          text="HUỶ"
          onPress={() => {}}
          textRight="LƯU & TẠO"
          onPressLeft={onCancel}
          onPressRight={onSaveAndCreate}
          customStyle={{margin: setValue(16)}}
        />
      </DelayRenderComponent>
    </BaseScreen>
  );
};

const RenderTextDate = (itemProps: any) => {
  const {placeholderText, datetime, type} = itemProps;
  return (
    <View style={{margin: 2}}>
      <AppText
        customStyle={{fontSize: METRICS.text_12, color: COLORS.LIGHT_GREY}}
        text={placeholderText}
      />
      {datetime &&
        <View style={styles.txtDatetime}>
          {type == 'time' ? (
            <AppText customStyle={styles.txtTime} text={datetime} />
          ) : (
            <AppText
              customStyle={styles.txtTime}
              text={datetime ? `${moment(datetime).format('DD/MM/YYYY')}` : ''}
            />
          )}
        </View>
      }
    </View>
  );
};

const styles = StyleSheet.create({
  txtDatetime: {
    flexDirection: 'row',
    justifyContent:'space-between',
    alignItems: 'flex-end',
    borderBottomWidth: 1,
    borderColor: COLORS.SILVER, 
    marginBottom: 2,
  },
  txtDate: {
    fontSize: METRICS.text_20,
    padding: 0,
    // fontWeight: 'bold',
    color: COLORS.BLACK,
  },
  txtTime: {
    fontSize: METRICS.text_14,
    paddingBottom: 2,
    // fontWeight: 'bold',
    color: COLORS.BLACK,
  },
  dropdown: {
    margin: 16,
    width: 50,
    height: 30,
    borderWidth: 1,
    borderColor: COLORS.SILVER,
    borderRadius: 8,
    paddingRight: 14,
  },
  view_header: {
    backgroundColor: 'rgba(154,154,154,0.5)',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    padding: setValue(5),
    borderTopLeftRadius: 10, borderTopRightRadius: 10
  },
  view_content: {
    height: 100,
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    padding: setValue(5),
    borderWidth: 1, borderColor: COLORS.SILVER,
    zIndex: 999
  },
  view_content_50: {
    height: 50,
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    padding: setValue(5),
    borderWidth: 1,
    borderColor: COLORS.SILVER,

  },
  txt_header: {
    fontSize: METRICS.text_14,
    color: COLORS.BLUE_SECONDARY,
    fontWeight: 'normal',
  },
  txt_content: {
    fontSize: METRICS.text_14,
    color: COLORS.BLACK,
    fontWeight: 'normal',
  },
  bodyRow: {
    backgroundColor: COLORS.WHITE,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  box: {
    padding: setValue(5),
    width: '100%',
    borderBottomWidth: 1,
    borderColor: COLORS.SILVER,
  },
  row: {
    backgroundColor: COLORS.WHITE,
  },

  dropdownNganhnghe: {
    margin: 16,
    height: 50,
    borderBottomWidth: 1,
    borderColor: COLORS.SILVER,
  },
  imageStyle: {
    width: 24,
    height: 24,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
    marginLeft: 8,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
export default QuickCatchDiary;
