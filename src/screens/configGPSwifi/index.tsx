import CalendarModal from './CalendarViewModal';
import {useNetInfoInstance, useNetInfo} from '@react-native-community/netinfo';
import {useNavigation} from '@react-navigation/native';
import {COLORS} from 'assets';
import axios from 'axios';
import {DelayRenderComponent} from 'components';
import Header from 'components/Header';
import Loading from 'components/Loading';
import SelectDate from 'components/SelectDate';
import {AppButton, AppIcon, AppText, BaseScreen} from 'elements';
import {IconType} from 'elements/AppIcon';
import moment from 'moment';
import SCREENS from 'navigation/Screens';
import React, {
  useEffect,
  useState,
  useCallback,
  useMemo,
  memo,
  useRef,
} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Text,
  Modal,
} from 'react-native';
import {PermissionsAndroid, Platform} from 'react-native';
import {ALERT_TYPE, Toast} from 'react-native-alert-notification';
import {But<PERSON>} from 'react-native-elements';
import {ScrollView} from 'react-native-gesture-handler';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import * as Progress from 'react-native-progress';
import WifiManager from 'react-native-wifi-reborn';
import reactotron from 'reactotron-react-native';
import FishList from 'screens/catchDiary/components/FishList';
import {SelectFishRow} from 'screens/catchDiary/components/SelectFishRow';
import {apiServices} from 'services';
import useDiaryStore from 'stores/diaryStore';
import useFishStore from 'stores/fishStore';
import useStorageStore from 'stores/useStorageStore';
import {FONT_TYPE, globalStyles, METRICS} from 'utils';
import {DM_NGANHNGHE} from 'utils/dm_nganhnghe';
import {convertDMS} from 'utils/funcUtils';
import {setValue} from 'utils/layout';
import uuid from 'uuid-random';

// import Modal from 'react-native-modal';

async function getWifiSSID(): Promise<string | null> {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      {
        title: 'Quyền truy cập vị trí',
        message: 'Ứng dụng Hỗ trợ NKĐT cần quyền để truy cập tên Wi-Fi',
        buttonNeutral: 'Hỏi lại sau',
        buttonNegative: 'Từ chối',
        buttonPositive: 'Đồng ý',
      },
    );

    if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
      console.log('Quyền bị từ chối');
      return null;
    }
  }
  try {
    const ssid = await WifiManager.getCurrentWifiSSID();
    // console.log("Wi-Fi hiện tại:", ssid);
    return ssid;
  } catch (error) {
    // console.log("Không lấy được SSID", error);
    return null;
  }
}

const TIME_EXCUTE = 1 * 1000;
const ConfigGPSWifi = () => {
  const {navigate, goBack} = useNavigation();
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0); // từ 0 đến 1
  const [running1, setRunning] = useState(false);
  const running = useRef(false);

  const [ssid, setSsid] = useState('');
  const [mainDevice, setMainDevice] = useState(false);
  const [location, setlocation] = useState('');
  const useNetworkStatus = useNetInfoInstance();
  const {
    setDiaryLocal,
    diary: {diaryCurrent, diaryLocal = []},
  } = useDiaryStore();

  const { credential: {user} } = useStorageStore();
  useEffect(()=>{
    setMainDevice(user.id_tau_ca?.thietbigiamsat?.serial?.toUpperCase()  === useNetworkStatus?.netInfo?.details?.bssid?.toUpperCase()  )
    
  },[user, useNetworkStatus])
     
  const list =
    diaryCurrent?.thoi_gian_tha !== null
      ? [diaryCurrent, ...diaryLocal]?.sort(
          (a: any, b: any) =>
            new Date(a?.thoi_gian_tha).getTime() -
            new Date(b?.thoi_gian_tha).getTime(),
        )
      : diaryLocal;
  // Kiểm tra cấu hình kết nối

  // Danh sách mẻ lưới
  // LOG  useNetworkStatus:  {"netInfo": {"details": {"bssid": "02:00:00:00:00:00", "frequency": 5220, "ipAddress": "************", "isConnectionExpensive": false, "linkSpeed": 585, "rxLinkSpeed": -1, "strength": 83, "subnet": "*************", "txLinkSpeed": 585}, "isConnected": true, "isInternetReachable": true, "isWifiEnabled": true, "type": "wifi"}, "refresh": [Function anonymous]}
  // LOG  useNetworkStatus:  {"netInfo": {"details": {"bssid": "3c:8a:1f:08:37:dd", "frequency": 2412, "ipAddress": "***********", "isConnectionExpensive": false, "linkSpeed": 39, "rxLinkSpeed": -1, "ssid": "HMS_Technology_Ship", "strength": 61, "subnet": "*************", "txLinkSpeed": 39}, "isConnected": true, "isInternetReachable": false, "isWifiEnabled": true, "type": "wifi"}, "refresh": [Function anonymous]}
  useEffect(() => init(), []);
  // useEffect(()=>{
  //   console.log('useNetworkStatus: ', useNetworkStatus)

  // },[useNetworkStatus])

  const init = () => {
    setTimeout(() => {
      getWifiSSID().then(ssid => {
       
        setSsid(ssid ?? '');
      });
      getGPSInformation().then(rs => {
        console.log('getGPSInformation: ', rs);
      });
    }, 500);
  };

  const sendGPS = async () => {
    let config = {
      method: 'post',
      url: 'http://***********/request',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        messageId: 1,
        data: {id: 1, timestamp: '2025-06-21 21:30:30'},
      },
    };
    try {
      const rs = await axios.request(config);
      setTimeout(async () => {
        await getGPSResponse();
      }, 1000);
      // console.log('rs: ', rs.data);
    } catch (error) {
      console.log('error: ', error);
    }
  };

  const getGPSResponse = async () => {
    let config = {
      method: 'get',
      url: 'http://***********/response',
      headers: {
        'Content-Type': 'application/json',
      },
    };
    try {
      const rsResponse = await axios.request(config);
      return {
        data: rsResponse.data,
        status: rsResponse.status,
      };
    } catch (error) {
      console.log('error: ', error);
    }
  };

  const getGPSInformation = async () => {
    let config = {
      method: 'post',
      url: 'http://***********/request',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        messageId: '1',
        data: {id: null, timestamp: null},
      },
    };
    try {
      const rs = await axios.request(config);
      if (rs.status == 200) {
        const gpsInfo: any = await getGPSResponse();
        if (gpsInfo?.status == 200) {
          const lonlat = gpsInfo?.data?.ToaDo?.split(',') ?? [];
          if (
            lonlat.length > 1 &&
            lonlat.filter((ele: any) => Number(ele) > 0).length > 1
          ) {
            const pos_current = convertDMS(lonlat[0], lonlat[1]);
            setlocation(pos_current);
          }
        }
      }
    } catch (error) {
      console.log('error: ', error);
    }
  };

  /**
   *
   * @param type 1: gửi một mẻ | 2: gửi danh sách mẻ lưới
   * @param data danh sách mẻ lưới
   * @param  messageId: 1 | Lấy thông tin thiết bị: { id: null, timestamp: null }
   * @param  messageId: 2 | Lấy vị trí theo danh sách mẻ : [{ id: 1, timestamp: '2025-06-21 21:30:30' }]
   * @param  messageId: 3 | Lấy vị trí trong khoảng thời gian: { fromTime: '2025-06-21 21:30:30', toTime: '2025-06-21 21:30:30' }
   */
  const ExcuteGetGPS = async (type: number, data: any) => {
    // console.log('data: ', data[0]);
    // type: 1 là một mẻ | 2: danh sách mẻ lưới
    // Chú ý: Khi gửi tín hiệu phải chờ 15s mới có thể gửi tiếp
    setRunning(true);
    running.current = true;
    setProgress(0);
    if (type == 1) {
    } else if (type == 2) {
      let list_data = data.map((item: any) => {
        return {
          id: item.id,
          thoi_gian_tha: moment(item.thoi_gian_tha).format(
            'YYYY-MM-DD HH:mm:ss',
          ),
          thoi_gian_thu: moment(item.thoi_gian_thu).format(
            'YYYY-MM-DD HH:mm:ss',
          ),
        };
      });
      // thực hiện gửi danh sách mẻ lưới
      let ls_send = [];
      for (let i = 0; i < list_data.length; i = i + 7) {
        if (!running.current) {
          continue;
        }
        let id = 0;
        for (let j = i; j < i + 7; j++) {
          if (!running.current || list_data.length <= j) {
            continue;
          }
          setProgress(j / 2 / list_data.length);

          id++;
          if (list_data[j]) {
            ls_send.push({
              id: id.toString(),
              timestamp: list_data[j].thoi_gian_tha,
            });
            id++;
            ls_send.push({
              id: id.toString(),
              timestamp: list_data[j].thoi_gian_thu,
            });
          }
        }
        try {
          let config = {
            method: 'post',
            url: 'http://***********/request',
            headers: {
              'Content-Type': 'application/json',
            },
            data: {
              messageId: '2',
              data: ls_send,
            },
          };
          const result = await axios.request(config);
          if (result.status == 200) {
            await new Promise(resolve => setTimeout(resolve, TIME_EXCUTE));
            // thực hiện lấy dữ liệu trước đó
            const response = await getGPSResponse();
            if (response?.status == 200) {
              // const data_mockup = [
              //   {id: 1, timestamp: '2025-06-21 21:30:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 2, timestamp: '2025-06-21 21:32:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 3, timestamp: '2025-06-21 21:34:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 4, timestamp: '2025-06-21 21:36:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 5, timestamp: '2025-06-21 21:38:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 6, timestamp: '2025-06-21 21:40:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 7, timestamp: '2025-06-21 21:42:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 8, timestamp: '2025-06-21 21:44:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 9, timestamp: '2025-06-21 21:46:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 10, timestamp: '2025-06-21 21:48:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 11, timestamp: '2025-06-21 21:50:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 12, timestamp: '2025-06-21 21:52:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 13, timestamp: '2025-06-21 21:54:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              //   {id: 14, timestamp: '2025-06-21 21:56:30', serial: 0, lat: '20.834240', lon: '105.783936'},
              // ];
              const data_mockup = response.data?.data;
              if (data_mockup.length > 0) {
                let _id = 0;
                for (let j = i; j < i + 7; j++) {
                  if (list_data.length <= j) {
                    continue;
                  }
                  list_data[j] = {
                    ...data[j],
                    vi_tri_tha: [data_mockup[_id].lat, data_mockup[_id].lon],
                    vi_tri_thu: [
                      data_mockup[_id + 1].lat,
                      data_mockup[_id + 1].lon,
                    ],
                  };
                  _id = _id + 2;
                }
              }
            }
          }
        } catch (error) {}
        // thực hiện chờ  10s để thực hiện lấy dữ liệu mapp vào
        reactotron.log('ls_send: ' + i, ls_send);
      }
      reactotron.log('list_data: ', list_data);
      setDiaryLocal(list_data);
    }
    setProgress(1);
    running.current = false;
    setRunning(false);
    await new Promise(resolve => setTimeout(resolve, 1000));

    Alert.alert('Thông Báo', 'Cập nhật thành công', [
      {text: 'OK', onPress: () => goBack()},
    ]);
  };

  return (
    <BaseScreen>
      <Header
        backgroundColor={COLORS.MAIN_COLOR}
        title={'THIẾT LẬP CẬP NHẬT GPS/WIFI'}
        onPressTextRight={init}
        iconTextRight={'Làm mới'}
      />
      <Loading isLoading={loading} />
      <DelayRenderComponent
        style={[
          globalStyles.flex1,
          {padding: setValue(5), backgroundColor: COLORS.WHITE},
        ]}>
        <View style={{flex: 0.4, backgroundColor: COLORS.WHITE}}>
          <View style={styles.view_header}>
            <AppText
              customStyle={{fontWeight: 'bold'}}
              color={COLORS.BLACK}
              font="REGULAR"
              text={'Cấu hình kết nối'}
            />
          </View>
          <View
            style={[
              styles.view_content,
              {flex: 1, flexDirection: 'row', justifyContent: 'space-between'},
            ]}>
            <AppText
              customStyle={{width: '30%', fontWeight: 'bold'}}
              color={COLORS.BLACK}
              font="REGULAR"
              text={'Tên mạng: '}
            />
            <AppText color={COLORS.BLACK} font="REGULAR" text={ssid ?? ''} />
          </View>
          <View
            style={[
              styles.view_content,
              {flex: 1, flexDirection: 'row', justifyContent: 'space-between'},
            ]}>
            <AppText
              customStyle={{width: '30%', fontWeight: 'bold'}}
              color={COLORS.BLACK}
              font="REGULAR"
              text={'Vị trí hiện tại: '}
            />
            <AppText
              color={COLORS.BLACK}
              font="REGULAR"
              text={location ?? ''}
            />
          </View>
          <View
            style={[
              styles.view_content,
              {flex: 1, flexDirection: 'row', justifyContent: 'space-between'},
            ]}>
            <AppText
              customStyle={{width: '30%', fontWeight: 'bold'}}
              color={COLORS.BLACK}
              font="REGULAR"
              text={'IP: '}
            />
            <AppText
              color={COLORS.BLACK}
              font="REGULAR"
              text={useNetworkStatus?.netInfo?.details?.ipAddress ?? ''}
            />
          </View>
          <View
            style={[
              styles.view_content,
              {flex: 1, flexDirection: 'row', justifyContent: 'space-between'},
            ]}>
            <AppText
              customStyle={{width: '30%', fontWeight: 'bold'}}
              color={COLORS.BLACK}
              font="REGULAR"
              text={'Mac Address: '}
            />
            <AppText
              color={COLORS.BLACK}
              font="REGULAR"
              text={
                useNetworkStatus?.netInfo?.details?.bssid?.toUpperCase() ?? ''
              }
            />
          </View>
        </View>
        <ScrollView style={{flex: 1, backgroundColor: COLORS.WHITE}}>
          {
            mainDevice ? <FishList
            disable={true}
            data={list}
            onRemoveFish={() => {}}
            onChangeTime={() => {}}
            onUpdate={() => {}}
          /> :<AppText
                size={METRICS.text_20}
                color={COLORS.BLACK}
                font="REGULAR"
                customStyle={{
                  backgroundColor: COLORS.LEMON,
                  marginTop: setValue(50),
                  padding: setValue(30),
                  textAlign:'center',
                  fontWeight: 'bold'
                }}
                text={'Vui lòng kết nối đúng thiết bị đã đăng ký để sử dụng tính năng mới!'}
              />
          }
          
        </ScrollView>
        <Modal visible={running.current} transparent animationType="fade">
          <View style={styles.modalBackground}>
            <View style={styles.modalContent}>
              <Text style={{marginBottom: 10}}>Đang xử lý dữ liệu...</Text>
              <AppText
                color={COLORS.BLACK}
                font="REGULAR"
                text={'Đang xử lý dữ liệu...'}
              />
              <Progress.Bar
                progress={progress}
                width={250}
                height={20}
                color={COLORS.MAIN_COLOR}
                style={{marginVertical: 10}}
              />
              <AppText
                color={COLORS.BLACK}
                font="REGULAR"
                text={(progress * 100).toFixed(0) + '%'}
              />
              <Button
                title="Ngưng"
                type="outline"
                onPress={() => {
                  running.current = false;
                  setProgress(0);
                  setRunning(false);
                }}
                buttonStyle={{
                  marginTop: 10,
                  borderRadius: 20,
                  width: 140,
                  borderColor: COLORS.MAIN_COLOR,
                  borderWidth: 1,
                }}
                titleStyle={{color: COLORS.MAIN_COLOR}}
              />
            </View>
          </View>
        </Modal>

          {
            mainDevice ? (<AppButton
              type="secondary"
              text="HUỶ"
              onPress={() => {}}
              textRight="Cập nhật GPS"
              onPressLeft={() => goBack()}
              onPressRight={() => ExcuteGetGPS(2, list)}
              customStyle={{margin: setValue(16)}}
            /> ): 
              (<AppButton
              text="Quay lại"
                onPress={() => {goBack()}}
                customStyle={{
                  margin: setValue(16),
                  // backgroundColor:   'rgba(158, 131, 129, 0.5)',
                  backgroundColor: COLORS.MAIN_COLOR,
                }}
              />)
          }
        
      </DelayRenderComponent>
    </BaseScreen>
  );
};

const styles = StyleSheet.create({
  view_header: {
    backgroundColor: 'rgba(154,154,154,0.5)',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    padding: setValue(5),
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  view_content: {
    height: 40,
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    padding: setValue(5),
    borderWidth: 0.5,
    borderColor: COLORS.SILVER,
    zIndex: 999,
  },

  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
});
export default ConfigGPSWifi;
