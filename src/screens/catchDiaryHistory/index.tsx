import IMAGES from '../../assets/images';
import COLORS from '../../assets/theme/colors';
import {Content, DelayRenderComponent, Header} from '../../components';
import SelectDate from '../../components/SelectDate';
import {
  AppButton,
  AppImage,
  BaseScreen,
  FlexView,
  SizeBox,
} from '../../elements';
import {globalStyles, METRICS} from '../../utils';
import {useNavigation} from '@react-navigation/native';
import React from 'react';

export const CatchDiaryHistory = () => {
  const {goBack} = useNavigation();
  return (
    <BaseScreen>
      <Header
        backgroundColor={COLORS.MAIN_COLOR}
        title={'TRA CỨU LỊCH TRÌNH'}
      />
      <Content>
        <DelayRenderComponent
          style={[globalStyles.flex1, {padding: METRICS.nomal}]}>
          <SelectDate
            editable={true}
            mode="date"
            placeholderText={'NGUỒN DỮ LIỆU'}
            shadow={false}
            value={new Date()}
            onSubmit={() => {}}
          />
          <SizeBox h={16} />
          <SelectDate
            editable={true}
            mode="date"
            placeholderText={'TỪ NGÀY'}
            shadow={false}
            value={new Date()}
            onSubmit={() => {}}
          />
          <SizeBox h={16} />
          <SelectDate
            editable={false}
            mode="date"
            placeholderText={'ĐẾN NGÀY'}
            shadow={false}
            value={new Date()}
            onSubmit={() => {}}
          />
          <AppImage
            customStyle={{width: 400, height: 400, alignSelf: 'center'}}
            src={IMAGES.IMG_MAP_VIEW}
          />
          <FlexView flex={1} />
          <AppButton text="Xem chi tiết" onPress={() => goBack()} />
        </DelayRenderComponent>
      </Content>
    </BaseScreen>
  );
};

export default CatchDiaryHistory;
