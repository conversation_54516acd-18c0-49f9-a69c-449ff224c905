import COLORS from '../../../assets/theme/colors';
import {BadgeButton} from '../../../components';
import {IconType} from '../../../elements/AppIcon';
import StatefulComponent from '../../../lib/StatefulComponent';
import {globalStyles} from '../../../utils';
import {
  footerHeight,
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  setValue,
} from '../../../utils/layout';
import {SelectFishRow} from './SelectFishRow';
import {get, isEmpty} from 'lodash';
import React from 'react';
import {FlatList, StyleSheet, Text, TouchableOpacity, View} from 'react-native';

const SectionHeader = React.memo(function (props) {
  const retrieveTime = get(props, 'retrieveTime');
  const onPress = get(props, 'onPress', () => {});
  return (
    <View style={styles.sectionHeader}>
      <View style={styles.sectionHeaderRow}>
        <Text
          style={styles.sectionName}>{`${'MẺ LƯỚI DD/MM/YYYY HH:mm'}`}</Text>
        <Text style={styles.badge}>{'TỔNG'}</Text>
      </View>
      <View style={styles.sectionFooterRow}>
        <Text style={styles.info}>{'THẢ LƯỚI'}</Text>
        <Text style={styles.info}>{'asdas'}</Text>
      </View>
      {retrieveTime ? (
        <View style={styles.sectionFooterRow}>
          <Text style={styles.info}>{'THU LƯỚI'}</Text>
          <Text style={styles.info}>asd</Text>
        </View>
      ) : (
        <View style={styles.updateContainer}>
          <BadgeButton
            name="angle-double-up"
            size={setValue(30)}
            title={'THU LƯỚI'}
            type={IconType.fontawesome}
            onPress={onPress(props.id, true)}
          />
        </View>
      )}
      {props?.note ? (
        <View style={styles.sectionFooterRow}>
          <Text style={styles.info}>{'GHI CHÚ'}</Text>
        </View>
      ) : null}
    </View>
  );
});

export const CatchRow = React.memo(({item, data, onUpdate, onRemoveFish}) => {
  return (
    <TouchableOpacity
      style={[styles.rowWrapper]}
      onPress={onUpdate(item.id, false, item)}>
      <SectionHeader
        disabled={get(item, 'disabled')}
        id={item.id}
        note={item?.note}
        releaseLocation={get(item, 'releaseLocation')}
        releaseTime={get(item, 'releaseTime')}
        retrieveLocation={get(item, 'retrieveLocation')}
        retrieveTime={get(item, 'retrieveTime')}
        total={get(item, 'total')}
        onPress={onUpdate}
      />
      <View style={styles.bodyRow}>
        {data?.map((fish, idx) => (
          <View key={fish.fishId} style={styles.box}>
            <SelectFishRow
              border={false}
              end={idx === data?.length - 1}
              remove={false}
              style={styles.row}
              onRemove={onRemoveFish(fish.fishId, item.id)}
              {...fish}
            />
          </View>
        ))}
      </View>
    </TouchableOpacity>
  );
});

export default class FishList extends StatefulComponent {
  static defaultProps = {
    showArrow: true,
    onUpdate: () => {},
  };
  keyExtractor = (item, index) => `${item.id}`;

  renderItem = ({item, index}) => {
    const data = get(item, 'data', []);
    const onRemoveFish = get(this.props, 'onRemoveFish', () => {});
    return (
      <CatchRow
        data={data}
        item={item}
        onRemoveFish={onRemoveFish}
        onUpdate={this.props?.onUpdate}
      />
    );
  };
  renderSectionHeader = ({section}) => {
    return (
      <SectionHeader
        editable={false}
        id={section.id}
        name={'MẺ LƯỚI'}
        releaseLocation={get(section, 'releaseLocation')}
        releaseTime={get(section, 'releaseTime')}
        retrieveLocation={get(section, 'retrieveLocation')}
        retrieveTime={get(section, 'retrieveTime')}
        total={get(section, 'total')}
        onPress={this.props.onUpdate}
      />
    );
  };

  render() {
    const data = [1, 2, 3, 4];
    if (isEmpty(data) || this.props.type === 1) {
      return (
        <View style={[styles.isEmpty]}>
          <Text style={styles.emptyString}>{'Chưa có dữ liệu'}</Text>
        </View>
      );
    }
    return (
      <FlatList
        contentContainerStyle={styles.content}
        data={data}
        keyExtractor={this.keyExtractor}
        renderItem={this.renderItem}
        renderSectionHeader={this.renderSectionHeader}
        stickySectionHeadersEnabled={false}
        style={styles.flatList}
      />
    );
  }
}

const styles = StyleSheet.create({
  badge: {
    backgroundColor: COLORS.WHITE,
    ...globalStyles.textSubHeading,
    borderRadius: 5,
    color: COLORS.SECONDARY,
    marginLeft: scaleBaseOnScreenWidth(5),
    overflow: 'hidden',
    paddingHorizontal: scaleBaseOnScreenWidth(5),
  },
  bodyRow: {
    backgroundColor: COLORS.LIGHT_SILVER,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  box: {
    width: '50%',
  },
  content: {
    paddingBottom: scaleBaseOnScreenHeight(80),
    paddingTop: scaleBaseOnScreenHeight(10),
  },
  emptyString: {
    ...globalStyles.textHeading,
  },
  flatList: {
    flex: 1,
    paddingHorizontal: scaleBaseOnScreenWidth(5),
  },
  info: {
    ...globalStyles.textSubHeading,
    color: COLORS.BITTER_GRAY,
  },
  isEmpty: {
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: 4,
    flex: 1,
    justifyContent: 'center',
    marginBottom: scaleBaseOnScreenWidth(5) + footerHeight,
    marginHorizontal: scaleBaseOnScreenWidth(5),
  },
  row: {},
  rowWrapper: {
    borderRadius: 4,
    overflow: 'hidden',
  },
  sectionFooterRow: {
    alignItems: 'center',
    backgroundColor: COLORS.LIGHT_SILVER,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: scaleBaseOnScreenWidth(10),
    paddingVertical: scaleBaseOnScreenHeight(5),
  },
  sectionHeader: {
    backgroundColor: COLORS.LIGHT_SILVER,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    marginTop: scaleBaseOnScreenHeight(5),
    overflow: 'hidden',
  },
  sectionHeaderRow: {
    alignItems: 'center',
    backgroundColor: COLORS.BITTER_GRAY,
    flex: 1,
    flexDirection: 'row',
    height: scaleBaseOnScreenHeight(40),
    paddingHorizontal: scaleBaseOnScreenWidth(10),
  },
  sectionName: {
    lineHeight: scaleBaseOnScreenHeight(25),
    ...globalStyles.textButton,
    color: COLORS.WHITE,
  },
  updateContainer: {
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderBottomLeftRadius: setValue(5),
    borderBottomRightRadius: setValue(5),
    paddingVertical: scaleBaseOnScreenHeight(20),
  },
});
