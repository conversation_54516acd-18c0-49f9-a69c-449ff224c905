import COLORS from '../../../assets/theme/colors';
import {AppIcon, AppImage} from '../../../elements';
import {IconType} from '../../../elements/AppIcon';
import {globalStyles} from '../../../utils';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  setValue,
} from '../../../utils/layout';
import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

export const SelectFishRow = ({openImage, border, remove, ...props}) => {
  return (
    <View style={[styles.row, props.style]}>
      <TouchableOpacity onPress={openImage}>
        <AppImage resizeMode="contain" src={{uri: ''}} style={styles.image} />
      </TouchableOpacity>
      <TouchableOpacity
        activeOpacity={1}
        disabled={props?.disabled}
        style={[styles.rowContent, !border && styles.noBorder]}
        onPress={props.onPress}>
        <View style={styles.infoContent}>
          <Text style={styles.name}>{'asdasd'}</Text>
          <Text style={styles.weight}>asdads</Text>
        </View>
      </TouchableOpacity>
      {remove && (
        <TouchableOpacity
          disabled={props?.disabled}
          style={styles.chevron}
          onPress={() => props.onRemove(props.fishId)}>
          <AppIcon
            color={COLORS.LIGHT_GREY}
            name="closecircleo"
            size={scaleBaseOnScreenWidth(30)}
            type={IconType.antDesign}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  chevron: {
    alignSelf: 'center',
    color: COLORS.LIGHT_GREY,
    fontSize: scaleBaseOnScreenWidth(25),
    marginRight: scaleBaseOnScreenWidth(5),
    opacity: 0.7,
  },
  image: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderColor: COLORS.SILVER,
    borderRadius: setValue(30),
    borderWidth: 1,
    height: setValue(60),
    justifyContent: 'center',
    width: setValue(60),
  },
  infoContent: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: scaleBaseOnScreenHeight(8),
  },
  name: {
    ...globalStyles.textButton,
    color: COLORS.GREY,
  },
  noBorder: {
    borderBottomWidth: 0,
  },
  row: {
    alignItems: 'center',
    backgroundColor: COLORS.LIGHT_SILVER,
    flexDirection: 'row',
    height: scaleBaseOnScreenHeight(72),
    marginBottom: 0.5,
    paddingLeft: scaleBaseOnScreenWidth(5),
  },
  rowContent: {
    borderBottomWidth: 0.3,
    borderColor: COLORS.LIGHT_SILVER,
    flexDirection: 'row',
    flex: 1,
    height: '100%',
    marginLeft: scaleBaseOnScreenWidth(12),
  },
  shortName: {
    fontSize: scaleBaseOnScreenWidth(16),
  },
  weight: {
    ...globalStyles.textLabel,
    color: COLORS.BITTER_GRAY,
    fontSize: scaleBaseOnScreenWidth(13),
  },
});
