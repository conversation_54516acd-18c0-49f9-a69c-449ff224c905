import {useNavigation} from '@react-navigation/native';
// import { Screen } from 'lib';
import {BadgeButton, DelayRenderComponent} from 'components';
import React from 'react';
import {StyleSheet} from 'react-native';
import BarcodeMask from 'react-native-barcode-mask';
import {RNCamera} from 'react-native-camera';
import {
  statusBarHeight,
  globalStyles,
  setValue,
  scaleBaseOnScreenWidth,
} from 'utils/layout';

const ScanCode = props => {
  const navigation = useNavigation();
  const onBarCodeRead = value => {
    try {
      if (value.data && props.route?.params?.callback) {
        const json = JSON.parse(value?.data);
        if (json?.vesselName) {
          const arr = json?.vesselName?.split('-');
          if (arr.length > 0) {
            const final = {
              ...json,
              province: arr[0],
              username: arr[1],
            };
            props?.route?.params.callback(final);
            navigation.goBack();
            return;
          }
        }
        props?.route?.params.callback(json);
        navigation.goBack();
      }
    } catch (error) {}
  };

  return (
    <DelayRenderComponent style={styles.container}>
      <RNCamera
        style={styles.container}
        captureAudio={false}
        aspect={1}
        onBarCodeRead={onBarCodeRead}>
        <DelayRenderComponent style={{flex: 1}}>
          <BarcodeMask />
        </DelayRenderComponent>
      </RNCamera>
      <BadgeButton
        name="leftcircleo"
        type={'antDesign'}
        iconSize={setValue(25)}
        onPress={() => {
          navigation.goBack();
        }}
        size={setValue(40)}
        style={styles.x}
      />
    </DelayRenderComponent>
  );
};

export default ScanCode;
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  x: {
    position: 'absolute',
    left: scaleBaseOnScreenWidth(20),
    top: scaleBaseOnScreenWidth(10) + statusBarHeight,
  },
});
