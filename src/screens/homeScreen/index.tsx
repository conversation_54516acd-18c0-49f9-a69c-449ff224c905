import {useNetInfoInstance} from '@react-native-community/netinfo';
import {useNavigation} from '@react-navigation/native';
import {COLORS, ICONS, IMAGES} from 'assets';
import {
  AppButton,
  AppImage,
  AppText,
  BaseScreen,
  FlexView,
  SizeBox,
} from 'elements';
import {isEmpty, isNaN} from 'lodash';
import moment from 'moment';
import SCREENS from 'navigation/Screens';
import React, {useEffect, useRef, useState} from 'react';
import {
  RefreshControl,
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View,
  Platform,
  Alert,
} from 'react-native';
import {ALERT_TYPE, Dialog, Toast} from 'react-native-alert-notification';
import DeviceInfo from 'react-native-device-info';
import {Source} from 'react-native-fast-image';
import reactotron from 'reactotron-react-native';
import {apiServices} from 'services';
import useAppStore from 'stores/appStore';
import useDiaryStore from 'stores/diaryStore';
import useFishStore from 'stores/fishStore';
import useStorageStore from 'stores/useStorageStore';
import {FONT_TYPE, METRICS} from 'utils';
import {formatNumber} from 'utils/dateUtils';
import {
  scaleBaseOnScreenWidth,
  screenHeight,
  screenWidth,
  scaleBaseOnScreenHeight,
  setValue,
} from 'utils/layout';
import uuid from 'uuid-random';

const MenuHome = ({
  text,
  text2,
  text3,
  data,
  icon,
  disabled,
  onPress,
}: {
  text: string;
  text2?: string | null | undefined;
  text3?: string | null | undefined;
  data?: any;
  disabled?: number;
  icon: number | Source | undefined;
  onPress: () => void;
}) => {
  const totalDiary = useRef({
    local: 0,
    total: 0,
  });
  const {
    fetchDiary,
    setLastDiary,
    diary: {diaryCurrent, diaryLocal, diaryServer, lastDiary},
  } = useDiaryStore();
  const {
    credential: {user},
    onClearnStorage,
  } = useStorageStore();
  const [loading, setLoading] = useState(false);
  const {showLoading, hideLoading} = useAppStore();
  const {
    netInfo: {isConnected, isInternetReachable},
  } = useNetInfoInstance();

  useEffect(() => {
    if (isConnected && isInternetReachable) {
      checkStatDiary();
      setTimeout(() => {
        InitPageMenuHome();
      }, 1000);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);
  useEffect(() => {
    reload();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [diaryServer, diaryLocal, diaryCurrent]);
  const reload = () => {
    const current = diaryCurrent?.thoi_gian_tha !== null ? 1 : 0;
    const local =
      diaryLocal?.filter((ele: any) => !ele?.is_online)?.length + current;
    const online = diaryLocal?.filter((ele: any) => ele?.is_online)?.length;
    const total = online + local;
    totalDiary.current = {
      local: local,
      total: total,
    };
    setLoading(!loading);
  };

  const InitPageMenuHome = () => {
    showLoading(true);
    const id_tau_ca = user?.id_tau_ca?._id;
    apiServices
      .Post({
        url: '/token/nk_khaithac',
        body: {
          filter: {
            id_tau_ca: id_tau_ca,
            trang_thai: [0, 1, 2, 3, 4],
          },
          sort: {
            createdAt: -1,
          },
          limit: 500,
        },
      })
      .then(data => {
        fetchDiary(data, false, user);
      })
      .catch((error: any) => {
        if (error == 'logout') {
          Toast.show({
            type: ALERT_TYPE.DANGER,
            title: 'Thông báo',
            textBody: 'Phiên đăng nhập hết hiệu lực. Vui lòng đăng nhập lại',
          });
          setTimeout(() => {
            onClearnStorage();
          }, 3000);
          return;
        }
      })
      .finally(() => {
        hideLoading();
      });
  };

  const checkStatDiary = async () => {
    const id_tau_ca = user?.id_tau_ca?._id;
    const result = await new Promise((resolve, reject) => {
      apiServices
        .Post({
          url: '/token/nk_truyxuat_status',
          body: {
            id_tau_ca: id_tau_ca,
          },
        })
        .then((res: any) => {
          if (res.status == 'success' && res?.data?.length > 0) {
            let start_diary = false;
            if (lastDiary?.thoi_gian_cap_cang != null) {
              start_diary =
                lastDiary?.thoi_gian_cap_cang !=
                res.data[0]?.thoi_gian_cap_cang;
            }
            setLastDiary({
              ...lastDiary,
              ...res.data[0],
              start_diary: start_diary,
            });
            resolve(true);
          } else {
            resolve(false);
          }
        })
        .catch((error: any) => {
          console.log('error: ', error);
          resolve(false);
        });
    });
    return result;
  };

  return (
    <TouchableOpacity
      activeOpacity={disabled ? 0.4 : 1}
      style={{
        ...styles.menuContainer,
        opacity: disabled == 1 ? 1 : disabled == 2 ? 0.4 : 1,
      }}
      onPress={onPress}>
      <View style={styles.buttonStyle}>
        <AppImage customStyle={styles.menuImage} src={icon} />
      </View>
      <SizeBox h={METRICS.small_8} />
      <AppText
        customStyle={styles.menuText}
        font={FONT_TYPE.BOLD}
        size={14}
        text={text}
      />
      {text2 && (
        <View
          style={{
            justifyContent: 'center',
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <AppText
            customStyle={styles.menuText}
            font={FONT_TYPE.BOLD}
            size={14}
            text={text3}
          />
          {text2?.trim() && (
            <AppText
              customStyle={styles.menuText}
              font={FONT_TYPE.BOLD}
              size={14}
              text={', '}
            />
          )}

          <AppText
            customStyle={styles.menuText}
            font={FONT_TYPE.BOLD}
            size={12}
            text={text2}
          />
        </View>
      )}
      {data && (
        <AppText
          customStyle={styles.menuText}
          font={FONT_TYPE.BOLD}
          size={14}
          text={`(${totalDiary.current?.local}/${totalDiary.current?.total})`}
          data={totalDiary.current}
        />
      )}
    </TouchableOpacity>
  );
};

const HomeScreen = (props: any) => {
  const {navigate} = useNavigation();
  const {
    netInfo: {isConnected, isInternetReachable},
  } = useNetInfoInstance();
  const {showLoading, hideLoading} = useAppStore();

  const {
    clearDiary,
    setDiary,
    getDiary,
    fetchDiary,
    diary: {diaryCurrent, diaryLocal, diaryServer, lastDiary},
  } = useDiaryStore();

  const [totalFish, setTotalFish] = useState<any>([]);
  const [totalWeight, setTotalWeight] = useState(0);
  const [load, setLoad] = useState(false);
  // diary
  const [createDiary, setCreateDiary] = useState(
    diaryCurrent?.thoi_gian_tha !== null,
  );
  // fish
  const {setListFish, fishData, setFavoriteArr} = useFishStore();
  // user
  const {
    credential: {user, token},
  } = useStorageStore();
  const {getCredential, credential} = useStorageStore();

  const {getFish} = useFishStore();
  useEffect(() => {
    getCredential();
    getDiary(credential?.user?._id ?? '');
    getFish(credential?.user?._id ?? '');
  }, []);
  const initHome = props?.route?.params?.init;
  useEffect(() => {
    showLoading(true);
    if (token && isConnected && isInternetReachable) {
      getFishServer();
      InitPage();
      formatDataView();
      showLoading(false);
      setLoad(!load);
    } else {
      formatDataView();
      showLoading(false);
      setLoad(!load);
    }
  }, [initHome]);

  useEffect(() => {
    setCreateDiary(diaryCurrent?.thoi_gian_tha !== null);
  }, [diaryCurrent]);
  useEffect(() => {
    formatDataView();
  }, [diaryCurrent, diaryLocal, diaryServer]);

  // gọi api lấy danh sách nhóm loài
  const onCreateDiary = () => {
    // TODO:
    if (lastDiary.thoi_gian_boc_do) {
      Alert.alert(
        'Thông báo',
        'Tàu chưa xuất bến nên chưa được phép dùng tính năng này!',
      );
      return;
    }

    if (diaryCurrent?.thoi_gian_tha !== null) {
      // Dialog.show({
      //   type: ALERT_TYPE.WARNING,
      //   title: 'Thông báo',
      //   textBody: 'Bạn có muốn HUỶ THẢ LƯỚI?',
      //   button: 'Đồng ý',
      //   onPressButton() {
      //     clearDiary();
      //     setCreateDiary(false);
      //     Dialog.hide();
      //   },
      // });

      Alert.alert('Thông báo', 'Bạn có muốn HUỶ THẢ LƯỚI?', [
        {
          text: 'Huỷ',
          onPress: () => {},
        },
        {
          text: 'Đồng ý',
          onPress: () => {
            clearDiary();
            setCreateDiary(false);
          },
        },
      ]);
      return;
    }
    setCreateDiary(true);
    const id_item = uuid();

    setDiary({
      trang_thai: 1,
      id: id_item,
      item_id: id_item,
      thoi_gian_tha: new Date(),
      san_luong:
        fishData
          ?.filter((ele: any) => ele?.isFavorite)
          ?.map((ele: any) => ({...ele, khoi_luong: '0'})) || [],
    });
  };

  // GỌI API LẤY DANH SÁCH CÁ
  const getFishServer = () => {
    showLoading(true);
    apiServices
      .Post({
        url: '/token/dm_nhomloai',
        body: {
          sort: {
            createdAt: -1,
          },
          limit: 2000,
        },
      })
      .then((data: any) => {
        if (data?.count > 0) {
          const id_tinh_thanh = credential?.user?.id_tinh_thanh?._id;
          const ids: any[] = fishData
            ?.filter((ele: any) => ele?.isFavorite)
            .map((ele: any) => ele?._id);
          const list = data?.data?.map((ele: any) => ({
            ...ele,
            ten_dia_phuong:
              ele?.ten_dia_phuong[id_tinh_thanh] ?? ele?.ten_dia_phuong_tc,
            ten_dia_phuong_tc: ele?.ten_dia_phuong_tc,
            isFavorite: false,
            id_nhom_loai: ele._id,
            id: ele?._id,
            khoi_luong_thap_toi_thieu: ele?.khoi_luong_thap_toi_thieu ?? 30,
            khoi_luong_thap_trung_binh: ele?.khoi_luong_thap_trung_binh ?? 45,
            khoi_luong_thap_cao: ele?.khoi_luong_thap_cao ?? 70,
            khoi_luong_cao: ele?.khoi_luong_cao ?? 0,
          }));
          const listUnfav = list.filter((ele: any) => !ids?.includes(ele?._id));
          const listfav = list
            .filter((ele: any) => ids?.includes(ele?._id))
            ?.map((ele: any) => ({...ele, isFavorite: true}));
          setListFish(listfav.concat(listUnfav));
        }
        // gọi API cập nhật danh sách loài ưa thích
        pullFishServer().catch((error: any) => {
          // console.log('error: ', error);
        });
      })
      .catch((error: any) => {
        // console.log('error: ', error);
      })
      .finally(() => {
        hideLoading();
      });
  };

  // GỌI API LOÀI ƯU THÍCH
  const pullFishServer: any = async () => {
    const res: any = await apiServices.Get({url: '/token/catchconfig'});
    if (res?.status == 'success') {
      const {id_nhom_loais = []} = res.data || {};
      setFavoriteArr(id_nhom_loais);
    }
  };

  // GỌI API LẤY NK KHAI THÁC
  const InitPage = () => {
    showLoading(true);
    const id_tau_ca = user?.id_tau_ca?._id;
    apiServices
      .Post({
        url: '/token/nk_khaithac',
        body: {
          filter: {
            id_tau_ca: id_tau_ca,
            trang_thai: [0, 1, 2, 3, 4],
          },
          sort: {
            createdAt: -1,
          },
          limit: 500,
        },
      })
      .then(data => {
        reactotron.log('GỌI API LẤY NK KHAI THÁC >>>> data: ', data);
        fetchDiary(data, false, user);
      })
      .catch((error: any) => {
        // console.log('error: ', error);
      })
      .finally(() => {
        hideLoading();
      });
  };

  const formatDataView = () => {
    let listFishDiary: any = [];

    // for (let i = 0; i < diaryServer.length; i++) {
    //   let san_luong = diaryServer[i]?.san_luong;
    //   if (san_luong?.length > 0) {
    //     for (let j = 0; j < san_luong.length; j++) {
    //       const check = listFishDiary?.filter(
    //         (ele: any) => ele?._id === san_luong[j]?._id,
    //       )?.length;
    //       if (check > 0) {
    //         listFishDiary = listFishDiary?.map((ele: any) => {
    //           if (ele?._id === san_luong[j]?._id) {
    //             return {
    //               ...ele,
    //               khoi_luong:
    //                 Number(san_luong[j]?.khoi_luong) + Number(ele?.khoi_luong),
    //             };
    //           }
    //           return ele;
    //         });
    //       } else {
    //         listFishDiary.push(san_luong[j]);
    //       }
    //     }
    //   }
    // }

    for (let i = 0; i < diaryLocal?.length; i++) {
      let san_luong = diaryLocal[i].san_luong;
      if (san_luong?.length > 0) {
        for (let j = 0; j < san_luong.length; j++) {
          const check =
            listFishDiary?.filter(
              (ele: any) =>
                (ele?._id &&
                  san_luong[j]?.id_nhom_loai &&
                  ele?._id === san_luong[j]?.id_nhom_loai) ||
                (ele?._id === san_luong[j]?.id_nhom_loai &&
                  ele?.id === san_luong[j]?.id_nhom_loai),
            ) || [];
          if (check.length > 0) {
            listFishDiary = listFishDiary?.map((ele: any) => {
              if (
                (ele?.id_nhom_loai &&
                  san_luong[j]?.id_nhom_loai &&
                  ele?._id === san_luong[j]?.id_nhom_loai) ||
                (ele?.id_nhom_loai === san_luong[j]?.id_nhom_loai &&
                  ele?.id === san_luong[j]?.id_nhom_loai)
              ) {
                // console.log('----------- TRÙNG ------------', ele?._id, listFishDiary.length);
                return {
                  ...ele,
                  khoi_luong:
                    Number(san_luong[j]?.khoi_luong?.toString()) +
                    Number(ele?.khoi_luong?.toString()),
                };
              }
              return ele;
            });
          } else {
            listFishDiary.push(san_luong[j]);
          }
        }
      }
    }
    if (
      diaryCurrent?.thoi_gian_tha !== null &&
      diaryCurrent?.san_luong?.length > 0
    ) {
      let san_luong = diaryCurrent?.san_luong;
      if (san_luong?.length > 0) {
        for (let j = 0; j < san_luong.length; j++) {
          const check =
            listFishDiary?.filter(
              (ele: any) =>
                (ele?.id_nhom_loai &&
                  san_luong[j]?.id_nhom_loai &&
                  ele?.id_nhom_loai === san_luong[j]?.id_nhom_loai) ||
                ele?.id_nhom_loai === san_luong[j]?.id_nhom_loai ||
                ele?.id_nhom_loai === san_luong[j]?.id_nhom_loai,
            ) || [];
          if (check.length > 0) {
            listFishDiary = listFishDiary?.map((ele: any) => {
              if (
                (ele?.id_nhom_loai &&
                  san_luong[j]?.id_nhom_loai &&
                  ele?.id_nhom_loai === san_luong[j]?.id_nhom_loai) ||
                ele?.id_nhom_loai === san_luong[j]?.id_nhom_loai ||
                ele?.id === san_luong[j]?.id_nhom_loai
              ) {
                return {
                  ...ele,
                  khoi_luong:
                    Number(san_luong[j]?.khoi_luong?.toString()) +
                    Number(ele?.khoi_luong?.toString()),
                };
              }
              return ele;
            });
          } else {
            listFishDiary.push(san_luong[j]);
          }
        }
      }
    }
    let lsRow = [];
    let row = [];
    let total = 0;
    for (let i = 0; i < listFishDiary.length; i++) {
      if (
        !isNaN(total) &&
        !isNaN(Number(listFishDiary[i]?.khoi_luong?.toString()))
      ) {
        total =
          Number(total) + Number(listFishDiary[i]?.khoi_luong?.toString());
      }
      if (i + 1 === listFishDiary.length && (i + 1) % 2 === 1) {
        row.push(listFishDiary[i]);
        lsRow.push(row);
      } else {
        if (i % 2 === 1) {
          row.push(listFishDiary[i]);
          lsRow.push(row);
          row = [];
        } else {
          row.push(listFishDiary[i]);
        }
      }
    }
    setTotalWeight(total);
    setTotalFish(lsRow);
  };
  return (
    <BaseScreen edges={[]}>
      {totalFish?.length > 0 ? (
        <TabViewFish
          data={totalFish}
          totalWeight={totalWeight}
          formatDataView={formatDataView}
        />
      ) : (
        <View style={styles.viewContainer}>
          <AppImage customStyle={styles.logo} src={IMAGES.IMG_DIARY} />
          <SizeBox h={METRICS.nomal} />
          <AppText
            customStyle={styles.homeTitle}
            font={FONT_TYPE.BOLD}
            size={METRICS.text_18}
            text="HỖ TRỢ NHẬT KÝ ĐIỆN TỬ"
          />
          <SizeBox h={METRICS.nomal} />
          <TouchableOpacity
            style={[styles.btnShowQR, {width: scaleBaseOnScreenWidth(180)}]}
            onPress={() => navigate(SCREENS.INFO_VERSEL as never)}>
            <View style={styles.showQrIcon}>
              <AppImage customStyle={styles.qrIcon} src={ICONS.IC_SHIP_SMALL} />
            </View>
            <SizeBox w={METRICS.small_8} />
            <AppText
              color={COLORS.WHITE}
              font={FONT_TYPE.BOLD}
              text={user?.id_tau_ca?.so_dang_ky?.toUpperCase() || 'xx-xxxx-xx'}
            />
          </TouchableOpacity>
        </View>
      )}

      <View
        style={{
          paddingHorizontal: 10,
          flexDirection: 'row',
          justifyContent: totalFish.length == 0 ? 'flex-end' : 'space-between',
        }}>
        {totalFish.length > 0 && (
          <AppText
            color={COLORS.BLACK}
            font={FONT_TYPE.BOLD}
            text={user?.id_tau_ca?.so_dang_ky?.toUpperCase() || 'xx-xxxx-xx'}
          />
        )}

        <AppText
          customStyle={{alignSelf: 'center'}}
          size={12}
          color={'black'}
          font={FONT_TYPE.BOLD}
          text={'Phiên bản: ' + DeviceInfo.getVersion()}
        />
      </View>
      <ScrollView
        style={{
          flex: 1,
          maxHeight: scaleBaseOnScreenHeight(screenHeight * 0.45),
          height: 'auto',
          minHeight: scaleBaseOnScreenHeight(screenHeight * 0.3),
        }}>
        <SizeBox h={setValue(32)} />
        <View style={styles.viewMenu}>
          <MenuHome
            icon={ICONS.IC_MENU_ONE}
            text={!createDiary ? 'THẢ LƯỚI' : 'HUỶ THẢ LƯỚI'}
            text2={
              !createDiary
                ? ' '
                : diaryCurrent?.thoi_gian_tha !== null
                ? moment(diaryCurrent?.thoi_gian_tha).format('DD/MM/YYYY')
                : ' '
            }
            text3={
              !createDiary
                ? ' '
                : diaryCurrent?.thoi_gian_tha !== null
                ? moment(diaryCurrent?.thoi_gian_tha).format('HH:mm')
                : ' '
            }
            onPress={onCreateDiary}
          />
          <MenuHome
            icon={ICONS.IC_MENU_TWO}
            text={'THU LƯỚI'}
            disabled={createDiary ? 1 : 2}
            text2={' '}
            onPress={() => {
              if (diaryCurrent?.thoi_gian_tha !== null) {
                navigate(SCREENS.COLLECT_INFORMATION_SCREEN as never);
              }
            }}
          />
        </View>
        <View style={styles.viewMenu}>
          <MenuHome
            icon={ICONS.IC_MENU_THREE}
            text={'THÔNG TIN THIẾT BỊ'}
            onPress={() => navigate(SCREENS.INFO_VERSEL as never)}
          />
          <MenuHome
            icon={ICONS.IC_DIARY}
            text={'NK KHAI THÁC'}
            data={true}
            onPress={() => {
              if (lastDiary.thoi_gian_boc_do) {
                Alert.alert(
                  'Thông báo',
                  'Tàu chưa xuất bến nên chưa được phép dùng tính năng này!',
                );
                return;
              }
              navigate(SCREENS.HISTORY_NIMING as never);
            }}
          />
        </View>
        <FlexView flex={1} />
      </ScrollView>
    </BaseScreen>
  );
};

const TabViewFish = ({data, totalWeight, formatDataView}: any) => {
  const [loading, setLoading] = useState(false);
  return (
    <View style={{flex: 1}}>
      <ScrollView
        style={{
          marginTop:
            Number(StatusBar.currentHeight) + (Platform.OS == 'ios' ? 60 : 0),
          flex: 1,
        }}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={() => {
              setLoading(true);
              formatDataView();
              setLoading(false);
            }}
          />
        }>
        {data?.map((ele: any, index: number) => {
          return (
            <View key={index} style={styles.rowView}>
              {ele[0]?.ten && (
                <View style={styles.colTabBorder}>
                  <AppText
                    text={ele[0]?.ten ?? ele[0]?.ten_dia_phuong}
                    font={FONT_TYPE.BOLD}
                  />
                  <AppText
                    color={COLORS.BLUE_SECONDARY}
                    text={'(' + ele[0]?.ma_code + ')'}
                    font={FONT_TYPE.BOLD}
                  />
                  <AppText
                    text={formatNumber(ele[0]?.khoi_luong) + ' (KG)'}
                    color={COLORS.BLUE}
                    font={FONT_TYPE.BOLD}
                    size={METRICS.text_12}
                  />
                </View>
              )}
              {ele[1]?.ten && (
                <View style={[styles.colTabBorder, styles.borderL]}>
                  <AppText
                    text={ele[1]?.ten ?? ele[1]?.ten_dia_phuong}
                    font={FONT_TYPE.BOLD}
                  />
                  <AppText
                    color={COLORS.BLUE_SECONDARY}
                    text={'(' + ele[1]?.ma_code + ')'}
                    font={FONT_TYPE.BOLD}
                  />
                  <AppText
                    text={formatNumber(ele[1]?.khoi_luong) + ' (KG)'}
                    color={COLORS.BLUE}
                    font={FONT_TYPE.BOLD}
                    size={METRICS.text_12}
                  />
                </View>
              )}
            </View>
          );
        })}
      </ScrollView>
      <View style={styles.tabView}>
        <View style={styles.colTab}>
          <AppText text={'TỔNG'} font={FONT_TYPE.BOLD} color={COLORS.GREY} />
        </View>
        <View style={styles.colTab}>
          <AppText
            text={formatNumber(totalWeight) + ' (KG)'}
            font={FONT_TYPE.BOLD}
            color={COLORS.BLACK}
            size={METRICS.text_16}
          />
        </View>
      </View>
      {/* <View style={{justifyContent: 'center', alignItems: 'center'}}>
        <TouchableOpacity
          style={[styles.btnShowQR, {width: scaleBaseOnScreenWidth(180)}]}
          onPress={() => navigate(SCREENS.INFO_VERSEL as never)}>
          <View style={styles.showQrIcon}>
            <AppImage customStyle={styles.qrIcon} src={ICONS.IC_SHIP_SMALL} />
          </View>
          <SizeBox w={METRICS.small_8} />
          <AppText
            color={COLORS.WHITE}
            font={FONT_TYPE.BOLD}
            text={user?.id_tau_ca?.so_dang_ky?.toUpperCase() || 'xx-xxxx-xx'}
          />
        </TouchableOpacity>
      </View> */}
    </View>
  );
};

const styles = StyleSheet.create({
  buttonStyle: {
    alignItems: 'center',
    borderColor: 'transparent',
    borderRadius: 50,
    borderWidth: 1,
    height: 80,
    justifyContent: 'center',
    width: 80,
  },
  viewMenu: {flexDirection: 'row', justifyContent: 'space-evenly'},
  logo: {width: 200, height: 200, marginTop: '15%'},
  btnShowQR: {
    height: 34,
    paddingHorizontal: 2,
    paddingRight: METRICS.nomal,
    backgroundColor: '#5C7596',
    alignItems: 'center',
    paddingLeft: 2,
    borderRadius: METRICS.nomal_medium,
    flexDirection: 'row',
  },
  showQrIcon: {
    width: 30,
    height: 30,
    backgroundColor: '#D4D4D4',
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  qrIcon: {width: 20, height: 20},
  homeTitle: {textAlign: 'center', maxWidth: 280, height: 'auto'},
  viewContainer: {alignItems: 'center', paddingTop: 30, flex: 1},
  menuText: {textAlign: 'center'},
  menuImage: {width: 50, height: 50},
  menuContainer: {width: 120, justifyContent: 'center', alignItems: 'center'},
  colTabBorder: {
    borderColor: 'rgba(154,154,154,0.1)',
    borderBottomWidth: 1,
    padding: 8,
    flex: 1,
    justifyContent: 'flex-start',
    flexDirection: 'column',
  },
  borderL: {
    borderColor: 'rgba(154,154,154,0.1)',
    borderLeftWidth: 1,
  },
  colTab: {
    padding: 8,
    flex: 1,
    justifyContent: 'flex-start',
    flexDirection: 'column',
  },
  tabView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: scaleBaseOnScreenWidth(16),
    borderColor: 'rgba(154,154,154,0.1)',
    borderTopWidth: 1,
  },
  scrollTab: {
    flex: 1,
  },
  rowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: scaleBaseOnScreenWidth(16),
  },
});
export default HomeScreen;
