import COLORS from '../../../assets/theme/colors';
import {AppIcon, AppImage, AppText} from '../../../elements';
import {IconType} from '../../../elements/AppIcon';
import {FONT_TYPE} from '../../../utils';
import {formatNumber} from '../../../utils/dateUtils';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  setValue,
} from '../../../utils/layout';
import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';

export const SelectFishRow = ({
  openImage,
  border,
  remove,
  typequick,
  ...props
}) => {
  // console.log('props: ', props);

  return (
    <View style={[styles.row, props.style]}>
      {/* <TouchableOpacity onPress={openImage}>
        <AppImage
          customStyle={styles.image}
          resizeMode="contain"
          src={{uri: props.url}}
        />
      </TouchableOpacity> */}
      <TouchableOpacity
        activeOpacity={1}
        disabled={props?.disabled}
        style={[styles.rowContent, !border && styles.noBorder]}
        onPress={props.onPress}>
        <View style={styles.infoContent}>
          <AppText
            customStyle={styles.name}
            font={FONT_TYPE.BOLD}
            text={
              (props?.ten ?? props?.ten_dia_phuong) + '(' + props?.ma_code + ')'
            }
          />
          <AppText
            customStyle={styles.weight}
            font={FONT_TYPE.BOLD}
            text={`${formatNumber(props?.khoi_luong)}`}
          />
          {/* {!typequick && (
            <AppText
              customStyle={styles.weight}
              font={FONT_TYPE.BOLD}
              text={`${formatNumber(props?.khoi_luong)}`}
            />)
          } */}
        </View>
      </TouchableOpacity>
      {remove && (
        <TouchableOpacity
          disabled={props?.disabled}
          style={styles.chevron}
          onPress={() => props.onRemove(props.fishId)}>
          <AppIcon
            color={COLORS.LIGHT_GREY}
            name="closecircleo"
            size={scaleBaseOnScreenWidth(25)}
            type={IconType.antDesign}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  chevron: {
    paddingTop: scaleBaseOnScreenHeight(10),
    alignSelf: 'flex-start',
    color: COLORS.WHITE,
    fontSize: scaleBaseOnScreenWidth(25),
    marginRight: scaleBaseOnScreenWidth(5),
    opacity: 0.7,
  },
  image: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderColor: COLORS.SILVER,
    borderRadius: setValue(30),
    borderWidth: 1,
    height: setValue(60),
    justifyContent: 'center',
    width: setValue(60),
  },
  infoContent: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: scaleBaseOnScreenHeight(8),
  },
  name: {
    color: COLORS.GREY,
    fontSize: scaleBaseOnScreenWidth(14),
  },
  noBorder: {
    borderBottomWidth: 0,
  },
  row: {
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    flexDirection: 'row',
    height: 'auto',
    marginBottom: 0.5,
    paddingLeft: scaleBaseOnScreenWidth(5),
  },
  rowContent: {
    borderBottomWidth: 0.3,
    borderColor: COLORS.WHITE,
    flexDirection: 'row',
    flex: 1,
    height: '100%',
    // marginLeft: scaleBaseOnScreenWidth(12),
  },
  shortName: {
    // ...fontFamily.futuraMedium,
    fontSize: scaleBaseOnScreenWidth(14),
    // color: 'rgb(115,127,141)',
  },
  weight: {
    color: COLORS.BITTER_GRAY,
    // color: COLORS.black_48,
  },
});
