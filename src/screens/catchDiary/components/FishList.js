import COLORS from '../../../assets/theme/colors';
import {AppText, AppIcon, FlexView} from '../../../elements';
import {IconType} from '../../../elements/AppIcon';
import StatefulComponent from '../../../lib/StatefulComponent';
import {FONT_TYPE, globalStyles, METRICS} from '../../../utils';
import {formatDate, formatNumber} from '../../../utils/dateUtils';
import {convertDMS} from '../../../utils/funcUtils';
import {
  footerHeight,
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  setValue,
} from '../../../utils/layout';
import {SelectFishRow} from './SelectFishRow';
import {useNetInfoInstance} from '@react-native-community/netinfo';
import {get, isEmpty} from 'lodash';
import moment from 'moment';
import React, {useState} from 'react';
import {
  Al<PERSON>,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ALERT_TYPE, Dialog, Toast} from 'react-native-alert-notification';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import reactotron from 'reactotron-react-native';
import {apiServices} from 'services';
import useAppStore from 'stores/appStore';
import useDiaryStore from 'stores/diaryStore';
import useStorageStore from 'stores/useStorageStore';

const SectionHeader = React.memo(function (props) {
  // diary
  const {
    fetchDiary,
    setDiaryLocal,
    setDiaryServer,
    setDiaryDelete,
    clearDiary,
    diary: {diaryLocal, diaryServer, diaryCurrent, diaryDelete},
  } = useDiaryStore();
  const {showLoading, hideLoading} = useAppStore();

  // user
  const {
    credential: {user},
  } = useStorageStore();

  const {
    netInfo: {type, isConnected},
    refresh,
  } = useNetInfoInstance();

  let total = 0;
  if (props?.san_luong?.length <= 0) {
    total = 0;
  } else if (props?.san_luong?.length === 1) {
    total =
      props?.san_luong
        ?.map((el: any) => el?.khoi_luong)
        ?.filter(el => el)
        ?.find(el => el >= 0) || 0;
  } else {

      props?.san_luong
        ?.map((el: any) => el?.khoi_luong)
        ?.filter(el => el)
        ?.filter((el: any) => !isNaN(el)).length > 0
    ) {
      total =
        props?.san_luong
          ?.map((el: any) => el?.khoi_luong)
          ?.filter(el => el)
          ?.filter((el: any) => !isNaN(el))
          ?.reduce((a, b) => Number(a) + Number(b)) || 0;
    }
  }
  const [open, setOpen] = useState(0); // != 0 là open
  const [releaseTime, setReleaseTime] = useState(props.releaseTime);
  // console.log('props.releaseTime: ', props.releaseTime);
  const [retrieveTime, setRetrieveTime] = useState(props.retrieveTime);
  // gọi hàm save
  // kiểm tra nhấn react native picker
  const onConfirm = (datetime: any) => {
    if (props.isSync) {
      setOpen(0);
      return;
    }
    if (open === 1) {
      if (moment(retrieveTime).isBefore(moment(datetime))) {
        setOpen(0);
        // Toast.show({
        //   type: ALERT_TYPE.WARNING,
        //   title: 'Thông báo: ',
        //   textBody: 'Thời gian thả lưới LỚN hơn thời gian thu lưới',
        // });
        setRetrieveTime(
          moment(datetime)
            .clone()
            .add(moment.duration({days: 1})),
        );
      }
      setReleaseTime(datetime);
      setOpen(0);
      const param = {retrieveTime: retrieveTime, releaseTime: datetime};
      Alert.alert('Thông báo', 'Tiếp tục cập nhật thời gian thu lưới?', [
        {
          text: 'Kết thúc',
          onPress: () => {
            props.onChangeTime(param);
          },
        },
        {
          text: 'Đồng ý',
          onPress: () => {
            props.onChangeTime(param);
            setOpen(2);
          },
        },
      ]);
    } else if (open === 2) {
      if (moment(releaseTime).isAfter(moment(datetime))) {
        setOpen(0);
        Toast.show({
          type: ALERT_TYPE.WARNING,
          title: 'Thông báo: ',
          textBody: 'Thời gian thu lưới LỚN hơn thời gian thả lưới',
        });
        return;
      }
      setRetrieveTime(datetime);
      setOpen(0);
      const param = {releaseTime: releaseTime, retrieveTime: datetime};
      props.onChangeTime(param);
    }
  };

  // Đổi rules ngày 06.11
  // const onDelete = async (id: string) => {
  //   showLoading();
  //   try {
  //     const rsDelete = await apiServices.Delete({
  //       url: '/token/nk_khaithac/' + id,
  //     });
  //   } catch (error) {
  //     console.log('error: ', error);
  //   }
  //   const rsFetch = await apiServices
  //     .Post({
  //       url: '/token/nk_khaithac',
  //       body: {
  //         filter: {
  //           id_tau_ca: user?.id_tau_ca?._id,
  //           trang_thai: [1, 2, 3, 4],
  //         },
  //         sort: {
  //           createdAt: -1,
  //         },
  //         limit: 500,
  //       },
  //     })
  //     .then(data =>{ fetchDiary(data, false, user)} )
  //     .catch(error => {
  //       console.log('error: ', error);
  //     })
  //     .finally(() => {
  //       hideLoading();
  //     });
  // };

  // task ngày 15.10
  const onRemove = async (value: boolean) => {
    // Dialog.show({
    //   type: ALERT_TYPE.WARNING,
    //   title: 'Thông báo',
    //   textBody: 'Bạn có muốn xoá lịch sử thả lưới này không?',
    //   button: 'Đồng ý',
    //   onPressButton() {
    //     Dialog.hide();
    //     onRemove(true);
    //   },
    // });
    if (value == false) {
      Alert.alert('Thông báo', 'Bạn có muốn xoá lịch sử thả lưới này không?', [
        {
          text: 'Ngày nghỉ',
          onPress: () => {
            onChooseOffDay(true);
          },
        },
        {
          text: 'Huỷ',
          onPress: () => {},
        },
        {
          text: 'Xoá',
          onPress: () => {
            onRemove(true);
          },
        },
        
      ]);
      return;
    }
    // if (props.isSync == true) {
    //   await onDelete(props.id);
    //   return;
    // }
    // gọi store xoá local
    if (diaryCurrent?.thoi_gian_tha != null) {
      clearDiary();
    }
    const _diaryLocal_exist_on = diaryLocal?.filter(
      (ele: any) => ele?._id == props.id,
    );
    // console.log('_diaryLocal_exist_on: ', _diaryLocal_exist_on);

    const _diaryLocal_exist_off = diaryLocal?.filter(
      (ele: any) => ele?.item_id == props.id,
    );

    if (_diaryLocal_exist_on.length > 0) {
      if (!diaryLocal) {
        Toast.show({
          type: ALERT_TYPE.DANGER,
          title: 'Thông báo',
          textBody: 'Vui lòng kết nối 4G/Internet để xoá mẻ lưới này',
        });
        return;
      }
      // gọi xoá record online
      const final = diaryLocal?.filter((ele: any) => ele?._id != props.id);
      const arrDelete = diaryLocal?.filter((ele: any) => ele?._id == props.id);
      // console.log('final: ', final.length);
      setDiaryLocal(final);
      setDiaryDelete(diaryDelete?.concat(arrDelete));
      // không gọi api delete
      // await onDelete(props.id); => chuyển  sang đồ
      // gọi API xoá
    } else if (_diaryLocal_exist_off.length > 0) {
      // gọi API xoá record offline
      const final = diaryLocal?.filter((ele: any) => ele?.item_id != props.id);
      setDiaryLocal(final);
    }
  };
  const warning =
    !get(props, 'releaseLocation') ||
    !get(props, 'retrieveLocation') ||
    get(props, 'releaseLocation')?.filter((ele: any) => ele > 0).length < 2 ||
    get(props, 'retrieveLocation')?.filter((ele: any) => ele > 0).length < 2;

  const onChooseOffDay = async (value)=>{
    if (value == false) {
      Alert.alert('Thông báo', 'Bạn có muốn chọn ngày nghỉ cho mẻ lưới này?', [
        {
          text: 'Huỷ',
          onPress: () => {},
        },
        {
          text: 'Đồng ý',
          onPress: () => {
            onChooseOffDay(true);
          },
        },
      ]);
      return;
    }


    // thực đổi trạng thái
    // xoá mẻ danh sách loài |
    // const _diaryLocal_exist_on = diaryLocal?.find(
    //   (ele: any) => ele?._id == props.id,
    // );
    // TH1: online
    // 1. API cập nhập Mẻ lưới
    // 2. API fetch dữ liệu mới về
    // const recordUpt = {
    //   ..._diaryLocal_exist_on,
    //   is_day_off: true,
    //   san_luong: null,
    //   vi_tri_tha: null,
    //   thoi_gian_thu: null
    // }

    // if(!isConnected){
    //   if(recordUpt?.is_online){
    //     try {
    //       const rs_upd = await apiServices.Put({
    //         url: '/token/nk_khaithac/' + recordUpt.id,
    //         body: recordUpt,
    //       });
    //       if(rs_upd?.status == 'success'){
    //         // thực hiện gọi API fetch
    //         const id_tau_ca = user?.id_tau_ca?._id;
    //         const rs_list = await apiServices.Post({
    //           url: '/token/nk_khaithac',
    //           body: {
    //             filter: {
    //               id_tau_ca: id_tau_ca,
    //               trang_thai: [0, 1, 2, 3, 4],
    //             },
    //             sort: {
    //               createdAt: -1,
    //             },
    //             limit: 500,
    //           },
    //         });
    //         if(rs_list?.status == 'success'){
    //           Toast.show({
    //             type: ALERT_TYPE.SUCCESS,
    //             title: 'Thông báo',
    //             textBody: rs_upd?.message
    //           })
    //           fetchDiary(rs_list, false, user);
    //         }
    //       }
    //     } catch (error) {
    //       console.log('error: ', error);
    //     }
    //   }
    // }else{
    // thực hiện thông báo
    //  Toast.show({
    //   type: ALERT_TYPE.WARNING,
    //   title: 'Thông báo',
    //   textBody: "Trạng thái offline ko thực hiện được"
    // })
    // }

    const _diaryLocal_exist_on = diaryLocal?.find(
      (ele: any) => ele?._id == props.id,
    );
    const list = diaryLocal?.map((ele: any) => {
      if (ele?._id == props?.id) {
        return {
          ...ele,
          is_day_off: true,
          is_online: false,
          san_luong: null,
          vi_tri_tha: null,
          thoi_gian_thu: null,
        };
      }
      return ele;
    });
    setDiaryLocal(list);
  };

  const RenderWaring = () => {
    if (props?.is_online == true) {
      return (
        <View
          style={{
            width: 15,
            height: 15,
            backgroundColor: COLORS.GREEN,
            borderRadius: 20,
          }}
        />
      );
    } else {
      if (props?._id) {
        return (
          <View
            style={{
              width: 15,
              height: 15,
              backgroundColor: COLORS.YELLOW,
              borderRadius: 20,
            }}
          />
        );
      }
      return (
        <View
          style={{
            width: 15,
            height: 15,
            backgroundColor: warning ? COLORS.RED : COLORS.BITTER_GRAY,
            borderRadius: 20,
          }}
        />
      );
    }
  };

  return (
    <View style={[styles.sectionHeader]}>
      <View
        style={[
          styles.sectionHeaderRow,
          {
            backgroundColor: props?.is_day_off
              ? COLORS.BITTER_GRAY_LIGHT
              : warning
              ? COLORS.RED
              : COLORS.BITTER_GRAY,
          },
        ]}>
        <RenderWaring />
        <AppText
          customStyle={{
            ...styles.badge,
            fontSize: scaleBaseOnScreenWidth(14),
            fontWeight: 'bold',
          }}
          text={props?.keyInput + 1}
        />
        <TouchableOpacity
          style={[
            styles.sectionHeaderRow,
            {
              backgroundColor: props?.is_day_off
                ? COLORS.BITTER_GRAY_LIGHT
                : warning
                ? COLORS.RED
                : COLORS.BITTER_GRAY,
            },
          ]}
          onPress={() => props.onPressUpdate()}>
          <AppText
            font={FONT_TYPE.BOLD}
            customStyle={styles.sectionName}
            text={`ML ${formatDate(
              props?.releaseTime,
              'HH:mm DD/MM/YYYY',
              '',
            )}`}
          />
          <AppText
            customStyle={styles.badge}
            text={`${formatNumber(get(props, 'total', total, 0))} KG`}
          />
        </TouchableOpacity>
        {!props?.isSync && props?.is_day_off && (
          <View
            style={{
              backgroundColor: props?.is_day_off
                ? COLORS.BITTER_GRAY_LIGHT
                : warning
                ? COLORS.RED
                : COLORS.BITTER_GRAY,
              paddingRight: 10,
            }}>
            <AppText
              customStyle={{
                ...styles.badge,
                padding: 2,
                backgroundColor: props?.is_day_off
                  ? COLORS.LEMON
                  : COLORS.WHITE,
              }}
              text={'Ngày nghỉ'}
            />
          </View>
        )}
        {
          // ((isConnected && props.isSync == true) || !props?.isSync) && <TouchableOpacity
          !props.disable && !props?.isSync && (
            <TouchableOpacity
              style={{
                backgroundColor: props?.is_day_off
                  ? COLORS.BITTER_GRAY_LIGHT
                  : warning
                  ? COLORS.RED
                  : COLORS.BITTER_GRAY,
              }}
              onPress={() => onRemove(false)}>
              <AppIcon
                color={COLORS.WHITE}
                name="trash"
                size={scaleBaseOnScreenWidth(20)}
                type={IconType.fontawesome}
              />
            </TouchableOpacity>
          )
        }
      </View>
      <TouchableOpacity
        activeOpacity={props.isSync ? 1 : 0.5}
        onPress={() => {
          if (props.disable) {return;}
          if (props.isSync) {
            setOpen(0);
            return;
          }
          setOpen(1);
        }}>
        <View style={styles.sectionFooterRow}>
          <AppText
            customStyle={{
              ...styles.info,
              flex: 1,
              width: setValue(80),
              color: COLORS.BLUE,
              height: setValue(25),
            }}
            text={`${formatDate(
              // text={`THẢ LƯỚI: ${formatDate(
              props?.releaseTime,
              'DD/MM HH:mm',
              '',
            )}`}
          />
          {!props?.is_day_off && (
            <AppText
              customStyle={{
                ...styles.info,
                flex: 1,
                width: 150,
                paddingLeft: scaleBaseOnScreenWidth(10),
                color:
                  get(props, 'releaseLocation')?.filter((ele: any) => ele > 0)
                    ?.length > 1
                    ? COLORS.BLUE
                    : COLORS.RED,
                height: setValue(25),
              }}
              text={
                get(props, 'releaseLocation')?.filter((ele: any) => ele > 0)
                  ?.length > 0
                  ? convertDMS(
                      get(props, 'releaseLocation[0]'),
                      get(props, 'releaseLocation[1]'),
                    )
                  : '---'
              }
            />
          )}
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        activeOpacity={props.isSync ? 1 : 0.5}
        onPress={() => {
          if (props.disable) {return;}

          if (props.isSync) {
            setOpen(0);
            return;
          }
          setOpen(2);
        }}>
        <View style={styles.sectionFooterRow}>
          <AppText
            customStyle={{
              ...styles.info,
              flex: 1,
              width: setValue(80),
              color: COLORS.YELLOW_PRIMARY,
              height: setValue(25),
            }}
            text={`${formatDate(
              // text={`THU LƯỚI: ${formatDate(
              props?.retrieveTime,
              'DD/MM HH:mm',
              '',
            )}`}
          />
          {!props?.is_day_off && (
            <AppText
              customStyle={{
                ...styles.info,
                flex: 1,
                width: 150,
                paddingLeft: scaleBaseOnScreenWidth(10),
                color:
                  get(props, 'retrieveLocation')?.filter((ele: any) => ele > 0)
                    ?.length > 1
                    ? COLORS.YELLOW_PRIMARY
                    : COLORS.RED,
                height: setValue(25),
              }}
              text={
                get(props, 'retrieveLocation')?.filter((ele: any) => ele > 0)
                  ?.length > 0
                  ? convertDMS(
                      get(props, 'retrieveLocation[0]'),
                      get(props, 'retrieveLocation[1]'),
                    )
                  : '---'
              }
            />
          )}
        </View>
      </TouchableOpacity>
      {props?.note ? (
        <View style={styles.sectionFooterRow}>
          <Text style={styles.info}>{'GHI CHÚ'}</Text>
        </View>
      ) : null}
      {open === 1 && (
        <DateTimePickerModal
          date={releaseTime ?  new Date(releaseTime) : new Date()}
          maximumDate={new Date()}
          isVisible={true}
          mode="datetime"
          onConfirm={onConfirm}
          onCancel={() => {
            setOpen(0);
          }}
        />
      )}
      {open === 2 && retrieveTime && (
        <DateTimePickerModal
          date={retrieveTime ?  new Date(retrieveTime) : new Date()}

          maximumDate={new Date()}
          isVisible={true}
          mode="datetime"
          onConfirm={onConfirm}
          onCancel={() => {
            setOpen(0);
          }}
        />
      )}
    </View>
  );
});

export const CatchRow = React.memo(
  ({
    item,
    data,
    onUpdate,
    onRemoveFish,
    onChangeTime,
    isSync,
    keyInput,
    onUpdateOffDay,
    disable,
  }) => {
    return (
      <View style={[styles.rowWrapper]}>
        <SectionHeader
          disable={disable}
          key={keyInput}
          keyInput={keyInput}
          isSync={isSync}
          onPressUpdate={() => onUpdate({...item, keyInput: keyInput + 1})}
          onChangeTime={(edit: any) => {
            item.thoi_gian_tha = edit?.releaseTime;
            item.thoi_gian_thu = edit?.retrieveTime;
            onChangeTime(item);
          }}
          disabled={get(item, 'disabled')}
          id={item.id}
          _id={item?._id}
          is_online={item?.is_online}
          note={item?.note}
          releaseLocation={get(item, 'vi_tri_tha')}
          releaseTime={get(item, 'thoi_gian_tha')}
          retrieveLocation={get(item, 'vi_tri_thu')}
          retrieveTime={get(item, 'thoi_gian_thu')}
          total={get(item, 'chuyentai.tong_khoi_luong')}
          thoi_gian={get(item, 'chuyentai.thoi_gian')}
          san_luong={get(item, 'san_luong')}
          is_day_off={get(item, 'is_day_off')}
        />
        <View style={styles.bodyRow}>
          {data?.map((fish, idx) => (
            <View key={idx ?? fish.fishId ?? fish.id} style={styles.box}>
              <SelectFishRow
                border={false}
                end={idx === data?.length - 1}
                remove={false}
                style={styles.row}
                onRemove={onRemoveFish(fish.fishId, item.id)}
                {...fish}
              />
            </View>
          ))}
        </View>
      </View>
    );
  },
);

export default class FishList extends StatefulComponent {
  // thằng này gây ảnh hưởng không fetch lại dữ liệu
  keyExtractor = (item, index) => item?._id || item?.id; // item.fishId || item.id;

  renderItem = ({item, index}) => {
    const data = get(item, 'san_luong', []);
    const onRemoveFish = get(this.props, 'onRemoveFish', () => {});
    // const onUpdateOffDay = get(this.props, 'onUpdateOffDay', () => {});
    const onUpdate = get(this.props, 'onUpdate', () => {});
    const onChangeTime = get(this.props, 'onChangeTime', () => {});
    return (
      <CatchRow
        disable={this.props.disable ?? false}
        key={index}
        keyInput={index}
        isSync={this.props.isSync}
        data={data}
        item={item}
        onRemoveFish={onRemoveFish}
        onUpdate={onUpdate}
        onChangeTime={(edit: any) => {
          item.thoi_gian_tha = edit?.thoi_gian_tha;
          item.thoi_gian_thu = edit?.thoi_gian_thu;
          onChangeTime(item);
        }}
      />
    );
  };

  render() {
    const {data} = this.props;
    if (isEmpty(data) || this.props.type === 1) {
      return (
        <View style={[styles.isEmpty]}>
          <Text style={styles.emptyString}>{'Chưa có dữ liệu'}</Text>
        </View>
      );
    }
    return (
      <FlatList
        contentContainerStyle={styles.content}
        data={data}
        keyExtractor={this.keyExtractor}
        renderItem={this.renderItem}
        // renderSectionHeader={this.renderSectionHeader}
        stickySectionHeadersEnabled={false}
        style={styles.flatList}
      />
    );
  }
}

const styles = StyleSheet.create({
  badge: {
    backgroundColor: COLORS.WHITE,
    ...globalStyles.textSubHeading,
    borderRadius: 5,
    color: COLORS.SECONDARY,
    marginLeft: scaleBaseOnScreenWidth(5),
    overflow: 'hidden',
    paddingHorizontal: scaleBaseOnScreenWidth(5),
    fontWeight: 'bold',
  },
  bodyRow: {
    backgroundColor: COLORS.WHITE,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  box: {
    width: '50%',
  },
  content: {
    paddingBottom: scaleBaseOnScreenHeight(80),
    paddingTop: scaleBaseOnScreenHeight(10),
  },
  emptyString: {
    ...globalStyles.textHeading,
  },
  flatList: {
    flex: 1,
    paddingHorizontal: scaleBaseOnScreenWidth(5),
  },
  info: {
    ...globalStyles.textSubHeading,
    color: COLORS.BITTER_GRAY,
    fontWeight: 'bold',
  },
  isEmpty: {
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: 4,
    flex: 1,
    justifyContent: 'center',
    marginBottom: scaleBaseOnScreenWidth(5) + footerHeight,
    marginHorizontal: scaleBaseOnScreenWidth(5),
  },
  row: {
    backgroundColor: COLORS.WHITE,
  },
  rowWrapper: {
    borderRadius: 4,
    overflow: 'hidden',
  },
  sectionFooterRow: {
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingHorizontal: scaleBaseOnScreenWidth(10),
    paddingVertical: scaleBaseOnScreenHeight(5),
  },
  sectionHeader: {
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    marginTop: scaleBaseOnScreenHeight(5),
    overflow: 'hidden',
  },
  sectionHeaderRow: {
    alignItems: 'center',
    backgroundColor: COLORS.BITTER_GRAY,
    flex: 1,
    flexDirection: 'row',
    height: scaleBaseOnScreenHeight(40),
    paddingHorizontal: scaleBaseOnScreenWidth(10),
  },
  sectionName: {
    lineHeight: scaleBaseOnScreenHeight(25),
    ...globalStyles.textButton,
    color: COLORS.WHITE,
    fontWeight: 'bold',
  },
  updateContainer: {
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderBottomLeftRadius: setValue(5),
    borderBottomRightRadius: setValue(5),
    paddingVertical: scaleBaseOnScreenHeight(20),
  },
});
