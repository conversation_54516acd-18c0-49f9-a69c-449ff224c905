import FishList from './components/FishList';
import {useNavigation} from '@react-navigation/native';
import {COLORS} from 'assets';
import {DelayRenderComponent, Header} from 'components';
import {AppButton, BaseScreen} from 'elements';
import React from 'react';
import {ALERT_TYPE, Toast} from 'react-native-alert-notification';
import {METRICS, globalStyles} from 'utils';

const CatchDiary = () => {
  const {goBack} = useNavigation();
  return (
    <BaseScreen>
      <Header backgroundColor={COLORS.MAIN_COLOR} title={'NHẬT KÝ ĐÁNH BẮT'} />
      <DelayRenderComponent style={[globalStyles.flex1, {padding: 8}]}>
        <FishList onRemoveFish={() => {}} onUpdate={() => {}} />
        <AppButton
          customStyle={{
            marginBottom: METRICS.nomal,
            marginHorizontal: METRICS.nomal,
          }}
          text="Đồng bộ dữ liệu"
          onPress={() =>
            Toast.show({
              type: ALERT_TYPE.SUCCESS,
              title: 'Success',
              textBody: 'Congrats! this is toast notification success',
              onHide: () => goBack(),
            })
          }
        />
      </DelayRenderComponent>
    </BaseScreen>
  );
};

export default CatchDiary;
