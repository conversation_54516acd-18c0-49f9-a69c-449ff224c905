import ListNotSync from './components/ListNotSync';
import ListSynced from './components/ListSynced';
import {useNetInfoInstance} from '@react-native-community/netinfo';
import {COLORS} from 'assets';
import {Content, Header} from 'components';
import {BaseScreen} from 'elements';
import {isEmpty} from 'lodash';
import moment from 'moment';
import React, {useEffect} from 'react';
import {Alert} from 'react-native';
import {ALERT_TYPE, Dialog, Toast} from 'react-native-alert-notification';
import reactotron from 'reactotron-react-native';
import {apiServices} from 'services';
import useAppStore from 'stores/appStore';
import useDiaryStore from 'stores/diaryStore';
import useFishStore from 'stores/fishStore';
import useStorageStore from 'stores/useStorageStore';

const routes = [
  {
    key: 'first',
    title: 'CHƯA ĐỒNG BỘ',
    iconName: 'clockcircle',
    iconType: 'AntDesign',
  },
  {
    key: 'second',
    title: 'ĐÃ ĐỒNG BỘ',
    iconName: 'checksquare',
    iconType: 'AntDesign',
  },
];

const HistoryMining = () => {
  const {
    credential: {user, token},
    onClearnStorage,
  } = useStorageStore();

  const {showLoading} = useAppStore();
  const {
    fetchDiary,
    clearDiaryDelete,
    setLastDiary,
    setDiaryDelete,
    diary: {diaryLocal, lastDiary, diaryDelete},
  } = useDiaryStore();
  const {setFavoriteArr} = useFishStore();

  const {
    netInfo: {type, isConnected, isInternetReachable},
    refresh,
  } = useNetInfoInstance();

  useEffect(() => {
    checkStatDiary();
    setTimeout(() => {
      InitPage(false);
    }, 1000);
  }, [token]);

  const checkStatDiary = async () => {
    const id_tau_ca = user?.id_tau_ca?._id;
    const result = await new Promise((resolve, reject) => {
      apiServices
        .Post({
          url: '/token/nk_truyxuat_status',
          body: {
            id_tau_ca: id_tau_ca,
          },
        })
        .then((res: any) => {
          if (res.status == 'success' && res?.data?.length > 0) {
            let start_diary = false;
            if (lastDiary?.thoi_gian_cap_cang != null) {
              start_diary =
                lastDiary?.thoi_gian_cap_cang !=
                res.data[0]?.thoi_gian_cap_cang;
            }
            setLastDiary({
              ...lastDiary,
              ...res.data[0],
              start_diary: start_diary,
            });
            resolve(true);
          } else {
            resolve(false);
          }
        })
        .catch((error: any) => {
          console.log('error: ', error);
          resolve(false);
        });
    });
    return result;
  };

  /**
   * check-exist: reload
   * true: đồng bộ
   * location: lấy vị trí
   * start-diary: fetch lần đầu
   *
   */
  const InitPage = async (value: any) => {
    if (!isConnected || value == 'error') {
      return;
    }
    // đồng bộ và reload => xoá mẻ lưới
    if (['check-exist', 'true'].includes(value?.toString())) {
      // fetch lại cài đặt nhanh
      const res: any = await apiServices.Get({url: '/token/catchconfig'});
      if (res?.status == 'success') {
        const {id_nhom_loais = []} = res.data || {};
        setFavoriteArr(id_nhom_loais);
      }
      // kiểm tra xem có cần xoá chuyến biển ko
      const thoi_gian_xuat_cang = lastDiary?.thoi_gian_xuat_cang ?? null;
      if (thoi_gian_xuat_cang) {
        const d_thoi_gian_xuat_cang = moment(thoi_gian_xuat_cang).add(
          -1,
          'days',
        );
        const arrExpired =
          diaryLocal?.filter((ele: any) =>
            moment(d_thoi_gian_xuat_cang).isAfter(ele?.thoi_gian_tha),
          ) ?? [];
        if (lastDiary?.start_diary == false || arrExpired.length > 0) {
          if (arrExpired?.length > 0) {
            // lấy danh sach delete
            const arrDelId = arrExpired
              .filter((ele: any) => ele?._id)
              .map((ele: any) => ele?._id);
            setDiaryDelete(diaryDelete?.concat(arrDelId));
            Alert.alert(
              'Thông báo',
              'Tàu đã kết thúc chuyển biển, bạn có muốn thiết lập lại nhật ký từ đầu cho chuyến biển mới không?',
              [
                {
                  text: 'Huỷ',
                  onPress: () => {},
                },
                {
                  text: 'Đồng ý',
                  onPress: () => {
                    InitPage('start-diary');
                  },
                },
              ],
            );

            // Dialog.show({
            //   type: ALERT_TYPE.WARNING,
            //   title: 'Thông báo',
            //   textBody: "Tàu đã kết thúc chuyển biển, bạn có muốn thiết lập lại nhật ký từ đầu cho chuyến biển mới không?",
            //   button: 'Đồng ý',
            //   onPressButton() {
            //     InitPage('start-diary')
            //     Dialog.hide();
            //   },
            // });
            return;
          }
        }
      }

      // kiểm tra dữ liệu cần đồng bộ
      const exist =
        diaryLocal?.filter((ele: any) => ele?._id && !ele?.is_online) || [];
      reactotron.log('>>>>>>>>>>>>> exist: ', exist);

      if (exist?.length > 0) {
        // Alert.alert(
        //   'Thông báo',
        //   'Bạn đang có "MẺ LƯỚI" chưa ĐỒNG BỘ. \n VUI LÒNG THỰC HIỆN ĐỒNG BỘ.',
        //   [
        //     {
        //       text: 'Huỷ',
        //       onPress: () => {},
        //     },
        //     {
        //       text: 'Đồng ý',
        //       onPress: () => {
        //         // InitPage('start-diary')
        //       },
        //     },
        //   ],
        // );

        // Dialog.show({
        //   type: ALERT_TYPE.WARNING,
        //   title: 'Thông báo',
        //   textBody: 'Bạn đang có "MẺ LƯỚI" chưa ĐỒNG BỘ. \n VUI LÒNG THỰC HIỆN ĐỒNG BỘ.',
        //   button: 'Đồng ý',
        //   onPressButton() {
        //     // InitPage(false)
        //     Dialog.hide();
        //   },
        // });
        return;
      }
    }
    // if( ['location'].includes(value)){
    //   const exist = diaryLocal?.filter((ele:any)=>ele?._id && !ele?.is_online) || [];
    //   if(exist?.length > 0){
    //     Toast.show({
    //       type: ALERT_TYPE.WARNING,
    //       title: 'Thông báo',
    //       textBody: 'Bạn đang có "MẺ LƯỚI" chưa ĐỒNG BỘ. \n VUI LÒNG THỰC HIỆN ĐỒNG BỘ.',
    //     });
    //     return;
    //   }
    // }
    showLoading(true);
    const id_tau_ca = user?.id_tau_ca?._id;
    const config = {
      url:
        value == 'location'
          ? '/token/nk_khaithac/synctracklog'
          : '/token/nk_khaithac',
      body: {
        filter: {
          id_tau_ca: id_tau_ca,
          trang_thai: [0, 1, 2, 3, 4],
        },
        sort: {
          createdAt: -1,
        },
        limit: 500,
      },
    };
    apiServices
      .Post(config)
      .then(async (data: any) => {
        if (value != 'location') {
          if (['check-exist'].includes(value)) {
            // nếu trường hợp chỉh xong và đồng bộ lại
            clearDiaryDelete();
          } else if (['start-diary'].includes(value)) {
            // tắt chế độ start-diary: false
            // API delete
            try {
              for (let i = 0; i < diaryDelete.length; i++) {
                try {
                  const rsDelete = await apiServices.Delete({
                    url: '/token/nk_khaithac/' + diaryDelete[i],
                  });
                } catch (error) {
                  console.log('rsDelete error: ', error);
                }
              }
              setDiaryDelete([]);
            } catch (error) {
              console.log('error: ', error);
            }
            clearDiaryDelete();
            setLastDiary({
              ...lastDiary,
              start_diary: true,
            });
            fetchDiary(data, false, user, true);
            return;
          }
          fetchDiary(data, false, user);
        } else {
          InitPage(true);
        }
      })
      .catch((error: any) => {
        if (error == 'logout') {
          Toast.show({
            type: ALERT_TYPE.DANGER,
            title: 'Thông báo',
            textBody: 'Phiên đăng nhập hết hiệu lực. Vui lòng đăng nhập lại',
          });
          setTimeout(() => {
            onClearnStorage();
          }, 3000);
          return;
        }
      })
      .finally(() => {
        showLoading(false);
      });
  };

  return (
    <BaseScreen>
      <Header
        backgroundColor={COLORS.MAIN_COLOR}
        title={'NHẬT KÝ KHAI THÁC'}
        // iconRight="cw"
        iconTextRight={isInternetReachable ? 'Tải về' : undefined}
        onPressTextRight={() => {
          if (isInternetReachable) {
            InitPage('check-exist');
          }
        }}
        onPressRight={() => {
          if (isInternetReachable) {
            InitPage('check-exist');
          }
        }}
      />
      <ListNotSync callback={InitPage} />
    </BaseScreen>
  );
};
export default HistoryMining;
