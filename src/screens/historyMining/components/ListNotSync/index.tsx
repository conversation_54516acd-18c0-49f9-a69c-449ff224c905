import {useNetInfoInstance} from '@react-native-community/netinfo';
import {useNavigation} from '@react-navigation/native';
import {COLORS} from 'assets';
import {AppButton} from 'elements';
import moment from 'moment';
import SCREENS from 'navigation/Screens';
import React, {memo} from 'react';
import {Alert, View} from 'react-native';
import {ALERT_TYPE, Toast} from 'react-native-alert-notification';
import reactotron from 'reactotron-react-native';
import FishList from 'screens/catchDiary/components/FishList';
import {apiServices} from 'services';
import useAppStore from 'stores/appStore';
import useDiaryStore from 'stores/diaryStore';
import useFishStore from 'stores/fishStore';
import useStorageStore from 'stores/useStorageStore';
import {setValue} from 'utils/layout';
import uuid from 'uuid-random';

const ListNotSync = (props: any) => {
  const {
    credential: {user},
    onClearnStorage,
  } = useStorageStore();
  const {callback} = props;

  const {navigate} = useNavigation();
  const {
    netInfo: {isConnected, isInternetReachable},
  } = useNetInfoInstance();
  const {showLoading, hideLoading} = useAppStore();

  // const [ fetchState, setFetchState]=useState(false)
  const {
    setDiary,
    setDiaryLocal,
    setDiaryDelete,
    fetchDiary,
    diary: {
      diaryCurrent,
      diaryLocal = [],

      diaryDelete,
      lastDiary,
      configQuickDiary,
    },
  } = useDiaryStore();
  const {setFavoriteArr} = useFishStore();

  const list =
    diaryCurrent?.thoi_gian_tha !== null
      ? [diaryCurrent, ...diaryLocal]?.sort(
          (a: any, b: any) =>
            new Date(a?.thoi_gian_tha).getTime() -
            new Date(b?.thoi_gian_tha).getTime(),
        )
      : diaryLocal;

  // lấy dữ liệu local
  const onPressDetail = (item: any) => {
    // setFetchState(true)
    // kiểm tra cập nhật
    let edit = item.id === diaryCurrent.id ? 1 : 2;
    navigate(SCREENS.DETAIL_HISTORY_MINING, {
      data: item,
      isSync: false,
      edit: edit, // 1: là cập nhật mẻ lưới hiện tại, 2: mẻ lứoi trứo đó
    });
  };
  // useEffect(() => {
  //   console.log('>>>>>> ListNotSync diaryLocal: ', diaryLocal);
  // }, [diaryCurrent, diaryLocal]);

  //Gọi API đồng bộ
  const onSubmitSync = async (value: number) => {
    if (diaryLocal?.length === 0 && diaryDelete?.length === 0) {
      return;
    }
    if (isConnected && isInternetReachable) {
      const thoi_gian_xuat_cang = lastDiary?.thoi_gian_xuat_cang ?? null;
      if (value == 0) {
        // có mẻ lưới cũ nhỏ hơn ngày ...
        let arrExpired = [];
        if (thoi_gian_xuat_cang) {
          arrExpired =
            diaryLocal?.filter((ele: any) =>
              moment(thoi_gian_xuat_cang).isAfter(ele?.thoi_gian_tha),
            ) ?? [];
        }
        reactotron.log('>>>>>> arrExpired: ', arrExpired);

        if (arrExpired?.length > 0) {
          const arrDelId = arrExpired
            .filter((ele: any) => ele?._id)
            .map((ele: any) => ele?._id);
          setDiaryDelete(diaryDelete?.concat(arrDelId));
          // Dialog.show({
          //   type: ALERT_TYPE.WARNING,
          //   title: 'Thông báo',
          //   textBody: 'Có dữ liệu của chuyến biển cũ, Bạn muốn xoá dữ liệu của chuyến cũ và tiếp tục ghi đè toàn bộ nhật ký điện tử cũ trên hệ thống?',
          //   button: 'Đồng ý',
          //   onPressButton() {
          //     Dialog.hide();
          //     onSubmitSync(3)
          //   },
          // });

          Alert.alert(
            'Thông báo',
            'Có dữ liệu của chuyến biển cũ, Bạn muốn xoá dữ liệu của chuyến cũ và tiếp tục ghi đè toàn bộ nhật ký điện tử cũ trên hệ thống?',
            [
              {
                text: 'Huỷ',
                onPress: () => {},
              },
              {
                text: 'Đồng ý',
                onPress: () => {
                  onSubmitSync(3);
                },
              },
            ],
          );
        } else {
          // Dialog.show({
          //   type: ALERT_TYPE.INFO,
          //   title: 'Thông báo',
          //   textBody: 'Sẽ ghi đè toàn bộ nhật ký điện tử cũ trên hệ thống?',
          //   button: 'Đồng ý',
          //   onPressButton() {
          //     Dialog.hide();
          //     onSubmitSync(1)
          //   },
          // });

          Alert.alert(
            'Thông báo',
            'Sẽ ghi đè toàn bộ nhật ký điện tử cũ trên hệ thống?',
            [
              {
                text: 'Huỷ',
                onPress: () => {},
              },
              {
                text: 'Đồng ý',
                onPress: () => {
                  onSubmitSync(1);
                },
              },
            ],
          );
        }
        return;
      } else if (value == 2) {
        // Dialog.show({
        //   type: ALERT_TYPE.INFO,
        //   title: 'Thông báo',
        //   textBody: 'Bạn muốn lấy vị trí khai thác mới nhất từ hệ thống?',
        //   button: 'Đồng ý',
        //   onPressButton() {
        //     Dialog.hide();
        //     callback('location')
        //   },
        // });
        Alert.alert(
          'Thông báo',
          'Bạn muốn lấy vị trí khai thác mới nhất từ hệ thống?',
          [
            {
              text: 'Huỷ',
              onPress: () => {},
            },
            {
              text: 'Đồng ý',
              onPress: () => {
                callback('location');
              },
            },
          ],
        );
        return;
      }

      showLoading(true);
      // kiểm tra mẻ lưới thu chưa
      if (diaryCurrent?.thoi_gian_tha !== null) {
        Toast.show({
          type: ALERT_TYPE.WARNING,
          title: 'Thông báo',
          textBody: 'Vui lòng THU LƯỚI trước khi đồng bộ dữ liệu',
        });
        showLoading(false);
        return;
      }
      // Kiểm tra sản lượng
      // let _diaryLocal = diaryLocal?.filter(
      //   (ele: any) => ele?.san_luong?.length === 0,
      // );
      // if (_diaryLocal?.length > 0) {
      //   Toast.show({
      //     type: ALERT_TYPE.WARNING,
      //     title: 'Thông báo',
      //     textBody: 'Vui lòng kiểm tra sản lượng trước khi đồng bộ dữ liệu',
      //   });
      //   showLoading(false);
      //   return;
      // }
      // console.log('>>>>>>>>>> diaryLocal: ', JSON.stringify(diaryLocal));
      // showLoading(false);
      let _diaryLocal = diaryLocal;
      if (value == 3 && thoi_gian_xuat_cang) {
        const arrExpired =
          diaryLocal?.filter((ele: any) =>
            moment(thoi_gian_xuat_cang).isAfter(ele?.thoi_gian_tha),
          ) ?? [];
        const arrDelId = arrExpired
          .filter((ele: any) => ele?._id)
          .map((ele: any) => ele?._id);
        setDiaryDelete(diaryDelete?.concat(arrDelId));
        _diaryLocal =
          diaryLocal?.filter(
            (ele: any) =>
              !moment(thoi_gian_xuat_cang).isAfter(ele?.thoi_gian_tha),
          ) ?? [];
      }
      // console.log('Bắt đầu đồng bộ _diaryLocal: ', _diaryLocal);

      // kiểm tra khối lượng
      const id_tau_ca = user?.id_tau_ca?._id;
      const params = {
        id_tau_ca: id_tau_ca,
        sync_data: _diaryLocal
          // ?.filter((ele: any) => !ele?.is_online || ele?.is_online && ele?.auto)
          // ?.filter((ele: any) => !ele?.is_online)
          ?.map((ele: any) => ({
            ...ele,
            trang_thai: ele.trang_thai != 0 ? 3 : 0,
            id_tau_ca: id_tau_ca,
          })),
      };
      await pushFishServer();
      apiServices
        .Post({
          url: '/token/nk_khaithac/sync',
          body: params,
        })
        .then(async (_res: any) => {
          // clear local
          // Toast.show({
          //   type: ALERT_TYPE.SUCCESS,
          //   title: 'Thông báo',
          //   textBody: 'Đồng bộ dữ liệu hệ thống thành công.',
          // });
          // gọi api onDelete
          onDelete(diaryDelete);
          setDiaryLocal(_diaryLocal?.filter((ele: any) => ele?.is_online));
          await pullFishServer();
          props?.callback(true);
        })
        .catch((error: any) => {
          if (error == 'logout') {
            Toast.show({
              type: ALERT_TYPE.DANGER,
              title: 'Thông báo',
              textBody: 'Phiên đăng nhập hết hiệu lực. Vui lòng đăng nhập lại',
            });
            setTimeout(() => {
              onClearnStorage();
            }, 3000);
            return;
          } else {
            props?.callback('error');
          }
          Toast.show({
            type: ALERT_TYPE.DANGER,
            title: 'Thông báo',
            textBody: error?.message,
          });
        })
        .finally(() => {
          showLoading(false);
          // callback('location')
        });
    }
  };

  const pushFishServer = async () => {
    const id_nhom_loais =
      configQuickDiary?.listFish
        ?.map((ele: any) => ele._id)
        ?.filter((ele: any) => ele) ?? [];
    const _saveFish = await apiServices.Put({
      url: '/token/catchconfig',
      body: {
        id_nhom_loais: id_nhom_loais,
      },
    });
  };

  const pullFishServer: any = async () => {
    const res: any = await apiServices.Get({url: '/token/catchconfig'});
    if (res?.status == 'success') {
      const {id_nhom_loais = []} = res.data || {};
      setFavoriteArr(id_nhom_loais);
    }
  };

  const onChangeTime = (item: any) => {
    if (diaryCurrent?.thoi_gian_tha !== null) {
      //lưu current
      setDiary({
        ...diaryCurrent,
        thoi_gian_tha: item.thoi_gian_tha,
        thoi_gian_thu: item.thoi_gian_thu,
      });
    } else {
      // dạng list
      const _diaryLocal = (diaryLocal ?? [])?.map((el: any) => {
        if (el?.id === item?.id) {
          return {
            ...el,
            thoi_gian_tha: item.thoi_gian_tha,
            thoi_gian_thu: item.thoi_gian_thu,
            is_online: false,
          };
        }
        return el;
      });
      // list = diaryCurrent?.thoi_gian_tha !== null
      // ? [diaryCurrent, ..._diaryLocal]
      // : diaryLocal;
      // setDiaryLocal(_diaryLocal?.sort((a:any, b:any)=> new Date(a?.thoi_gian_tha) - new Date(b?.thoi_gian_tha)));

      // tạo item mới
      if (item?._id) {
        const cp_item = _diaryLocal.find((ele: any) => item._id === ele.id);
        const cp_item_id = uuid();
        const cp_final = {
          id: cp_item_id,
          item_id: cp_item_id,
          trang_thai: 2,
          is_online: false,
          vi_tri_tha: cp_item?.vi_tri_tha,
          vi_tri_thu: cp_item?.vi_tri_thu,
          thoi_gian_tha: cp_item?.thoi_gian_tha,
          thoi_gian_thu: cp_item?.thoi_gian_thu,
          san_luong: cp_item?.san_luong,
        };
        const list = diaryLocal.concat([cp_final]);
        let __diaryLocal = list.filter((ele: any) => ele.id != item?._id);
        setDiaryLocal(
          __diaryLocal?.sort(
            (a: any, b: any) =>
              new Date(a?.thoi_gian_tha).getTime() -
              new Date(b?.thoi_gian_tha).getTime(),
          ),
        );
        setDiaryDelete(diaryDelete.concat([item?._id]));
      } else {
        setDiaryLocal(
          _diaryLocal?.sort(
            (a: any, b: any) =>
              new Date(a?.thoi_gian_tha).getTime() -
              new Date(b?.thoi_gian_tha).getTime(),
          ),
        );
      }
    }
  };

  const onDelete = async (list: any) => {
    showLoading(true);
    try {
      for (let i = 0; i < list?.length; i++) {
        try {
          const _rsDelete = await apiServices.Delete({
            url: '/token/nk_khaithac/' + (list[i]._id ?? list[i]),
            // url: '/token/nk_khaithac/' + list[i]._id,
          });
        } catch (error) {
          console.log('rsDelete error: ', error);
        }
      }
      setDiaryDelete([]);
    } catch (error) {
      console.log('error: ', error);
    }
    const _rsFetch = await apiServices
      .Post({
        url: '/token/nk_khaithac',
        body: {
          filter: {
            id_tau_ca: user?.id_tau_ca?._id,
            trang_thai: [0, 1, 2, 3, 4],
          },
          sort: {
            createdAt: -1,
          },
          limit: 500,
        },
      })
      .then(data => {
        fetchDiary(data, false, user);
      })
      .catch(error => {
        console.log('error: ', error);
      })
      .finally(() => {
        hideLoading();
      });
    showLoading(false);
  };

  // const onUpdateOffDay =(item:any)=>{
  //   console.log('item: ', item);
  //   const list = diaryLocal.map((ele:any)=>{
  //     if(ele?.id == item.id){
  //     console.log('ele: ', ele);

  //     }
  //     return ele
  //   })

  // }

  return (
    <View style={{flex: 1}}>
      <FishList
        data={list}
        onRemoveFish={() => {}}
        onChangeTime={(item: any) => onChangeTime(item)}
        // onUpdateOffDay={(item: any) => onUpdateOffDay(item)}
        onUpdate={(item: any) => onPressDetail(item)}
      />
      {isConnected && isInternetReachable ? (
        <AppButton
          size={[1, 1]}
          type="secondary"
          text="Tạo nhanh chuyến biển"
          onPressLeft={() => {
            navigate(SCREENS.QUICK_CATCH_DIARY as never);
          }}
          textRight={
            isConnected && isInternetReachable
              ? 'Đồng bộ dữ liệu'
              : 'Điện thoại chưa kết nối 4G (Thiết bị)'
          }
          customStyle={{
            margin: setValue(16),
            backgroundColor: COLORS.TRANSPARENT,
          }}
          onPressRight={() => onSubmitSync(0)}
        />
      ) : isConnected && !isInternetReachable ? (
        <AppButton
          size={[1, 1]}
          type="secondary"
          text="Tạo nhanh chuyến biển"
          onPressLeft={() => {
            navigate(SCREENS.QUICK_CATCH_DIARY as never);
          }}
          textRight={'Kết nối GPS'}
          customStyle={{
            margin: setValue(16),
            backgroundColor: COLORS.TRANSPARENT,
          }}
          onPressRight={() => navigate(SCREENS.CONFIG_GPS_WIFI as never)}
        />
      ) : (
        <AppButton
          text="Tạo nhanh chuyến biển"
          onPress={() => {
            navigate(SCREENS.QUICK_CATCH_DIARY as never);
          }}
          customStyle={{
            margin: setValue(16),
            // backgroundColor:   'rgba(158, 131, 129, 0.5)',
            backgroundColor: COLORS.MAIN_COLOR,
          }}
        />
      )}
    </View>
  );
};

export default memo(ListNotSync);
