import {useNavigation} from '@react-navigation/native';
import {COLORS} from 'assets';
import {AppButton} from 'elements';
import SCREENS from 'navigation/Screens';
import React, {memo} from 'react';
import {View} from 'react-native';
import FishList from 'screens/catchDiary/components/FishList';
import useDiaryStore from 'stores/diaryStore';
import {METRICS} from 'utils';

const ListSynced = (props: any) => {
  const {navigate} = useNavigation();
  const {callback} = props;
  const {
    diary: {diaryServer},
  } = useDiaryStore();

  const onPressDetail = (item: any) => {
    navigate(SCREENS.DETAIL_HISTORY_MINING, {data: item, isSync: true});
  };

  return (
    <View style={{flex: 1}}>
      <FishList
        isSync={true}
        data={diaryServer}
        onRemoveFish={() => {}}
        onUpdate={onPressDetail}
      />

      <AppButton
        text={'Lấy vị trí'}
        customStyle={{
          margin: METRICS.nomal,
          backgroundColor: COLORS.MAIN_COLOR,
        }}
        onPress={callback}
      />
    </View>
  );
};

export default memo(ListSynced);
