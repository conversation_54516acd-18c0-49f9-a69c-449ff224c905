import ListNotSync from './components/ListNotSync';
import ListSynced from './components/ListSynced';
import {useNetInfoInstance} from '@react-native-community/netinfo';
import Tabs from 'components/TabView';
import {isEmpty} from 'lodash';
import React, {useEffect} from 'react';
import {ALERT_TYPE, Toast} from 'react-native-alert-notification';
import {SceneMap} from 'react-native-tab-view';
import {apiServices} from 'services';
import useAppStore from 'stores/appStore';
import useDiaryStore from 'stores/diaryStore';
import useStorageStore from 'stores/useStorageStore';

const routes = [
  {
    key: 'first',
    title: 'CHƯA ĐỒNG BỘ',
    iconName: 'clockcircle',
    iconType: 'AntDesign',
  },
  {
    key: 'second',
    title: 'ĐÃ ĐỒNG BỘ',
    iconName: 'checksquare',
    iconType: 'AntDesign',
  },
];

const HistoryMining = () => {
  const {
    credential: {user, token},
    onClearnStorage,
  } = useStorageStore();
  const {showLoading, hideLoading} = useAppStore();
  const {
    setDiaryServer,
    fetchDiary,
    diary: {diaryLocal},
  } = useDiaryStore();
  const {
    netInfo: {type, isConnected},
    refresh,
  } = useNetInfoInstance();

  useEffect(() => {
    InitPage(false);
  }, [token]);

  const InitPage = (value: boolean) => {
    if (!isConnected) {
      return;
    }
    showLoading(true);
    const id_tau_ca = user?.id_tau_ca?._id;
    apiServices
      .Post({
        url: '/token/nk_khaithac',
        body: {
          filter: {
            id_tau_ca: id_tau_ca,
            trang_thai: [0, 1, 2, 3, 4],
          },
          sort: {
            createdAt: -1,
          },
          limit: 500,
        },
      })
      .then(data => {
        fetchDiary(data, false, user);
      })
      .catch((error: any) => {
        if (error == 'logout') {
          Toast.show({
            type: ALERT_TYPE.DANGER,
            title: 'Thông báo',
            textBody: 'Phiên đăng nhập hết hiệu lực. Vui lòng đăng nhập lại',
          });
          setTimeout(() => {
            onClearnStorage();
          }, 3000);
          return;
        }
      })
      .finally(() => {
        showLoading(false);
      });
  };

  const renderScene = SceneMap({
    first: () => (<ListNotSync callback={InitPage} />) as never,
    second: () => (<ListSynced callback={InitPage} />) as never,
  });

  return (
    <Tabs
      headerTitle={'NHẬT KÝ KHAI THÁC'}
      renderScene={renderScene}
      routes={routes}
      iconRight="cw"
      onPressRight={() => InitPage(true)}
    />
  );
};

export default HistoryMining;
