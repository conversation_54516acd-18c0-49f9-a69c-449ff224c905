import {useNavigation} from '@react-navigation/native';
import {COLORS} from 'assets';
import {Content, DelayRenderComponent, Header, Table} from 'components';
import {
  AppButton,
  AppIcon,
  AppImage,
  AppText,
  BaseScreen,
  CustomTextField,
  FlexView,
  SizeBox,
} from 'elements';
import {IconType} from 'elements/AppIcon';

import SCREENS from 'navigation/Screens';
import React, {useState, useCallback, useMemo, memo} from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  StyleSheet,
  TouchableOpacity,
  View,
  Platform,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import KeyboardSpacer from 'react-native-keyboard-spacer';
import Modal from 'react-native-modal';
import {ModalContent} from 'react-native-modals';

import useAppStore from 'stores/appStore';
import useDiaryStore from 'stores/diaryStore';
import useFishStore from 'stores/fishStore';
import ToggleSwitch from 'toggle-switch-react-native';
import {FONT_TYPE, METRICS, globalStyles} from 'utils';
import {formatNumber} from 'utils/dateUtils';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  screenHeight,
  screenWidth,

} from 'utils/layout';
import uuid from 'uuid-random';

// Memoize the ItemDiary component to prevent unnecessary re-renders
export const ItemDiary = memo(
  ({
    item,
    isSync,
    onPressDelete,
    onPressUpdate,
  }: {
    item: any;
    isSync: boolean;
    onPressDelete?: () => void;
    onPressUpdate?: () => void;
  }) => {
    const styles = StyleSheet.create({
      btnDelete: {
        alignItems: 'center',
        borderColor: COLORS.GREY,
        borderRadius: 15,
        borderWidth: 1,
        height: 25,
        justifyContent: 'center',
        width: 25,
        marginLeft: 8,
      },
      itemContainerStyle: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        marginHorizontal: 4,
        paddingRight: 8,
        paddingVertical: 4,
      },

      tab1: {
        justifyContent: 'flex-start',
        flexDirection: 'row',
        width: screenWidth * 0.45,
      },
      itemTab1: {flex: 1, marginLeft: 16},

      tab2: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        width: screenWidth * 0.45,
      },
      textTab2: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
      },
      image: {width: 40, height: 40, borderRadius: 20, marginLeft: 8},
    });
    const khoi_luong = isNaN(Number(item?.khoi_luong))
      ? '0'
      : formatNumber(Number(item?.khoi_luong));

    // Handle press events
    const handleItemPress = useCallback(() => {
      if (onPressUpdate) {
        onPressUpdate();
      }
    }, [onPressUpdate]);

    const handleDeletePress = useCallback(() => {
      if (onPressDelete) {
        onPressDelete();
      }
    }, [onPressDelete]);

    return (
      <TouchableOpacity
        activeOpacity={isSync ? 1 : 0.3}
        key={item.ten}
        style={styles.itemContainerStyle}
        onPress={handleItemPress}>
        <View style={styles.tab1}>
          <AppImage
            customStyle={styles.image}
            resizeMode={'cover'}
            src={{
              uri: item.url,
            }}
          />
          <View style={styles.itemTab1}>
            <AppText
              font={FONT_TYPE.BOLD}
              // text={item.ten_dia_phuong ?? item.ten}
              text={item.ten ?? item.ten_dia_phuong}
            />
            <AppText color={COLORS.BLUE} text={item.ma_code} />
          </View>
        </View>
        <View style={styles.tab2}>
          <AppText
            color={COLORS.MAIN_COLOR}
            text={`${khoi_luong} (KG)`}
            customStyle={styles.textTab2}
          />
          {!isSync && (
            <View style={styles.btnDelete}>
              <AppIcon
                color={COLORS.GREY}
                name="cross"
                size={20}
                type={IconType.entypo}
                onPress={handleDeletePress}
              />
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  },
);

const DetailHistoryMining = ({route}: any) => {
  const {
    data: itemData,
    isSync,
    edit,
    data: _data,
    callback: _callback = () => {},
  } = route?.params;
  // để call back
  const [dataDetail, setDataDetail] = useState(itemData);
  const {showLoading} = useAppStore();
  const {
    clearDiary,
    setDiaryLocal,
    setDiary,

    setDiaryDelete,
    setConfigQuickListFish,
    diary: {
      diaryCurrent,
      diaryLocal,

      diaryDelete,
      configQuickDiary,
    },
  } = useDiaryStore();
  const {goBack, navigate} = useNavigation();

  const {setFavorite, fishData} = useFishStore();

  // Modal state
  const [khoiLuong, setKhoiLuong] = useState(0);
  const [isFav, setIsFav] = useState(true);
  const [openModal, setOpenModal] = useState<any>(false);
  const [fishSelected, setFishSelected] = useState<any>({});

  // Memoized callbacks
  const onPressDeleteFish = useCallback(
    (item: any) => {
      if (isSync) {
        return;
      }
      Alert.alert('Thông báo', 'Bạn muốn xóa loài này khỏi danh sách', [
        {
          text: 'Huỷ',
          onPress: () => {},
        },
        {
          text: 'Đồng ý',
          onPress: () => {
            if (edit === 1) {
              const _diaryCurrent = {...diaryCurrent};
              let _san_luong = _diaryCurrent?.san_luong || [];
              _diaryCurrent.san_luong = _san_luong.filter(
                (ele: any) => ele._id !== item._id,
              );
              setDiary(_diaryCurrent);
            } else if (edit === 2) {
              // Only create a new copy of array if needed
              const _diaryLocal = diaryLocal.map((ele: any) => {
                if (ele.id === dataDetail.id) {
                  const _data = {...dataDetail};
                  let _san_luong = _data?.san_luong || [];
                  _data.san_luong = _san_luong.filter(
                    (ele1: any) => ele1._id !== item._id,
                  );
                  return _data;
                }
                return ele;
              });

              if (dataDetail?._id || dataDetail.id) {
                const cp_item_id = uuid();
                // Create new item efficiently
                const filteredDiaryLocal = _diaryLocal.filter(
                  (ele: any) => ele.id != dataDetail.id,
                );

                const cp_final = {
                  id: cp_item_id,
                  item_id: cp_item_id,
                  trang_thai: 2,
                  is_online: false,
                  vi_tri_tha: dataDetail?.vi_tri_tha,
                  vi_tri_thu: dataDetail?.vi_tri_thu,
                  thoi_gian_tha: dataDetail?.thoi_gian_tha,
                  thoi_gian_thu: dataDetail?.thoi_gian_thu,
                  san_luong:
                    _diaryLocal.find((ele: any) => ele.id === dataDetail.id)
                      ?.san_luong || [],
                };

                // Avoid sorting multiple times by doing it only once after updates
                const sortedDiaryLocal = [...filteredDiaryLocal, cp_final].sort(
                  (a: any, b: any) =>
                    new Date(a?.thoi_gian_tha).getTime() -
                    new Date(b?.thoi_gian_tha).getTime(),
                );

                setDiaryLocal(sortedDiaryLocal);
                setDataDetail(cp_final);
                setDiaryDelete([...diaryDelete, dataDetail._id]);
              } else {
                // Sort only once after all updates
                setDiaryLocal(
                  _diaryLocal.sort(
                    (a: any, b: any) =>
                      new Date(a?.thoi_gian_tha).getTime() -
                      new Date(b?.thoi_gian_tha).getTime(),
                  ),
                );
              }
            }
          },
        },
      ]);
    },
    [
      isSync,
      edit,
      diaryCurrent,
      dataDetail,
      diaryLocal,
      diaryDelete,
      setDiary,
      setDiaryLocal,
      setDiaryDelete,
      setDataDetail,
    ],
  );

  // Callbacks
  const onCallback = useCallback(() => {
    goBack();
  }, [goBack]);

  const onFinishedDiary = useCallback(() => {
    if (edit === 2) {
      if (diaryLocal?.find((ele: any) => dataDetail._id === ele.id)) {
        const param = {
          ...diaryLocal?.find(
            (ele: any) => dataDetail.id === ele.id || dataDetail._id === ele.id,
          ),
          trang_thai: 2,
          san_luong: dataDetail?.san_luong.filter(
            (ele: any) =>
              !isNaN(ele?.khoi_luong) && Number(ele?.khoi_luong) >= 0,
          ),
          is_online: false,
        };

        // Create a copy of diaryLocal only once
        let _diaryLocal = [...diaryLocal];

        if (param) {
          if (
            _diaryLocal?.length > 0 &&
            _diaryLocal?.some((ele: any) => ele.id === param.id)
          ) {
            _diaryLocal = _diaryLocal.map((el: any) =>
              param.id === el.id ? {...el, ...param} : el,
            );
          } else {
            _diaryLocal.unshift(param);
          }
        }

        // Sort only once after all changes
        setDiaryLocal(
          _diaryLocal.sort(
            (a: any, b: any) =>
              new Date(a?.thoi_gian_tha).getTime() -
              new Date(b?.thoi_gian_tha).getTime(),
          ),
        );
      }
    } else if (edit === 1) {
      const param = {
        ...diaryCurrent,
        trang_thai: 2,
        thoi_gian_thu: new Date(),
        san_luong: diaryCurrent?.san_luong.filter(
          (ele: any) => !isNaN(ele?.khoi_luong) && Number(ele?.khoi_luong) >= 0,
        ),
        is_online: false,
      };

      // Create copies only when needed
      let _diaryLocal = [...diaryLocal];

      if (param) {
        if (
          _diaryLocal?.length > 0 &&
          _diaryLocal?.some((ele: any) => ele.id === param.id)
        ) {
          _diaryLocal = _diaryLocal.map((el: any) =>
            param.id === el.id ? {...el, ...param} : el,
          );
        } else {
          _diaryLocal.unshift(param);
        }
      }

      clearDiary();

      // Sort only once after all changes
      setDiaryLocal(
        _diaryLocal.sort(
          (a: any, b: any) =>
            new Date(a?.thoi_gian_tha).getTime() -
            new Date(b?.thoi_gian_tha).getTime(),
        ),
      );
    }

    goBack();
  }, [
    edit,
    dataDetail,
    diaryLocal,
    diaryCurrent,
    clearDiary,
    setDiaryLocal,
    goBack,
  ]);

  const onComfirmSaveLocal = useCallback(() => {
    if (edit === 2) {
      showLoading(true);
      onFinishedDiary();
      setTimeout(() => {
        showLoading(false);
      }, 2000);
      return;
    }

    Alert.alert(
      'Thông báo',
      'Bạn muốn THU LƯỚI và cập nhật thông tin sản lượng',
      [
        {
          text: 'Huỷ',
          onPress: () => {},
        },
        {
          text: 'Đồng ý',
          onPress: () => {
            onFinishedDiary();
            showLoading(true);
            setTimeout(() => {
              showLoading(false);
            }, 2000);
          },
        },
      ],
    );
  }, [edit, onFinishedDiary, showLoading]);

  const onPressUpdate = useCallback(
    (item: any) => {
      setFishSelected(item);
      const _item = fishData?.find((ele: any) => ele._id === item._id);
      if (_item) {
        setIsFav(_item?.isFavorite);
      }
      setKhoiLuong(item?.khoi_luong || 0);
      setOpenModal(true);
    },
    [fishData],
  );

  const onCloseModal = useCallback(() => {
    setKhoiLuong(0);
    setOpenModal(false);
  }, []);

  const onPressAdd = useCallback(() => {
    if (khoiLuong < 0) {
      return;
    }
    setFavFish(isFav);
    const param = {
      ...fishSelected,
      khoi_luong: khoiLuong,
    };

    if (edit === 1) {
      // Handle diaryCurrent updates
      const _diaryCurrent = {...diaryCurrent};
      let _san_luong = _diaryCurrent?.san_luong || [];
      const exist = _san_luong.filter((ele: any) => ele?._id === param?._id);

      if (exist.length > 0) {
        _san_luong = _san_luong.map((el: any) =>
          el?._id !== param?._id ? el : {...el, khoi_luong: Number(khoiLuong)},
        );
      } else {
        _san_luong.push(param);
      }

      _diaryCurrent.san_luong = _san_luong;
      setDiary(_diaryCurrent);
    } else {
      // Handle dataDetail updates
      const _dataDetail = {...dataDetail};
      let _san_luong = _dataDetail?.san_luong || [];
      const exist = _san_luong.filter((ele: any) => ele?._id === param?._id);

      if (exist.length > 0) {
        _san_luong = _san_luong.map((el: any) =>
          el?._id !== param?._id ? el : {...el, khoi_luong: Number(khoiLuong)},
        );
      } else {
        _san_luong.push(param);
      }

      _dataDetail.san_luong = _san_luong;
      setDataDetail(_dataDetail);

      // Now update diaryLocal with the new dataDetail
      if (diaryLocal?.length > 0) {
        let newDiaryLocal = diaryLocal.map((el: any) =>
          el.id === _dataDetail.id ? _dataDetail : el,
        );

        setDiaryLocal(newDiaryLocal);
      }
    }

    onCloseModal();
  }, [
    khoiLuong,
    isFav,
    fishSelected,
    edit,
    diaryCurrent,
    dataDetail,
    diaryLocal,
    setDiary,
    setDiaryLocal,
    setDataDetail,
    onCloseModal,
  ]);

  const setFavFish = useCallback(
    (value: any) => {
      setIsFav(value);
      const param = {...fishSelected, isFavorite: value};
      // Update in store
      setFavorite(param);
      // Update in quick config
      const {listFish} = configQuickDiary;
      const newListFish = listFish.concat([param]);
      setConfigQuickListFish(newListFish);
    },
    [fishSelected, configQuickDiary, setFavorite, setConfigQuickListFish],
  );

  const _onDiscard = useCallback(
    (value: boolean) => {
      if (value) {
        clearDiary();
        goBack();
      }
    },
    [clearDiary, goBack],
  );

  // Memoize fish data
  const fishDataFormatted = useMemo(() => {
    const targetData = edit === 1 ? diaryCurrent : dataDetail;
    return targetData?.san_luong || [];
  }, [edit, diaryCurrent, dataDetail]);

  // Memoize modal content
  const modalContent = useMemo(() => {
    if (!openModal) {
      return null;
    }

    return (
      <Modal
        style={{
          backgroundColor: 'transparent',
          width: '100%',
          marginHorizontal: 0,
          height: screenHeight,
        }}
        isVisible={openModal}>
        <ModalContent
          style={{
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
            height: screenHeight,
            padding: 0,
            flex: 1,
            width: screenWidth,
            backgroundColor: COLORS.WHITE,
          }}>
          <KeyboardAvoidingView
            enabled={openModal}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{flex: 1}}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <CustomTextField
                styleCustom={{flex: 1}}
                required={true}
                editable={false}
                placeholderText={'LOÀI'}
                multiline={true}
                value={
                  (fishSelected?.ten_dia_phuong ?? fishSelected.ten) +
                  ' (' +
                  fishSelected?.ma_code +
                  ')'
                }
              />

              <AppIcon
                color={COLORS.LIGHT_GREY}
                name="fish"
                size={scaleBaseOnScreenWidth(20)}
                type={IconType.ionicons}
              />
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <CustomTextField
                autoFocus={true}
                showSoftInputOnFocus={true}
                keyboardType="numeric"
                containerStyle={{fontFamily: FONT_TYPE.LIGHT, flex: 1}}
                required={true}
                editable={true}
                placeholderText={'SẢN LƯỢNG(KG)'}
                value={khoiLuong.toString()}
                onChangeText={(text: any) => {
                  if (isNaN(Number(text))) {
                    setKhoiLuong(0);
                    return;
                  }
                  if (!isNaN(Number(text)) && Number(text) > 200000) {
                    setKhoiLuong(200000);
                  } else {
                    setKhoiLuong(Number(text));
                  }
                }}
              />
              <AppIcon
                color={COLORS.LIGHT_GREY}
                name="balance-scale"
                size={scaleBaseOnScreenWidth(20)}
                type={IconType.fontawesome}
              />
            </View>

            <View
              style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <AppText
                color={COLORS.TEXT_PRIMARY}
                customStyle={{padding: METRICS.small_8}}
                font={FONT_TYPE.LIGHT}
                size={METRICS.text_16}
                text={'DANH MỤC LOÀI ƯU THÍCH'}
              />
              <ToggleSwitch
                isOn={isFav}
                onColor={COLORS.BLUE}
                offColor={COLORS.SMOKE}
                size="medium"
                onToggle={isOn => setFavFish(isOn)}
              />
            </View>
            <SizeBox h={METRICS.large_64} />
            <AppButton
              size={[1, 2]}
              text="HỦY"
              textRight="THÊM"
              type="secondary"
              onPressLeft={onCloseModal}
              onPressRight={onPressAdd}
            />
            <KeyboardSpacer
              topSpacing={
                Platform.OS === 'ios' ? 0 : scaleBaseOnScreenHeight(-200)
              }
            />
          </KeyboardAvoidingView>
        </ModalContent>
      </Modal>
    );
  }, [
    openModal,
    fishSelected,
    khoiLuong,
    isFav,
    onCloseModal,
    onPressAdd,
    setFavFish,
  ]);

  // Render fish item with memoization
  const renderFishItem = useCallback(
    ({item}: {item: any}) => (
      <ItemDiary
        item={item}
        isSync={!!isSync}
        onPressDelete={() => onPressDeleteFish(item)}
        onPressUpdate={() => onPressUpdate(item)}
      />
    ),
    [isSync, onPressDeleteFish, onPressUpdate],
  );

  return (
    <BaseScreen>
      <Header
        backgroundColor={COLORS.MAIN_COLOR}
        title={'CHI TIẾT MẺ LƯỚI'}
        iconTextRight="Thêm loài"
        onPressTextRight={() => {
          // @ts-ignore - Ignore the type error for navigation params
          navigate(SCREENS.SUGGEST_KIND, {
            id: dataDetail?.id,
            update: true,
            setDataDetail: setDataDetail,
          });
        }}
      />
      <Content>
        <DelayRenderComponent style={[globalStyles.flex1, {padding: 16}]}>
          <ScrollView>
            <Table
              // item={dataDetail}
              data={fishDataFormatted}
              renderItem={renderFishItem}
            />
          </ScrollView>
          <FlexView flex={1} />
          <AppButton
            size={[1, 1]}
            text="QUAY LẠI"
            textRight={edit === 1 ? 'THU LƯỚI' : 'CẬP NHẬT'}
            type="secondary"
            onPressLeft={onCallback}
            onPressRight={onComfirmSaveLocal}
          />
        </DelayRenderComponent>
        {modalContent}
      </Content>
    </BaseScreen>
  );
};

export default DetailHistoryMining;
