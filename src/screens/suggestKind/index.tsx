import FishMenu from './FishMenu';
import {useNavigation} from '@react-navigation/native';
import {COLORS} from 'assets';
import {Content, Header} from 'components';
import {
  AppButton,
  AppText,
  BaseScreen,
  CustomTextField,
  SizeBox,
} from 'elements';
import AppIcon, {IconType} from 'elements/AppIcon';
import {isEmpty} from 'lodash';
import React, {useState, useCallback, useMemo} from 'react';
import {KeyboardAvoidingView, Platform, View} from 'react-native';
import KeyboardSpacer from 'react-native-keyboard-spacer';
import Modal from 'react-native-modal';
import {ModalContent} from 'react-native-modals';
import useDiaryStore from 'stores/diaryStore';
import useFishStore from 'stores/fishStore';
import ToggleSwitch from 'toggle-switch-react-native';
import {FONT_TYPE, METRICS} from 'utils';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  screenHeight,
  screenWidth,
} from 'utils/layout';
import uuid from 'uuid-random';

const SuggestedKind = (props: any) => {
  const {id, setDataDetail, type, callback, update} = props.route?.params;
  const [openModal, setOpenModal] = useState<any>(false);
  const [fishSelected, setFishSelected] = useState<any>({});
  const {goBack} = useNavigation();
  const [khoiLuong, setKhoiLuong] = useState<any>(0);
  const [isFav, setIsFav] = useState<any>(true);

  const {fishData, setFavorite} = useFishStore();
  const {
    setDiary,
    setDiaryLocal,
    setDiaryDelete,
    diary: {diaryCurrent, diaryLocal, diaryDelete},
  } = useDiaryStore();

  const {
    setConfigQuickListFish,
    diary: {configQuickDiary},
  } = useDiaryStore();

  const onPressItem = useCallback(
    (item: any) => {
      if (type == 'quickCatchDiary') {
        // callback(item);
        // thực hiện lưu vào list tam
        setOpenModal(true);
        if (!isEmpty(item)) {
          setFishSelected(item);
        }
        // thêm khối lượng
        // goBack();
        return;
      }

      if (!isEmpty(item)) {
        setFishSelected(item);
        setIsFav(true);
      }
      setOpenModal(true);
    },
    [type, callback, goBack],
  );

  const onCloseModal = useCallback(() => {
    setKhoiLuong(0);
    setOpenModal(false);
  }, []);

  const setFavFish = useCallback(
    (value: any) => {
      setIsFav(value);
      const param = {...fishSelected, isFavorite: value};
      console.log('param: ', param);
      // thực hiện cập nhật vào store
      setFavorite(param);
      const {listFish} = configQuickDiary;
      const newListFish = listFish.concat([param]);
      // cập nhật vào vào quickconfig
      setConfigQuickListFish(newListFish);
    },
    [fishSelected, setFavorite, configQuickDiary, setConfigQuickListFish],
  );

  const onPressAdd = useCallback(() => {
    if (type == 'quickCatchDiary') {
      const param = {
        ...fishSelected,
        khoi_luong: khoiLuong,
      };
      callback(param);
      onCloseModal();
      goBack();
      return;
    }
    if (khoiLuong < 0 || !isFav) {
      return;
    }
    setFavFish(isFav);

    if (!update && id === diaryCurrent?.id) {
      const param = {
        ...fishSelected,
        khoi_luong: khoiLuong,
      };
      let _san_luong = diaryCurrent?.san_luong || [];
      const exist = _san_luong.filter((ele: any) => ele?._id === param?._id);

      if (exist.length > 0) {
        _san_luong = _san_luong.map((el: any) => {
          if (el?._id !== param?._id) {
            return el;
          }
          return {
            ...el,
            khoi_luong: Number(khoiLuong),
          };
        });
      } else {
        _san_luong.push(param);
      }

      const _diaryCurrent = {
        ...diaryCurrent,
        san_luong: _san_luong,
      };

      setDiary(_diaryCurrent);
      onCloseModal();
      goBack();
      return;
    }
    // Handle local diary case
    let _diaryLocal = [...diaryLocal];
    const itemUpdate = _diaryLocal.find((ele: any) => ele.id === id);

    if (!itemUpdate) {
      onCloseModal();
      return;
    }

    const param = {
      ...fishSelected,
      khoi_luong: khoiLuong,
    };

    let _san_luong = itemUpdate?.san_luong || [];
    const exist = _san_luong.filter((ele: any) => ele?._id === param?._id);

    if (exist.length > 0) {
      _san_luong = _san_luong?.map((el: any) => {
        if (el?._id !== param?._id) {
          return el;
        }
        return {
          ...el,
          khoi_luong: Number(khoiLuong),
        };
      });
    } else {
      _san_luong = _san_luong.concat([param]);
    }

    if (itemUpdate?._id) {
      const cp_item_id = uuid();
      // Create new item
      const cp_final = {
        id: cp_item_id,
        item_id: cp_item_id,
        trang_thai: 2,
        is_online: false,
        vi_tri_tha: itemUpdate?.vi_tri_tha,
        vi_tri_thu: itemUpdate?.vi_tri_thu,
        thoi_gian_tha: itemUpdate?.thoi_gian_tha,
        thoi_gian_thu: itemUpdate?.thoi_gian_thu,
        san_luong: _san_luong,
      };

      _diaryLocal = _diaryLocal.filter((ele: any) => ele.id != id);
      _diaryLocal.push(cp_final);

      setDiaryLocal(
        _diaryLocal?.sort(
          (a: any, b: any) =>
            new Date(a?.thoi_gian_tha).getTime() -
            new Date(b?.thoi_gian_tha).getTime(),
        ),
      );
      setDataDetail(cp_final);
      setDiaryDelete([...diaryDelete, id]);
    } else {
      const list = _diaryLocal.map((el: any) => {
        if (id === el.id) {
          return {...el, san_luong: _san_luong};
        }
        return el;
      });
      const cp_item = list.find((ele: any) => id === ele.id);
      setDiaryLocal(list);
      setDataDetail(cp_item);
    }

    onCloseModal();
    goBack();
  }, [
    khoiLuong,
    isFav,
    setFavFish,
    id,
    diaryCurrent,
    fishSelected,
    diaryLocal,
    diaryDelete,
    setDiary,
    setDiaryLocal,
    setDiaryDelete,
    setDataDetail,
    goBack,
    onCloseModal,
  ]);

  const onPressFish = useCallback(() => {}, []);

  const modalContent = useMemo(() => {
    if (!openModal) {
      return null;
    }

    return (
      <Modal
        style={{
          backgroundColor: 'transparent',
          width: '100%',
          marginHorizontal: 0,
          height: screenHeight,
        }}
        isVisible={openModal}>
        <ModalContent
          style={{
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
            height: screenHeight,
            padding: 0,
            flex: 1,
            width: screenWidth,
            backgroundColor: COLORS.WHITE,
          }}>
          <KeyboardAvoidingView
            enabled={openModal}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{flex: 1}}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <CustomTextField
                styleCustom={{flex: 1}}
                required={true}
                editable={false}
                placeholderText={'LOÀI'}
                multiline={true}
                value={
                  (fishSelected?.ten_dia_phuong ?? fishSelected.ten) +
                  ' (' +
                  fishSelected?.ma_code +
                  ')'
                }
              />

              <AppIcon
                color={COLORS.LIGHT_GREY}
                name="fish"
                size={scaleBaseOnScreenWidth(20)}
                type={IconType.ionicons}
              />
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <CustomTextField
                autoFocus={true}
                showSoftInputOnFocus={true}
                keyboardType="numeric"
                containerStyle={{fontFamily: FONT_TYPE.LIGHT, flex: 1}}
                required={true}
                editable={true}
                placeholderText={'SẢN LƯỢNG(KG)'}
                value={khoiLuong.toString()}
                onChangeText={(text: any) => {
                  if (isNaN(Number(text))) {
                    setKhoiLuong(0);
                    return;
                  }
                  if (!isNaN(Number(text)) && Number(text) > 200000) {
                    setKhoiLuong(200000);
                  } else {
                    setKhoiLuong(Number(text));
                  }
                }}
              />
              <AppIcon
                color={COLORS.LIGHT_GREY}
                name="balance-scale"
                size={scaleBaseOnScreenWidth(20)}
                type={IconType.fontawesome}
              />
            </View>
            {type !== 'quickCatchDiary' && (
              <View
                style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                <AppText
                  color={COLORS.TEXT_PRIMARY}
                  customStyle={{padding: METRICS.small_8}}
                  font={FONT_TYPE.LIGHT}
                  size={METRICS.text_16}
                  text={'DANH MỤC LOÀI ƯU THÍCH'}
                />
                <ToggleSwitch
                  isOn={isFav}
                  onColor={COLORS.BLUE}
                  offColor={COLORS.SMOKE}
                  size="medium"
                  onToggle={isOn => {
                    setFavFish(isOn);
                  }}
                />
              </View>
            )}
            <SizeBox h={METRICS.large_64} />
            <AppButton
              size={[1, 2]}
              text="HỦY"
              textRight="THÊM"
              type="secondary"
              onPressLeft={onCloseModal}
              onPressRight={onPressAdd}
            />
            <KeyboardSpacer
              topSpacing={
                Platform.OS == 'ios' ? 0 : scaleBaseOnScreenHeight(-200)
              }
            />
          </KeyboardAvoidingView>
        </ModalContent>
      </Modal>
    );
  }, [
    openModal,
    fishSelected,
    khoiLuong,
    isFav,
    onCloseModal,
    onPressAdd,
    setFavFish,
  ]);

  return (
    <BaseScreen>
      <Header backgroundColor={COLORS.MAIN_COLOR} title={'DANH MỤC THUỶ SẢN'} />
      <Content backgroundColor={COLORS.WHITE}>
        <FishMenu
          search
          selectedValue={null}
          onPressFish={onPressFish}
          onPressItem={onPressItem}
          fishData={fishData}
        />
      </Content>
      {modalContent}
    </BaseScreen>
  );
};

export default SuggestedKind;
