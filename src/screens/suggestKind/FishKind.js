import COLORS from '../../assets/theme/colors';
import {AppImage, AppText, AppIcon} from '../../elements';
import {IconType} from '../../elements/AppIcon';
import {globalStyles} from '../../utils';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  setValue,
} from '../../utils/layout';
import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';

export default React.memo(function (props) {
  return (
    <View style={styles.row}>
      <TouchableOpacity onPress={() => props.onPressFish(props?.url)}>
        <AppImage
          customStyle={styles.fishImage}
          resizeMode={'cover'}
          src={{
            uri: props?.url,
          }}
        />
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.rowContent}
        onPress={() => props.onPressItem(props)}>
        <View style={styles.infoContent}>
          <AppText customStyle={styles.name} text={props?.ten} />
          <AppText
            customStyle={styles.relation}
            // text={props?.ten_dia_phuong ?? props?.ten_dia_phuong_tc}
            text={props?.ten_dia_phuong_tc}
          />
          {/* <AppText customStyle={styles.relation} text={props?.ten_khoa_hoc} /> */}
        </View>
        <View style={styles.icon}>
          {props?.isFavorite && (
            <AppIcon
              color={COLORS.YELLOW}
              name={'star'}
              size={scaleBaseOnScreenWidth(10)}
              style={{marginRight: 16}}
              type={IconType.fontawesome}
            />
          )}
          <AppText customStyle={styles.code} text={props.ma_code} />
        </View>
      </TouchableOpacity>
    </View>
  );
});

const styles = StyleSheet.create({
  chevron: {
    alignSelf: 'center',
    color: 'rgb(181,131,44)',
    marginRight: scaleBaseOnScreenWidth(14),
  },
  code: {
    alignSelf: 'center',
    fontWeight: 'bold',
    marginRight: 16,
  },
  fishImage: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderColor: COLORS.SILVER,
    borderRadius: setValue(30),
    borderWidth: 1,
    height: setValue(60),
    justifyContent: 'center',
    resizeMode: 'contain',
    width: setValue(60),
  },
  image: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderColor: COLORS.SILVER,
    borderRadius: setValue(30),
    borderWidth: 1,
    height: setValue(60),
    justifyContent: 'center',
    resizeMode: 'contain',
    width: setValue(60),
  },
  infoContent: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: scaleBaseOnScreenHeight(8),
  },
  name: {
    ...globalStyles.textButton,
    color: COLORS.PRIMARY,
    fontWeight: 'bold',
    fontSize: scaleBaseOnScreenWidth(14),
  },
  relation: {
    ...globalStyles.textCaption,
    color: COLORS.GREY,
    fontSize: scaleBaseOnScreenWidth(13),
  },
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    height: scaleBaseOnScreenHeight(80),
  },
  rowContent: {
    flexDirection: 'row',
    flex: 1,
    height: '100%',
    marginLeft: scaleBaseOnScreenWidth(12),
  },
  shortName: {
    fontSize: scaleBaseOnScreenWidth(16),
  },
  icon: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
});
