import {IconType} from '../../elements/AppIcon';
import CustomTextField from '../../elements/TextField';
import StatefulComponent from '../../lib/StatefulComponent';
import {DUMPDATA} from '../../utils';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
} from '../../utils/layout';
import FishKind from './FishKind';
import isEmpty from 'lodash/isEmpty';
import moment from 'moment';
import React from 'react';
import {FlatList, StyleSheet, View} from 'react-native';
import {removeVietnameseTones} from 'utils/funcUtils';

class FishMenu extends StatefulComponent {
  // fish
  keyExtractor = item => item._id;

  renderItem = ({item}) => {
    return (
      <FishKind
        {...item}
        isConnected={this.props?.isConnected}
        onPressFish={this.props.onPressFish}
        onPressItem={this.props.onPressItem}
      />
    );
  };

  clearSearch = () =>
    this.setState({
      searchText: '',
    });

  // Memoized filtered and sorted data
  cachedData = null;
  cachedSearchText = '';
  cachedFishData = null;

  get data() {
    const {searchText} = this.state || {};
    const fishData = this.props?.fishData || [];

    // Use cached result if inputs haven't changed
    if (
      this.cachedData &&
      this.cachedSearchText === searchText &&
      this.cachedFishData === fishData
    ) {
      return this.cachedData;
    }

    let result = [...fishData];

    // Nếu có searchText, lọc trước
    if (!isEmpty(searchText)) {
      const search = searchText.toLowerCase();
      const searchNoTone = removeVietnameseTones(search);

      result = result.filter(
        ({
          ma_code = '',
          ten_dia_phuong = '',
          ten = '',
          ten_dia_phuong_tc = '',
        }) => {
          const ten_dp = ten_dia_phuong.toLowerCase();
          const ten_dp_tc = ten_dia_phuong_tc.toLowerCase();
          const name = ten.toLowerCase();

          // Early return optimization - check the most common/fastest conditions first
          if (ma_code.toLowerCase().includes(search)) {
            return true;
          }
          if (name.includes(search)) {
            return true;
          }
          if (ten_dp.includes(search)) {
            return true;
          }
          if (ten_dp_tc.includes(search)) {
            return true;
          }

          // More expensive operations - only if needed
          const ten_khong_dau = removeVietnameseTones(name);
          if (ten_khong_dau.includes(searchNoTone)) {
            return true;
          }

          const ten_dia_phuong_khong_dau = removeVietnameseTones(ten_dp);
          if (ten_dia_phuong_khong_dau.includes(searchNoTone)) {
            return true;
          }

          const ten_dia_phuong_tc_khong_dau = removeVietnameseTones(ten_dp_tc);
          return ten_dia_phuong_tc_khong_dau.includes(searchNoTone);
        },
      );
    }

    // Sắp xếp: isFavorite trước, sau đó theo createdAt mới nhất
    result.sort((a, b) => {
      if (a.isFavorite === b.isFavorite) {
        return moment(b.createdAt) - moment(a.createdAt);
      }
      return b.isFavorite ? 1 : -1; // true lên trước
    });

    // Cache the result
    this.cachedData = result;
    this.cachedSearchText = searchText;
    this.cachedFishData = fishData;

    return result;
  }

  render() {
    return (
      <View style={styles.body}>
        <CustomTextField
          appendIcon="cross"
          appendIconType={IconType.entypo}
          containerStyle={styles.search}
          placeholderText={'TÌM KIẾM'}
          value={this.state?.searchText}
          onChangeText={this.onChangeState('searchText')}
          onPressAppend={this.clearSearch}
        />
        <FlatList
          data={this.data}
          keyExtractor={this.keyExtractor}
          renderItem={this.renderItem}
          style={styles.flatlist}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={5}
          removeClippedSubviews={true}
        />
      </View>
    );
  }
}

export default FishMenu;

const styles = StyleSheet.create({
  body: {
    flex: 1,
  },
  flatlist: {
    marginTop: scaleBaseOnScreenHeight(10),
    paddingLeft: scaleBaseOnScreenWidth(16),
  },
  search: {
    marginLeft: scaleBaseOnScreenWidth(20),
    marginTop: scaleBaseOnScreenHeight(10),
    width: '100%',
  },
});
