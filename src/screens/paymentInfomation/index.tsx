import {useNavigation} from '@react-navigation/native';
import {COLORS} from 'assets';
import {Content, DelayRenderComponent, Header} from 'components';
import {AppButton, AppText, BaseScreen, FlexView, SizeBox} from 'elements';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import {ALERT_TYPE, Toast} from 'react-native-alert-notification';
import {FONT_TYPE, METRICS, globalStyles} from 'utils';
import {scaleBaseOnScreenHeight} from 'utils/layout';

const TextWithValue = ({title, content}: {title: string; content: string}) => {
  return (
    <View style={styles.textContainer}>
      <AppText color={COLORS.BLACK} size={METRICS.text_18} text={title} />
      <AppText
        color={COLORS.BLACK}
        font={FONT_TYPE.BOLD}
        size={METRICS.text_16}
        text={content}
      />
    </View>
  );
};

const PaymentInfomation = () => {
  const {goBack} = useNavigation();
  return (
    <BaseScreen>
      <Header
        backgroundColor={COLORS.MAIN_COLOR}
        title={'THÔNG TIN THANH TOÁN'}
      />
      <Content>
        <DelayRenderComponent style={globalStyles.flex1}>
          <View style={styles.container}>
            <AppText
              color={COLORS.BLACK}
              text="VUI LÒNG CHUYỂN TIỀN ĐẾN TÀI KHOẢN BÊN DƯỚI ĐỂ THỰC HIỆN THANH TOÁN"
            />
            <SizeBox h={METRICS.nomal} />
            <AppText
              color={COLORS.BLACK}
              font={FONT_TYPE.BOLD}
              text="* Lưu ý: Nhập chính xác Nội dung chuyển khoản khi thanh toán"
            />
            <FlexView flex={1} />
            <TextWithValue
              content={'Techcombank'}
              title={'Ngân hàng thụ hưởng'}
            />
            <TextWithValue content={'************'} title={'Số tài khoản'} />
            <TextWithValue content={'************'} title={'Số tiền'} />
            <TextWithValue content={'3,500,000 VND'} title={'Họ và tên'} />
            <TextWithValue
              content={'ECDT DNA 98402 TS 24'}
              title={'Nội dung'}
            />
            <SizeBox h={32} />
            <AppButton
              size={[1, 1]}
              text="Sao chép NDCK"
              textRight="LƯU MÃ QR"
              type="secondary"
              onPressLeft={() =>
                Toast.show({
                  type: ALERT_TYPE.INFO,
                  title: 'Warning',
                  textBody: 'Đã sao chép thành công',
                })
              }
              onPressRight={() => {
                goBack();
              }}
            />
          </View>
        </DelayRenderComponent>
      </Content>
    </BaseScreen>
  );
};

export default PaymentInfomation;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: scaleBaseOnScreenHeight(15),
    padding: 16,
  },
  row: {
    flexDirection: 'row',
    marginTop: scaleBaseOnScreenHeight(10),
  },
  textContainer: {
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    paddingVertical: 8,
  },
  textField: {
    marginTop: scaleBaseOnScreenHeight(5),
  },
});
