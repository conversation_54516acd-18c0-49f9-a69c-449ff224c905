import {Content, DelayRenderComponent, Header} from '../../components';
import {COLORS, IMAGES} from 'assets';
import {
  AppButton,
  AppIcon,
  AppImage,
  AppText,
  BaseScreen,
  FlexView,
  SizeBox,
} from 'elements';
import {IconType} from 'elements/AppIcon';
import {useState} from 'react';
import React from 'react';
import {Dimensions, TouchableOpacity, View} from 'react-native';
import {ALERT_TYPE, Toast} from 'react-native-alert-notification';
import QRCode from 'react-native-qrcode-svg';
import {FONT_TYPE, METRICS, globalStyles} from 'utils';

const {width} = Dimensions.get('window');

const CheckBox = ({
  text,
  textSecond,
  index,
  onPressIndex,
  isChecked,
}: {
  text: string;
  textSecond: string;
  index: number;
  onPressIndex: (index: number) => void;
  isChecked: boolean;
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.9}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
      onPress={() => onPressIndex(index)}>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <AppIcon
          color={COLORS.PRIMARY}
          name={isChecked ? 'radio-btn-active' : 'radio-btn-passive'}
          size={20}
          type={IconType.fontisto}
        />
        <SizeBox w={8} />
        <AppText color={COLORS.BLACK} size={METRICS.text_18} text={text} />
      </View>
      <AppText
        color={COLORS.BLACK}
        font={FONT_TYPE.BOLD}
        size={METRICS.text_20}
        text={textSecond}
      />
    </TouchableOpacity>
  );
};

const RenewRegistration = () => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  return (
    <BaseScreen>
      <Header backgroundColor={COLORS.MAIN_COLOR} title={'GIA HẠN DỊCH VỤ'} />
      <Content>
        <DelayRenderComponent
          style={[globalStyles.flex1, {padding: METRICS.nomal}]}>
          <AppImage
            customStyle={{
              width: width - 32,
              height: 200,
            }}
            src={IMAGES.IMG_CARD}
          />
          <FlexView flex={0.5} />
          <CheckBox
            index={0}
            isChecked={selectedIndex === 0}
            text={'Gói 1 năm'}
            textSecond={'1.200.000 đ'}
            onPressIndex={i => setSelectedIndex(i)}
          />
          <FlexView flex={0.5} />
          <CheckBox
            index={1}
            isChecked={selectedIndex === 1}
            text={'Gói 2 năm'}
            textSecond={'2.400.000 đ'}
            onPressIndex={i => setSelectedIndex(i)}
          />
          <FlexView flex={0.5} />
          <CheckBox
            index={2}
            isChecked={selectedIndex === 2}
            text={'Gói 3 năm'}
            textSecond={'3.600.000 đ'}
            onPressIndex={i => setSelectedIndex(i)}
          />
          <FlexView flex={1} />
          <View
            style={{
              alignSelf: 'center',
              borderRadius: METRICS.nomal,
              overflow: 'hidden',
            }}>
            <QRCode size={180} value={`GTO-${selectedIndex}`} />
          </View>
          <FlexView flex={1} />
          <AppButton
            text="LƯU MÃ QR"
            onPress={() =>
              Toast.show({
                type: ALERT_TYPE.SUCCESS,
                title: 'Thành công',
                textBody: 'Đã lưu mã QR Code',
              })
            }
          />
        </DelayRenderComponent>
      </Content>
    </BaseScreen>
  );
};

export default RenewRegistration;
