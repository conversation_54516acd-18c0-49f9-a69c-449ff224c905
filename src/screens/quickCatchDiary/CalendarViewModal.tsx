import {COLORS} from 'assets';
import React, {useState} from 'react';
import {View, Text, Modal, StyleSheet} from 'react-native';
import {Calendar} from 'react-native-calendars';
import {Button} from 'react-native-elements';

const CalendarViewModal = ({
  visible,
  onClose,
  onConfirm,
  minDate,
  maxDate,
}: any) => {
  const [selectedDates, setSelectedDates] = useState({from: null, to: null});
  const isBefore = (date1: any, date2: any) => date1 < date2; // date1 trước date2
  const isAfter = (date1: any, date2: any) => date1 > date2; // date1 sau date2
  const isSameDate = (date1: any, date2: any) => {
    return (
      date1.toISOString().split('T')[0] === date2.toISOString().split('T')[0]
    );
  };
  // Xử lý chọn ngày
  const handleDayPress = (day: any) => {
    // console.log(isAfter(selectedDates.from, day.dateString)); // true

    if (!selectedDates.from || (selectedDates.from && selectedDates.to)) {
      // Chọn ngày bắt đầu
      setSelectedDates({from: day.dateString, to: null});
    } else {
      // console.log(isAfter(selectedDates.from, day.dateString)); // true
      if (isAfter(selectedDates.from, day.dateString)) {
        setSelectedDates({from: day.dateString, to: selectedDates.from});
      } else {
        // Chọn ngày kết thúc
        setSelectedDates({...selectedDates, to: day.dateString});
      }
    }
  };

  // Tô màu khoảng thời gian đã chọn
  const getMarkedDates = () => {
    const current = new Date().toISOString().split('T')[0];
    let marked: any = {};
    marked[`${current}`] = {
      current: true,
      color: COLORS.BITTER_GRAY,
      textColor: 'black',
      borderRadius: 0,
    };
    if (!selectedDates.from) {
      return marked;
    }
    marked = {
      [selectedDates.from]: {
        startingDay: true,
        color: COLORS.BITTER_GRAY,
        textColor: 'black',
      },
    };

    if (selectedDates.to) {
      marked[selectedDates.to] = {
        endingDay: true,
        color: COLORS.BITTER_GRAY,
        textColor: 'black',
      };
      let start = new Date(selectedDates.from);
      let end = new Date(selectedDates.to);
      let date = new Date(start);

      while (date < end) {
        date.setDate(date.getDate() + 1);
        if (date < end) {
          marked[date.toISOString().split('T')[0]] = {
            color: COLORS.BITTER_GRAY_LIGHT,
            textColor: 'black',
          };
        }
      }
    }
    marked[`${current}`] = {
      current: true,
      color: COLORS.YELLOW_SECONDARY,
      textColor: 'black',
      borderRadius: 5,
    };

    return marked;
  };

  return (
    <Modal visible={visible} transparent animationType="slide">
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.title}>Chọn khoảng thời gian</Text>
          <Calendar
            markingType="period"
            markedDates={getMarkedDates()}
            onDayPress={handleDayPress}
            maxDate={maxDate}
            minDate={minDate}
          />

          <View style={styles.buttonContainer}>
            <Button
              title="Hủy"
              type="outline"
              onPress={onClose}
              buttonStyle={{
                borderRadius: 20,
                width: 140,
                borderColor: COLORS.MAIN_COLOR,
                borderWidth: 2,
              }}
              titleStyle={{color: COLORS.MAIN_COLOR}}
            />
            <Button
              title="Xác nhận"
              onPress={() => onConfirm(selectedDates.from, selectedDates.to)}
              disabled={!selectedDates.from || !selectedDates.to}
              buttonStyle={{
                borderRadius: 20,
                width: 140,
                backgroundColor: COLORS.MAIN_COLOR,
                borderColor: COLORS.MAIN_COLOR,
                borderWidth: !selectedDates.from || !selectedDates.to ? 0 : 2,
              }}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: 350,
    backgroundColor: '#FFF',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    elevation: 5,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2C3E50',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 20,
  },
});

export default CalendarViewModal;

{
  /* <View style={styles.container}>
          <Text style={styles.title}>📅 Chọn Ngày + Giờ</Text>

            <TouchableOpacity onPress={() => setModalVisible(true)} style={styles.selectButton}>
              <Text>📆 Chọn khoảng thời gian</Text>
            </TouchableOpacity>

            {selectedDates.from && selectedDates.to && (
              <Text style={styles.dateText}>
                🕒 Từ: {selectedDates.from?.toLocaleString()} {"\n"}
                🕒 Đến: {selectedDates.to?.toLocaleString()}
              </Text>
            )}

            <CalendarModal
              visible={modalVisible}
              onClose={() => setModalVisible(false)}
              onConfirm={handleConfirm}
            />
          </View> */
}
