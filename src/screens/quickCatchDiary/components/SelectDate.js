import SelectTextField from './SelectTextField';
import {StatefulComponent} from 'lib';
import moment from 'moment';
import React from 'react';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {formatDate} from 'utils';

export default class SelectDate extends StatefulComponent {
  componentDidMount() {
    this.fixData();
  }

  fixData = () => {
    if (
      typeof this.props?.value === 'string' &&
      this.props?.value.includes('/')
    ) {
      const {onSubmit = () => {}} = this.props;
      this.setState({
        value: moment(this.props?.value, 'DD/MM/YYYY'),
      });
      onSubmit(moment(this.props?.value, 'DD/MM/YYYY'));
    }
  };

  onSummit = value => {
    const {onSubmit = () => {}} = this.props;
    this.toggleBooleanState('showModal', false)();
    setTimeout(() => {
      onSubmit(value);
    }, 200);
  };

  render() {
    const {
      value,
      placeholderText,
      style,
      required,
      shadow,
      mode,
      editable = true,
      ...rest
    } = this.props;
    const {showModal} = this.state;
    let date = this.props?.value;
    if (typeof date === 'string' && date.includes('/')) {
      date = moment(this.props?.value, 'DD/MM/YYYY');
    }

    return (
      <React.Fragment>
        <SelectTextField
          style={style}
          required={required}
          shadow={shadow}
          disabled={!editable}
          value={formatDate(
            date,
            mode === 'date' ? 'DD/MM/YYYY' : 'HH:mm:ss - DD/MM/YYYY',
            '',
          )}
          placeholderText={placeholderText}
          onPress={this.toggleBooleanState('showModal', true)}
          appendIcon="calendar"
          appendIconType="Foundation"
          {...rest}
        />
        {editable ? (
          <DateTimePickerModal
            isVisible={showModal}
            locale="vi"
            date={value ? new Date(value) : new Date()}
            maximumDate={this.props?.maximumDate}
            minimumDate={this.props?.minimumDate}
            mode={mode || 'datetime'}
            onConfirm={this.onSummit}
            onCancel={this.toggleBooleanState('showModal', false)}
          />
        ) : null}
      </React.Fragment>
    );
  }
}
