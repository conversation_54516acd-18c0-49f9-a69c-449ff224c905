import CalendarModal from './CalendarViewModal';
import {useNetInfoInstance} from '@react-native-community/netinfo';
import {useNavigation} from '@react-navigation/native';
import {COLORS} from 'assets';
import {DelayRenderComponent} from 'components';
import Header from 'components/Header';
import Loading from 'components/Loading';
import SelectDate from 'components/SelectDate';
import {AppButton, AppIcon, AppText, BaseScreen, CustomTextField, SizeBox} from 'elements';
import {IconType} from 'elements/AppIcon';
import moment from 'moment';
import SCREENS from 'navigation/Screens';
import React, {useEffect, useState, useCallback, useMemo, memo} from 'react';
import {View, StyleSheet, TouchableOpacity, Alert, KeyboardAvoidingView, Platform} from 'react-native';
import {ALERT_TYPE, Toast} from 'react-native-alert-notification';
import {ScrollView} from 'react-native-gesture-handler';
import KeyboardSpacer from 'react-native-keyboard-spacer';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { ModalContent } from 'react-native-modals';
import Modal from 'react-native-modal';

import reactotron from 'reactotron-react-native';
import {SelectFishRow} from 'screens/catchDiary/components/SelectFishRow';
import {apiServices} from 'services';
import useDiaryStore from 'stores/diaryStore';
import useFishStore from 'stores/fishStore';
import useStorageStore from 'stores/useStorageStore';
import {FONT_TYPE, globalStyles, METRICS} from 'utils';
import {scaleBaseOnScreenHeight, scaleBaseOnScreenWidth, screenHeight, screenWidth, setValue} from 'utils/layout';
import uuid from 'uuid-random';
import _ from 'lodash'

// Memoized fish item component
const FishItem = memo(
  ({item, onRemove}: {item: any; onRemove: (id: string) => void}) => {
    const handleRemove = useCallback(() => {
      onRemove(item._id);
    }, [item._id, onRemove]);

    return (
      <SelectFishRow
        key={item?._id}
        image={item?.url}
        title={item?.ten_dia_phuong ?? item?.ten}
        content={item?.ma_code}
        border={false}
        remove={true}
        typequick={true}
        openImage={false}
        onRemove={handleRemove}
        {...item}
      />
    );
  },
);

// Memoized fish list component
// const FishList = memo(
//   ({
//     fishList,
//     onRemoveFish,
//   }: {
//     fishList: any[];
//     onRemoveFish: (id: string) => void;
//   }) => {
//     if (!fishList || fishList.length === 0) {
//       return (
//         <TouchableOpacity
//           style={[styles.view_content, {justifyContent: 'center'}]}
//           onPress={() => {}}>
//           <AppText
//             color={COLORS.LIGHT_GREY}
//             font="REGULAR"
//             text={'CHỌN LOÀI CÁ'}
//           />
//         </TouchableOpacity>
//       );
//     }

//     return (
//       <View style={styles.bodyRow}>
//         {fishList.map((fish, idx) => (
//           <View key={fish._id || idx} style={styles.box}>
//             <FishItem item={fish} onRemove={onRemoveFish} />
//           </View>
//         ))}
//       </View>
//     );
//   },
// );
// const distributeOneFish = (
//   totalWeight: number,
//   batchCount: number,
//   thap: number,
//   cao: number
// ): number[] => {
//   const result = Array(batchCount).fill(0);

//   if (totalWeight === 0) return result;

//   // Nếu không đủ để phân phối tối thiểu cho tất cả → chỉ gán cho một vài batch
//   const maxAssignable = Math.min(batchCount, Math.floor(totalWeight / (thap || 1)));

//   if (maxAssignable === 0) {
//     // Gán hết vào 1 batch random
//     const randIndex = Math.floor(Math.random() * batchCount);
//     result[randIndex] = totalWeight;
//     return result;
//   }

//   // Gán thap cho những batch được chọn
//   const indexes = _.shuffle([...Array(batchCount).keys()]).slice(0, maxAssignable);
//   indexes.forEach(i => result[i] = thap);
//   let remaining = totalWeight - thap * maxAssignable;

//   // Phân phối phần dư ngẫu nhiên
//   const availableIndexes = indexes.filter(i => result[i] < cao);
//   while (remaining > 0 && availableIndexes.length > 0) {
//     const randIndex = _.sample(availableIndexes);
//     if (randIndex !== undefined && result[randIndex] + 1 <= cao) {
//       result[randIndex]++;
//       remaining--;
//     }
//   }

//   return result;
// };

const distributeOneFishAdvanced = (
  totalWeight: number,
  batchCount: number,
  thapToiThieu: number,
  thapTrungBinh: number,
  thapCao: number,
  cao: number
): number[] => {
  const result = Array(batchCount).fill(0);

  if (totalWeight <= 0) return result;

  const maxBatch = Math.floor(totalWeight / thapToiThieu);
  const minBatch = Math.floor(totalWeight / thapCao);

  const expectedBatchCount =
    batchCount > maxBatch
      ? _.random(minBatch, maxBatch)
      : batchCount;

  const selectedIndexes = _.sampleSize([...Array(batchCount).keys()], expectedBatchCount);

  let remaining = totalWeight;

  // Bước 1: Gán khởi đầu trong khoảng [trung bình, cao]
  for (const i of selectedIndexes) {
    const randValue = _.random(thapTrungBinh, thapCao);
    const actual = Math.min(randValue, cao, remaining);
    result[i] = actual;
    remaining -= actual;
  }

  // Bước 2: Nếu phần dư nhỏ < thapToiThieu → phân vào các batch đã có sản lượng
  while (remaining > 0 && remaining < thapToiThieu) {
    const candidates = selectedIndexes.filter(i => result[i] > 0 && result[i] < cao);
    if (candidates.length === 0) break;
    const i = _.sample(candidates)!;
    result[i]++;
    remaining--;
  }

  // Bước 3: Nếu vẫn còn → cộng dồn vào các mẻ đã có sản lượng > 0
  while (remaining > 0) {
    const candidates = selectedIndexes.filter(i => result[i] > 0 && result[i] < cao);
    if (candidates.length === 0) break;
    const i = _.sample(candidates)!;
    const space = cao - result[i];
    if (space <= 0) continue;
    const addAmount = Math.min(space, remaining);
    result[i] += addAmount;
    remaining -= addAmount;
  }

  return result;
};



// const distributeAllFishToBatches = (batches: any[], listFish: any[]) => {
//   const batchCount = batches.length;
//   if (batchCount === 0) return [];

//   const distributionMap: number[][] = [];

//   // Tạo phân phối trước cho từng loài cá
//   listFish.forEach((fish, fishIndex) => {
//     let totalWeight = Math.round(fish.khoi_luong || 0);
//     const thap = fish.khoi_luong_thap ?? 0;
//     const cao = fish.khoi_luong_cao === 0 ? Infinity : (fish.khoi_luong_cao ?? Infinity);

//     // Nếu tổng không nằm trong khoảng cho phép → gán toàn bộ 0

//     if (totalWeight < thap * batchCount || totalWeight > cao * batchCount) {
//       console.log('totalWeight < thap * batchCount: ', totalWeight < thap * batchCount);
//       // distributionMap[fishIndex] = Array(batchCount).fill(0);
//       const result = Array(batchCount).fill(0);
//       if (totalWeight <= 0 || thap === 0) {
//         distributionMap[fishIndex] = result;
//         return;
//       }

//       // Bước 1: Chọn ngẫu nhiên số lượng batch có thể cấp `thap`
//       const assignableBatches = Math.min(batchCount, Math.floor(totalWeight / thap));

//       console.log('assignableBatches: ', assignableBatches);
//       const shuffledIndexes = _.shuffle([...Array(batchCount).keys()]);
//       const selectedIndexes = shuffledIndexes.slice(0, assignableBatches);
//       console.log('selectedIndexes: ', selectedIndexes);
//       // Bước 2: Gán `thap` cho từng batch được chọn
//       selectedIndexes.forEach(i => {
//         result[i] = thap;
//         totalWeight -= thap;
//       });

//       // Bước 3: Phân phần dư còn lại (random) cho các batch đã chọn
//       // trường hợp nhỏ hơn tổng > 0 và không đủ để gán cho tất cả batch
//       if(selectedIndexes.length == 0 ){
//         result[0] = totalWeight;
//         distributionMap[fishIndex] = result;
//         return 
//       }

//       while (totalWeight > 0 && selectedIndexes.length > 0) {
//         const i = _.sample(selectedIndexes)!;
//         const maxEach = isFinite(cao) ? cao : totalWeight;
//         if (result[i] + 1 <= maxEach) {
//           result[i]++;
//           totalWeight--;
//         }
//       }

//       distributionMap[fishIndex] = result;
//     } else {
//       distributionMap[fishIndex] = distributeOneFish(totalWeight, batchCount, thap, cao);
//     }
//   });

//   // Gán sản lượng cho từng batch từ map
//   return batches.map((batch, batchIndex) => {
//     const san_luong = listFish.map((fish, fishIndex) => ({
//       ...fish,
//       khoi_luong: distributionMap[fishIndex][batchIndex],
//     }));

//     return {
//       ...batch,
//       san_luong,
//     };
//   });
// };
const distributeAllFishToBatches = (batches: any[], listFish: any[]) => {
  const batchCount = batches.length;
  if (batchCount === 0) return [];

  const distributionMap: number[][] = [];

  listFish.forEach((fish, fishIndex) => {
    const totalWeight = Math.round(fish.khoi_luong || 0);
    const thapToiThieu = fish.khoi_luong_thap_toi_thieu ?? 30;
    const thapTrungBinh = fish.khoi_luong_thap_trung_binh ?? 45;
    const thapCao = fish.khoi_luong_thap_cao ?? 70;
    const cao = fish.khoi_luong_cao === 0 ? Infinity : (fish.khoi_luong_cao ?? Infinity);

    distributionMap[fishIndex] = distributeOneFishAdvanced(
      totalWeight,
      batches.length,
      thapToiThieu,
      thapTrungBinh,
      thapCao,
      cao
    );
  });

  // Tổng hợp lại kết quả cho từng batch
  return batches.map((batch, batchIndex) => {
    const san_luong = listFish.map((fish, fishIndex) => ({
      ...fish,
      khoi_luong: distributionMap[fishIndex][batchIndex],
    }));

    return {
      ...batch,
      san_luong,
    };
  });
};


const QuickCatchDiary = () => {

  // modal
  const [openModal, setOpenModal] = useState<any>(false);
  const [khoiLuong, setKhoiLuong] = useState(0);
  const [fishSelected, setFishSelected] = useState<any>({});
  const {
    setConfigQuickListFish,
    clearDiaryDelete,
    fetchDiary,
    setDiaryLocal,
    setConfigQuickDiary,
    diary: {configQuickDiary, lastDiary},
  } = useDiaryStore();
  const { setFavoriteArr, setFavorite, fishData} = useFishStore();

  const onCloseModal = useCallback(() => {
    setKhoiLuong(0);
    setOpenModal(false);
  }, []);

  const onChangeWeightFish= useCallback(
    (item: any) => {
      setFishSelected(item);
      setKhoiLuong(item?.khoi_luong || 0);
      setOpenModal(true);
    },
    [fishData],
  );
  

  const onPressAdd = useCallback(() => {
    if (khoiLuong < 0) {
      return;
    } 
    // setFavFish(isFav);
    const param = {
      ...fishSelected,
      khoi_luong: khoiLuong,
    };
    // lưu vào list
    const mergedUniqueFishTemp = listFish?.map((ele:any)=>({
      ...ele,
      khoi_luong: ele._id === param._id ? param.khoi_luong : ele.khoi_luong,
    }))
    setListFish(mergedUniqueFishTemp);
    onCloseModal();
  },[khoiLuong, fishSelected])

  // Main
  const {navigate, goBack} = useNavigation();


  const {
    netInfo: {type, isConnected, isInternetReachable},
    refresh,
  } = useNetInfoInstance();

  useEffect(() => {
    if (isConnected == true && isInternetReachable == true) {
      loadingPage();
    }
  }, [isConnected]);

  const loadingPage = useCallback(async () => {
    try {
      const res: any = await apiServices.Get({url: '/token/catchconfig'});
      if (res?.status !== 'success') {
        return;
      }

      // Destructure all data at once
      const {
        id_nhom_loais = [],
        khoi_luongs = [],
        thoi_gian_khai_thac_from,
        thoi_gian_khai_thac_to,
        thoi_gian_tha_from,
        thoi_gian_tha_to,
        thoi_gian_thu_from,
        thoi_gian_thu_to,
      } = res.data || {};
      // Process fish data in one go
      const listFishMatched = id_nhom_loais
        .map((id: string) => fishData.find((item: any) => item._id === id))
        .filter(Boolean);

      const lsFav =
        fishData?.filter((ele: any) => ele.isFavorite === true) ?? [];

      // Efficiently merge unique fish data
      const mergedUniqueFish = [
        ...new Map(
          [...listFishMatched, ...lsFav]?.map(item => [item._id, item]) ?? [],
        ).values(),
      ];
      let mergedUniqueFishTemp = mergedUniqueFish.map((ele: any) => ({
        ...ele,
        khoi_luong:
          khoi_luongs.find((kl: any) => kl.id === ele._id)?.khoi_luong ?? 0,
      }));

      // Create a complete config object
      const configSetting = {
        autoTp: 1,
        listFish: mergedUniqueFishTemp,
        dateDiary: {
          from: thoi_gian_khai_thac_from,
          to: thoi_gian_khai_thac_to,
        },
        autoHours: {
          releaseTime: {
            from: thoi_gian_tha_from,
            to: thoi_gian_tha_to,
          },
          retrieveTime: {
            from: thoi_gian_thu_from,
            to: thoi_gian_thu_to,
          },
          waitTime: {from: 0, to: 24},
          processTime: {from: 0, to: 24},
        },
      };

      // Batch update all state at once to prevent cascade renders
      setFavoriteArr(mergedUniqueFishTemp.map((ele: any) => ele._id));

      // Update all related states in one render cycle
      setListFish(mergedUniqueFishTemp);
      setSelectedDates(configSetting.dateDiary);
      setHourReleaseTime(configSetting.autoHours.releaseTime);
      setHourRetrieveTime(configSetting.autoHours.retrieveTime);
      setWaitFrom(configSetting.autoHours.waitTime.from);
      setWaitTo(configSetting.autoHours.waitTime.to);
      setExpectedFrom(configSetting.autoHours.processTime.from);
      setExpectedTo(configSetting.autoHours.processTime.to);

      // Finally update the config
      setConfigQuickDiary(configSetting);
    } catch (err) {
      console.error('Error loading catch config:', err);
    }
  }, [fishData, setFavoriteArr, setConfigQuickDiary]);

  const {
    credential: {user},
  } = useStorageStore();

  const [modalVisible, setModalVisible] = useState(false);

  const [selectedDates, setSelectedDates] = useState({
    from: configQuickDiary?.dateDiary?.from ?? null,
    to: configQuickDiary?.dateDiary?.to ?? null,
  });

  const [selectedTimes, setSelectedTimes] = useState({
    from: {
      start: configQuickDiary?.autoDay?.timeDiary?.from?.start ?? null,
      end: configQuickDiary?.autoDay?.timeDiary?.from?.end ?? null,
    },
    to: {
      start: configQuickDiary?.autoDay?.timeDiary?.to?.start ?? null,
      end: configQuickDiary?.autoDay?.timeDiary?.to?.end ?? null,
    },
  });

  const [openReleaseTime, setOpenReleaseTime] = useState({
    from: false,
    to: false,
  });

  const [openRetrieveTime, setOpenRetrieveTime] = useState({
    from: false,
    to: false,
  });

  const [numDay, setNumday] = useState(
    configQuickDiary?.autoDay?.numDayOnDiary ?? 1,
  );
  const [listFish, setListFish] = useState<any[]>(
    configQuickDiary?.listFish ?? [],
  );

  const [hourReleaseTime, setHourReleaseTime] = useState({
    from: configQuickDiary?.autoHours?.releaseTime?.from ?? null,
    to: configQuickDiary?.autoHours?.releaseTime?.to ?? null,
  });

  const [hourRetrieveTime, setHourRetrieveTime] = useState({
    from: configQuickDiary?.autoHours?.retrieveTime?.from ?? null,
    to: configQuickDiary?.autoHours?.retrieveTime?.to ?? null,
  });

  const [openPickerTimeTpHour, setOpenPickerTimeTpHour] = useState<number>(0);
  const [expectedFrom, setExpectedFrom] = useState<any>(0);
  const [expectedTo, setExpectedTo] = useState<any>(24);
  const [waitFrom, setWaitFrom] = useState<any>(0);
  const [waitTo, setWaitTo] = useState<any>(24);

  const handleConfirm = (from: any, to: any) => {
    setSelectedDates({from, to});
    setModalVisible(false);
  };

  const onConfirmRelease = (datetime: any, type: any) => {
    setOpenReleaseTime({from: false, to: false});
    if (selectedTimes.from.start && type == 2) {
      if (moment(selectedTimes.from.start, 'HH:mm').isAfter(moment(datetime))) {
        Alert.alert(
          'Thông báo',
          'Thời gian thả mẻ lưới bắt đầu phải sau kết thúc',
        );
        return;
      }
    }

    setSelectedTimes({
      ...selectedTimes,
      from: {
        start:
          type == 1
            ? moment(datetime).format('HH:mm')
            : selectedTimes.from.start,
        end:
          type == 2 ? moment(datetime).format('HH:mm') : selectedTimes.from.end,
      },
    });
  };

  const onConfirmRetrieve = (datetime: any, type: any) => {
    setOpenRetrieveTime({from: false, to: false});
    if (numDay <= 1) {
      if (type == 1) {
        if (moment(selectedTimes.from.end, 'HH:mm').isAfter(moment(datetime))) {
          Alert.alert(
            'Thông báo',
            'TG bắt đầu thu PHẢI sau TG kết thúc thả(Vì số ngày khai thác là 1)',
          );
          return;
        }
      } else if (type == 2) {
        if (moment(selectedTimes.from.end, 'HH:mm').isAfter(moment(datetime))) {
          Alert.alert(
            'Thông báo',
            'TG kết thúc thu PHẢI sau TG kết thúc thả(Vì số ngày khai thác là 1)',
          );
          return;
        }
      }
    }

    if (selectedTimes.to.start && type == 2) {
      if (moment(selectedTimes.to.start, 'HH:mm').isAfter(moment(datetime))) {
        Alert.alert(
          'Thông báo',
          'Thời gian thu mẻ lưới bắt đầu phải sau kết thúc',
        );
        return;
      }
    }
    setSelectedTimes({
      ...selectedTimes,
      to: {
        start:
          type == 1 ? moment(datetime).format('HH:mm') : selectedTimes.to.start,
        end:
          type == 2 ? moment(datetime).format('HH:mm') : selectedTimes.to.end,
      },
    });
  };

  const openListFish = () => {
    navigate(SCREENS.SUGGEST_KIND as never, {
      type: 'quickCatchDiary',
      callback: callback,
    });
  };

  const callback = (data: any) => {
    let _exist = listFish.find((ele: any) => ele._id === data._id);
    if (_exist) {
      return;
    }
    let list = [...listFish, data];
    setListFish(list);
  };

  const onRemoveFish = (item: any) => {
    let list = listFish?.filter((ele: any) => ele._id !== item?._id) ??[];
    const param = {...item, isFavorite: false};
      // Update in store
    setFavorite(param);   
    setListFish(list);
    setConfigQuickListFish(list);
  };

  // const validateSubmit = () => {
  //   if (!hourReleaseTime.from || !hourReleaseTime.to) {
  //     Alert.alert('Thông báo', 'VUI LÒNG CHỌN THỜI GIAN THẢ LƯỚI');
  //     return false;
  //   }
  //   if (expectedFrom > expectedTo) {
  //     Alert.alert(
  //       'Thông báo',
  //       '[4] Thời gian kết thúc phải sau thời gian bắt đầu.',
  //     );
  //     return false;
  //   }
  //   if (waitFrom > waitTo) {
  //     Alert.alert(
  //       'Thông báo',
  //       '[5] Thời gian kết thúc phải sau thời gian bắt đầu.',
  //     );
  //     return false;
  //   }
  //   return true;
  // };

  const [loading, setLoading] = useState(false);

  const createDiaryLocal = useCallback(() => {
    setLoading(true);

    // Helper functions - moved outside main logic for clarity
    const parseTime = (timeStr: any) => {
      const [hours, minutes] = timeStr.split(':').map(Number);
      return {hours, minutes};
    };

    const randomInRange = (min: any, max: any, roundTo = 0.5) => {
      const value = Math.random() * (max - min) + min;
      return Math.round(value / roundTo) * roundTo;
    };

    const randomTimeInRange = (fromTime: any, toTime: any) => {
      const from = parseTime(fromTime);
      const to = parseTime(toTime);
      const totalMinutesFrom = from.hours * 60 + from.minutes;
      const totalMinutesTo = to.hours * 60 + to.minutes;
      const randomMinutes = randomInRange(totalMinutesFrom, totalMinutesTo, 5);
      return {
        hours: Math.floor(randomMinutes / 60),
        minutes: randomMinutes % 60,
      };
    };

    // Prepare parameters once
    const params = {
      id_tau_ca: user?.id_tau_ca?._id,
      id_nhom_loais: listFish.map(ele => ele?._id),
      khoi_luongs: listFish.map(ele => ({
        id: ele?._id,
        khoi_luong: ele?.khoi_luong,
        khoi_luong_thap: ele?.khoi_luong_thap,
        khoi_luong_cao: ele?.khoi_luong_cao,
      })),
      thoi_gian_khai_thac_from: selectedDates.from,
      thoi_gian_khai_thac_to: selectedDates.to,
      thoi_gian_tha_from: hourReleaseTime.from,
      thoi_gian_tha_to: hourReleaseTime.to,
      thoi_gian_thu_from: hourRetrieveTime.from,
      thoi_gian_thu_to: hourRetrieveTime.to,
      thoi_gian_me_luoi_from: 0,
      thoi_gian_me_luoi_to: 24,
      thoi_gian_nghi_from: 0,
      thoi_gian_nghi_to: 24,
    };

    // Validate parameters
    if (params.thoi_gian_me_luoi_from >= params.thoi_gian_me_luoi_to) {
      Alert.alert('Thông báo', 'Thời gian mẻ lưới không hợp lệ!');
      setLoading(false);
      return;
    }

    if (params.thoi_gian_nghi_from >= params.thoi_gian_nghi_to) {
      Alert.alert('Thông báo', 'Thời gian nghỉ không hợp lệ!');
      setLoading(false);
      return;
    }

    // Setup configuration
    const config = {
      dateRange: {
        from: new Date(params.thoi_gian_khai_thac_from),
        to: new Date(params.thoi_gian_khai_thac_to),
      },
      castTime: {
        from: params.thoi_gian_tha_from,
        to: params.thoi_gian_tha_to,
      },
      collectTime: {
        from: params.thoi_gian_thu_from,
        to: params.thoi_gian_thu_to,
      },
      fishingDuration: {
        from: params.thoi_gian_me_luoi_from,
        to: params.thoi_gian_me_luoi_to,
      },
      restDuration: {
        from: params.thoi_gian_nghi_from,
        to: params.thoi_gian_nghi_to,
      },
    };

    // Pre-calculate collection time boundaries
    const [collectFromHour, collectFromMinute] = config.collectTime.from
      .split(':')
      .map(Number);
    const [collectToHour, collectToMinute] = config.collectTime.to
      .split(':')
      .map(Number);
    const fromTimeInMinutes = collectFromHour * 60 + collectFromMinute;
    const toTimeInMinutes = collectToHour * 60 + collectToMinute;

    // Create fish template array only once
    // const fishTemplate =
    //   listFish?.map((ele: any) => ({...ele, khoi_luong_cao: 0 ,khoi_luong_thap: 1,
    //     })) || [];
    // Generate batches
    const batches = [];
    let currentDate = new Date(config.dateRange.from);
    const endDate = new Date(config.dateRange.to);

    // Process all dates in one loop
    while (currentDate <= endDate) {
      // Optimize by using a date copy to avoid modifying the loop variable
      const processDate = new Date(currentDate);

      // Generate random cast time
      const randomCast = randomTimeInRange(
        config.castTime.from,
        config.castTime.to,
      );
      const castTime = new Date(processDate);
      castTime.setHours(randomCast.hours, randomCast.minutes);

      // Generate random collect time
      const randomCollect = randomTimeInRange(
        config.collectTime.from,
        config.collectTime.to,
      );
      const collectTime = new Date(processDate);
      const castTimeMinutes = randomCast.hours * 60 + randomCast.minutes;
      const collectTimeMinutes =
        randomCollect.hours * 60 + randomCollect.minutes;

      // Check if collection time should be next day
      if (collectTimeMinutes <= castTimeMinutes) {
        collectTime.setDate(collectTime.getDate() + 1);
      }

      collectTime.setHours(randomCollect.hours, randomCollect.minutes);

      // Calculate fishing hours
      const fishingHours =
        (collectTime.getTime() - castTime.getTime()) / (1000 * 60 * 60);
      const restHours = randomInRange(
        config.restDuration.from,
        config.restDuration.to,
      );

      // Validate batch
      if (castTime <= endDate) {
        const collectHour = collectTime.getHours();
        const collectMinute = collectTime.getMinutes();
        const collectTimeInMinutes = collectHour * 60 + collectMinute;

        const isCollectTimeValid =
          collectTimeInMinutes >= fromTimeInMinutes &&
          collectTimeInMinutes <= toTimeInMinutes;
        const isFishingDurationValid =
          fishingHours >= config.fishingDuration.from &&
          fishingHours <= config.fishingDuration.to;

        if (isCollectTimeValid && isFishingDurationValid) {
          batches.push({
            thoi_gian_tha: castTime.toISOString(),
            thoi_gian_thu: collectTime.toISOString(),
            thoi_gian_me_luoi: Number(fishingHours.toFixed(2)),
            thoi_gian_nghi: restHours,
          });
        }
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
      currentDate.setHours(0, 0, 0, 0);
    }

    // Create diary entries efficiently
    // const distributeAllFishToBatches = (batches: any[], listFish: any[]) => {
    //   const batchCount = batches.length;
    //   if (batchCount === 0) {
    //     return [];
    //   }

    //   return batches
    //     .map((batch, index) => ({
    //       ...batch,
    //       san_luong: [],
    //     }))
    //     .map((batch, index, newBatches) => {
    //       const batchIndex = index;

    //       const san_luong = listFish.map(fish => {
    //         const totalWeight = Math.round(fish.khoi_luong || 0);

    //         // Nếu chưa có phân phối, tạo mới
    //         if (!fish._distributedWeightsInt) {
    //           // Tạo mảng số ngẫu nhiên
    //           const randoms = Array.from({length: batchCount}, () =>
    //             Math.random(),
    //           );
    //           const sum = randoms.reduce((a, b) => a + b, 0);

    //           // Phân phối tỉ lệ, làm tròn xuống
    //           let raw = randoms.map(r => Math.floor((r / sum) * totalWeight));

    //           // Đảm bảo mỗi phần tử >= 1 nếu tổng > 0
    //           let remaining = totalWeight - raw.reduce((a, b) => a + b, 0);

    //           // Phân bổ phần dư còn lại (do làm tròn)
    //           while (remaining > 0) {
    //             for (let i = 0; i < raw.length && remaining > 0; i++) {
    //               raw[i]++;
    //               remaining--;
    //             }
    //           }

    //           fish._distributedWeightsInt = raw;
    //         }

    //         return {
    //           ...fish,
    //           id_nhom_loai: fish.id_nhom_loai,
    //           khoi_luong: fish._distributedWeightsInt[batchIndex], // số nguyên
    //         };
    //       });

    //       return {
    //         ...batch,
    //         san_luong,
    //       };
    //     });
    // };
    const newBatches = distributeAllFishToBatches(batches, listFish);
    const listDiary = newBatches.map(batch => {
      const id_item = uuid();
      return {
        auto: true,
        trang_thai: 0,
        id: id_item,
        item_id: id_item,
        thoi_gian_tha: batch.thoi_gian_tha,
        thoi_gian_thu: batch.thoi_gian_thu,
        is_day_off: false,
        // san_luong: [...fishTemplate], // Use the template we created once
        san_luong: batch.san_luong, // Use the template we created once
      };
    });

    // Sort and save in one operation
    if (listDiary.length > 0) {
      setDiaryLocal(
        listDiary.sort(
          (a: any, b: any) =>
            new Date(a?.thoi_gian_tha).getTime() -
            new Date(b?.thoi_gian_tha).getTime(),
        ),
      );
    }

    // Save config settings
    const configSetting = {
      autoTp: 1,
      listFish: listFish,
      dateDiary: {
        from: selectedDates.from,
        to: selectedDates.to,
      },
      autoHours: {
        releaseTime: hourReleaseTime,
        retrieveTime: hourRetrieveTime,
        waitTime: {
          from: 0,
          to: 24,
        },
        processTime: {
          from: 0,
          to: 24,
        },
      },
      autoDay: {
        numDayOnDiary: numDay,
        timeDiary: {
          from: selectedTimes.from,
          to: selectedTimes.to,
        },
      },
    };

    setConfigQuickDiary(configSetting);
    setLoading(false);
    goBack();
  }, [
    user,
    listFish,
    selectedDates,
    hourReleaseTime,
    hourRetrieveTime,
    numDay,
    selectedTimes,
    setDiaryLocal,
    setConfigQuickDiary,
    goBack,
  ]);

  const onSaveAndCreate = async () => {
    if (
      !selectedDates.from ||
      !selectedDates.to ||
      !hourReleaseTime.from ||
      !hourReleaseTime.to ||
      !hourRetrieveTime.from ||
      !hourRetrieveTime.to
    ) {
      Toast.show({
        type: ALERT_TYPE.WARNING,
        title: 'Thông báo',
        textBody: 'Vui lòng nhập đủ thông tin',
      });
      return;
    }

    if (!isConnected || !isInternetReachable) {
      createDiaryLocal();
      return;
    }
    setLoading(true); 
    const param = {
      id_tau_ca: user?.id_tau_ca?._id,
      id_nhom_loais: listFish?.map(ele => ele?._id) ?? [],
      khoi_luongs: listFish?.map(ele => ({
        id: ele?._id,
        khoi_luong: ele?.khoi_luong,
      })) ?? [],
      thoi_gian_khai_thac_from: selectedDates.from,
      thoi_gian_khai_thac_to: selectedDates.to,
      thoi_gian_tha_from: hourReleaseTime.from,
      thoi_gian_tha_to: hourReleaseTime.to,
      thoi_gian_thu_from: hourRetrieveTime.from,
      thoi_gian_thu_to: hourRetrieveTime.to,
      thoi_gian_me_luoi_from: 0,
      thoi_gian_me_luoi_to: 24,
      thoi_gian_nghi_from: 0,
      thoi_gian_nghi_to: 24,
    };
    const config: any = {
      url: '/token/nk_khaithac/generate',
      body: param,
    };
    try {
      const result: any = await apiServices.Post(config);
      const resultRefresh: any = await apiServices.Post({
        url: '/token/nk_khaithac',
        body: {
          filter: {
            id_tau_ca: param.id_tau_ca,
            trang_thai: [0, 1, 2, 3, 4],
          },
          sort: {
            createdAt: -1,
          },
          limit: 500,
        },
      });
      if (resultRefresh?.status == 'success') {
        const ls = {
          ...resultRefresh,
          data: resultRefresh?.data?.map((ele: any) => ({
            ...ele,
            is_day_off: ele?.is_day_off ?? false,
            auto: true // bổ sung nhận dạng tạo nhanh
          })),
        };
        clearDiaryDelete();
        fetchDiary(ls, true, user);
      }
    } catch (error: any) {
      Toast.show({
        type: ALERT_TYPE.WARNING,
        title: 'Thông báo',
        textBody: error?.message,
      });
    }

    const configSetting = {
      autoTp: 1,
      listFish: listFish,
      dateDiary: {
        from: selectedDates.from,
        to: selectedDates.to,
      },
      autoHours: {
        releaseTime: hourReleaseTime,
        retrieveTime: hourRetrieveTime,
        waitTime: {
          from: 0,
          to: 24,
        },
        processTime: {
          from: 0,
          to: 24,
        },
      },
      autoDay: {
        numDayOnDiary: numDay,
        timeDiary: {
          from: selectedTimes.from,
          to: selectedTimes.to,
        },
      },
    };
    setConfigQuickDiary(configSetting);
    setLoading(false);
    goBack();
  };
  const onCancel = () => {
    Alert.alert('Thông báo', 'Bạn có muốn bỏ qua cài đặt tạo nhanh?', [
      {
        text: 'Huỷ',
        onPress: () => {},
      },
      {
        text: 'Đồng ý',
        onPress: () => {
          goBack();
        },
      },
    ]);
  };

  const validateChooseHours = (datetime: any) => {
    if (openPickerTimeTpHour == 1) {
    } else if (openPickerTimeTpHour == 2) {
    }
    return true;
  };

  const onConfirmTpHour = (datetime: any) => {
    if (!validateChooseHours(datetime)) {
      return;
    }

    if (openPickerTimeTpHour == 1) {
      let param = hourReleaseTime;
      if (
        hourReleaseTime.from &&
        moment(hourReleaseTime.from, 'HH:mm').isBefore(moment(datetime))
      ) {
        const dur = moment.duration({minutes: 1});
        if (
          moment(datetime).hours() == 23 &&
          moment(datetime).minutes() == 59
        ) {
        } else {
          const temp = moment(datetime).clone().add(dur);
          setOpenPickerTimeTpHour(0);
          param = {
            ...hourReleaseTime,
            to: temp.format('HH:mm'),
          };
        }
      }
      setHourReleaseTime({
        ...hourReleaseTime,
        ...param,
        from: moment(datetime).format('HH:mm'),
      });
    } else if (openPickerTimeTpHour == 2) {
      let param = hourReleaseTime;
      if (
        hourReleaseTime.from &&
        moment(hourReleaseTime.from, 'HH:mm').isAfter(moment(datetime))
      ) {
        setOpenPickerTimeTpHour(0);
        const dur = moment.duration({minutes: -1});
        if (moment(datetime).hours() == 0 && moment(datetime).minutes() == 0) {
        } else {
          const temp = moment(datetime).clone().add(dur);
          setOpenPickerTimeTpHour(0);
          param = {
            ...hourReleaseTime,
            from: temp.format('HH:mm'),
          };
        }
      }
      setHourReleaseTime({
        ...hourReleaseTime,
        ...param,
        to: moment(datetime).format('HH:mm'),
      });
    } else if (openPickerTimeTpHour == 7) {
      let param = hourRetrieveTime;
      if (
        hourRetrieveTime.from &&
        moment(hourRetrieveTime.from, 'HH:mm').isBefore(moment(datetime))
      ) {
        const dur = moment.duration({minutes: 1});
        if (
          moment(datetime).hours() == 23 &&
          moment(datetime).minutes() == 59
        ) {
        } else {
          const temp = moment(datetime).clone().add(dur);
          setOpenPickerTimeTpHour(0);
          param = {
            ...hourRetrieveTime,
            to: temp.format('HH:mm'),
          };
        }
      }
      setHourRetrieveTime({
        ...hourRetrieveTime,
        ...param,
        from: moment(datetime).format('HH:mm'),
      });
    } else if (openPickerTimeTpHour == 8) {
      let param = hourRetrieveTime;
      if (
        hourRetrieveTime.from &&
        moment(hourRetrieveTime.from, 'HH:mm').isAfter(moment(datetime))
      ) {
        const dur = moment.duration({minutes: -1});
        if (moment(datetime).hours() == 0 && moment(datetime).minutes() == 0) {
        } else {
          const temp = moment(datetime).clone().add(dur);
          setOpenPickerTimeTpHour(0);
          param = {
            ...hourRetrieveTime,
            from: temp.format('HH:mm'),
          };
        }
      }
      setHourRetrieveTime({
        ...hourRetrieveTime,
        ...param,
        to: moment(datetime).format('HH:mm'),
      });
    }
    setOpenPickerTimeTpHour(0);
  };

  const RenderByHour = React.memo(() => {
    return (
      <View>
        {/* Thời gian thả */}
        <View style={[styles.view_header]}>
          <AppText
            customStyle={styles.txt_header}
            text={'KHOẢNG THỜI GIAN THẢ LƯỚI'}
          />
        </View>
        <View
          style={[
            styles.view_content,
            {height: 'auto', flexDirection: 'column', marginBottom: 10},
          ]}>
          <SelectDate
            clearable
            isDate={false}
            required={true}
            mode="time"
            onSubmit={(values: any) => {
              setHourReleaseTime({
                ...hourReleaseTime,
                from: moment(values).format('HH:mm'),
              });
            }}
            value={
              hourReleaseTime.from && moment(hourReleaseTime.from, 'HH:mm')
            }
            width="100%"
            marginHorizontal={0}
            placeholderText={'Từ'}
          />
          <SelectDate
            clearable
            isDate={false}
            required={true}
            mode="time"
            onSubmit={(values: any) => {
              setHourReleaseTime({
                ...hourReleaseTime,
                to: moment(values).format('HH:mm'),
              });
            }}
            value={hourReleaseTime.to && moment(hourReleaseTime.to, 'HH:mm')}
            width="100%"
            marginHorizontal={0}
            placeholderText={'Đến'}
          />
        </View>
        {/* Thời gian thu */}
        <View style={[styles.view_header]}>
          <AppText
            customStyle={styles.txt_header}
            text={'KHOẢNG THỜI GIAN THU LƯỚI'}
          />
        </View>
        <View
          style={[
            styles.view_content,
            {height: 'auto', flexDirection: 'column', marginBottom: 10},
          ]}>
          <SelectDate
            clearable
            isDate={false}
            required={true}
            mode="time"
            onSubmit={(values: any) => {
              setHourRetrieveTime({
                ...hourRetrieveTime,
                from: moment(values).format('HH:mm'),
              });
            }}
            value={
              hourRetrieveTime.from && moment(hourRetrieveTime.from, 'HH:mm')
            }
            width="100%"
            marginHorizontal={0}
            placeholderText={'Từ'}
          />
          <SelectDate
            isDate={false}
            required={true}
            mode="time"
            onSubmit={(values: any) => {
              setHourRetrieveTime({
                ...hourRetrieveTime,
                to: moment(values).format('HH:mm'),
              });
            }}
            value={hourRetrieveTime.to && moment(hourRetrieveTime.to, 'HH:mm')}
            width="100%"
            marginHorizontal={0}
            placeholderText={'Đến'}
          />
        </View>
      </View>
    );
  });
  // Memoize modal content
  const modalContent = useMemo(() => {
    if (!openModal) {
      return null;
    }

    return (
      <Modal
        style={{
          backgroundColor: 'transparent',
          width: '100%',
          marginHorizontal: 0,
          height: screenHeight,
        }}
        isVisible={openModal}>
        <ModalContent
          style={{
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
            height: screenHeight,
            padding: 0,
            flex: 1,
            width: screenWidth,
            backgroundColor: COLORS.WHITE,
          }}>
          <KeyboardAvoidingView
            enabled={openModal}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{flex: 1}}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <CustomTextField
                styleCustom={{flex: 1}}
                required={true}
                editable={false}
                placeholderText={'LOÀI'}
                multiline={true}
                value={
                  (fishSelected?.ten_dia_phuong ?? fishSelected.ten) +
                  ' (' +
                  fishSelected?.ma_code +
                  ')'
                }
              />

              <AppIcon
                color={COLORS.LIGHT_GREY}
                name="fish"
                size={scaleBaseOnScreenWidth(20)}
                type={IconType.ionicons}
              />
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <CustomTextField
                autoFocus={true}
                showSoftInputOnFocus={true}
                keyboardType="numeric"
                containerStyle={{fontFamily: FONT_TYPE.LIGHT, flex: 1}}
                required={true}
                editable={true}
                placeholderText={'SẢN LƯỢNG(KG)'}
                value={khoiLuong.toString()}
                onChangeText={(text: any) => {
                  if (isNaN(Number(text))) {
                    setKhoiLuong(0);
                    return;
                  }
                  if (!isNaN(Number(text)) && Number(text) > 200000) {
                    setKhoiLuong(200000);
                  } else {
                    setKhoiLuong(Number(text));
                  }
                }}
              />
              <AppIcon
                color={COLORS.LIGHT_GREY}
                name="balance-scale"
                size={scaleBaseOnScreenWidth(20)}
                type={IconType.fontawesome}
              />
            </View>
            <SizeBox h={METRICS.normal_large} />
            <AppButton
              size={[1, 2]}
              text="HỦY"
              textRight="THÊM"
              type="secondary"
              onPressLeft={onCloseModal}
              onPressRight={onPressAdd}
            />
            <KeyboardSpacer
              topSpacing={
                Platform.OS === 'ios' ? 0 : scaleBaseOnScreenHeight(-200)
              }
            />
          </KeyboardAvoidingView>
        </ModalContent>
      </Modal>
    );
    
  }, [
    openModal,
    fishSelected,
    khoiLuong,
    // isFav,
    onCloseModal,
    onPressAdd,
    // setFavFish,
  ]);


  return (
    <BaseScreen>
      <Header
        backgroundColor={COLORS.MAIN_COLOR}
        title={'THIẾT LẬP NHANH MẺ LƯỚI'}
      />
      <Loading isLoading={loading} />

      <DelayRenderComponent
        style={[
          globalStyles.flex1,
          {padding: setValue(5), backgroundColor: COLORS.WHITE},
        ]}>
        <ScrollView>
          <View style={[styles.view_header]}>
            <AppText
              customStyle={styles.txt_header}
              text={'KHOẢNG THỜI GIAN KHAI THÁC'}
            />
          </View>
          <View
            style={[
              styles.view_content,
              {height: 'auto', flexDirection: 'column', marginBottom: 10},
            ]}>
            <View
              style={[
                globalStyles.flex1,
                {
                  width: '100%',
                  height: 60,
                  flexDirection: 'row',
                  alignItems: 'center',
                  borderBottomWidth: selectedDates.from ? 0 : 1,
                  borderColor: selectedDates.from ? COLORS.SILVER : COLORS.RED,
                  paddingRight: 15,
                },
              ]}>
              <TouchableOpacity
                style={{flex: 1}}
                activeOpacity={0.5}
                onPress={() => setModalVisible(true)}>
                <RenderTextDate
                  datetime={selectedDates.from}
                  placeholderText={'Từ'}
                />
              </TouchableOpacity>
              <AppIcon
                color={'#7a8691'}
                name={'calendar'}
                size={20}
                type={IconType.antDesign}
              />
            </View>
            <View
              style={[
                globalStyles.flex1,
                {
                  width: '100%',
                  height: 60,
                  flexDirection: 'row',
                  alignItems: 'center',
                  borderBottomWidth: selectedDates.to ? 0 : 1,
                  borderColor: selectedDates.to ? COLORS.SILVER : COLORS.RED,
                  marginBottom: 10,
                  paddingRight: 15,
                },
              ]}>
              <TouchableOpacity
                style={{flex: 1}}
                activeOpacity={0.5}
                onPress={() => setModalVisible(true)}>
                <RenderTextDate
                  datetime={selectedDates.to}
                  placeholderText={'Đến'}
                />
              </TouchableOpacity>
              <AppIcon
                color={'#7a8691'}
                name={'calendar'}
                size={20}
                type={IconType.antDesign}
              />
            </View>
          </View>
          <RenderByHour />
          <View style={styles.view_header}>
            <AppText customStyle={styles.txt_header} text={'LOÀI KHAI THÁC'} />
            <TouchableOpacity
              style={{
                borderWidth: 2,
                borderColor: COLORS.WHITE,
                borderRadius: 20,
                padding: 5,
                paddingHorizontal: 20,
              }}
              onPress={openListFish}>
              <AppText
                customStyle={{...styles.txt_header, fontWeight: 'bold'}}
                text={'THÊM LOÀI'}
              />
            </TouchableOpacity>

            {/* <AppIcon
              color={COLORS.BLUE_6C}
              name={'add-circle-outline'}
              size={40}
              type={'ionicons'}
              onPress={openListFish}
            /> */}
          </View>
          <View
            style={{
              borderWidth: 1,
              borderColor: COLORS.SILVER,
              paddingBottom: 150,
            }}>
            <View style={styles.bodyRow}>
              {listFish.map((fish, idx) => (
                <View key={idx ?? fish.fishId ?? fish.id} style={styles.box}>
                  <SelectFishRow
                    border={false}
                    end={idx === listFish?.length - 1}
                    remove={true}
                    style={styles.row}
                    typequick={true}
                    onRemove={() => onRemoveFish(fish)}
                    onPress={() => onChangeWeightFish(fish)}
                    {...fish}
                  />
                </View>
              ))}
            </View>
          </View>
          <CalendarModal
            visible={modalVisible}
            onClose={() => setModalVisible(false)}
            onConfirm={handleConfirm}
            minDate={new Date(lastDiary?.thoi_gian_xuat_cang).toISOString().split('T')[0] ?? null}
            maxDate={new Date().toISOString().split('T')[0]} // ✅ chuyển về string
          />
          {openReleaseTime.from && (
            <DateTimePickerModal
              isVisible={true}
              mode="time"
              onConfirm={from => onConfirmRelease(from, 1)}
              onCancel={() =>
                setOpenReleaseTime({...openReleaseTime, from: false})
              }
            />
          )}

          {openReleaseTime.to && (
            <DateTimePickerModal
              isVisible={true}
              mode="time"
              onConfirm={from => onConfirmRelease(from, 2)}
              onCancel={() =>
                setOpenReleaseTime({...openReleaseTime, to: false})
              }
            />
          )}
          {openRetrieveTime.from && (
            <DateTimePickerModal
              isVisible={true}
              mode="time"
              onConfirm={to => onConfirmRetrieve(to, 1)}
              onCancel={() =>
                setOpenRetrieveTime({...openRetrieveTime, from: false})
              }
            />
          )}
          {openRetrieveTime.to && (
            <DateTimePickerModal
              isVisible={true}
              mode="time"
              onConfirm={to => onConfirmRetrieve(to, 2)}
              onCancel={() =>
                setOpenRetrieveTime({...openRetrieveTime, to: false})
              }
            />
          )}
          {/* Loại theo giờ..... */}
          {openPickerTimeTpHour != 0 && (
            <DateTimePickerModal
              isVisible={true}
              mode={
                [7, 8, '7', '8'].includes(openPickerTimeTpHour)
                  ? 'time'
                  : 'time'
              }
              onConfirm={time => onConfirmTpHour(time)}
              onCancel={() => setOpenPickerTimeTpHour(0)}
            />
          )}

          {/* {openRetrieveTime.to && (
            <DateTimePickerModal
              isVisible={true}
              mode="time"
              onConfirm={(to)=>onConfirmRetrieve(to, 2)}
              onCancel={() => setOpenRetrieveTime({...openRetrieveTime, to: false})}
            />
          )} */}
        </ScrollView>
        <AppButton
          type="secondary"
          text="HUỶ"
          onPress={() => {}}
          textRight="LƯU & TẠO"
          onPressLeft={onCancel}
          onPressRight={onSaveAndCreate}
          customStyle={{margin: setValue(16)}}
        />
      </DelayRenderComponent>
        {modalContent}

    </BaseScreen>
  );
};

const RenderTextDate = (itemProps: any) => {
  const {placeholderText, datetime, type} = itemProps;
  return (
    <View style={{margin: 2}}>
      <AppText
        customStyle={{fontSize: METRICS.text_12, color: COLORS.LIGHT_GREY}}
        text={placeholderText}
      />
      {datetime && (
        <View style={styles.txtDatetime}>
          {type == 'time' ? (
            <AppText customStyle={styles.txtTime} text={datetime} />
          ) : (
            <AppText
              customStyle={styles.txtTime}
              text={datetime ? `${moment(datetime).format('DD/MM/YYYY')}` : ''}
            />
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  txtDatetime: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    borderBottomWidth: 1,
    borderColor: COLORS.SILVER,
    marginBottom: 2,
  },
  txtDate: {
    fontSize: METRICS.text_20,
    padding: 0,
    color: COLORS.BLACK,
  },
  txtTime: {
    fontSize: METRICS.text_14,
    paddingBottom: 2,
    color: COLORS.BLACK,
  },
  dropdown: {
    margin: 16,
    width: 50,
    height: 30,
    borderWidth: 1,
    borderColor: COLORS.SILVER,
    borderRadius: 8,
    paddingRight: 14,
  },
  view_header: {
    backgroundColor: 'rgba(154,154,154,0.5)',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    padding: setValue(5),
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  view_content: {
    height: 100,
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    padding: setValue(5),
    borderWidth: 1,
    borderColor: COLORS.SILVER,
    zIndex: 999,
  },
  view_content_50: {
    height: 50,
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    padding: setValue(5),
    borderWidth: 1,
    borderColor: COLORS.SILVER,
  },
  txt_header: {
    fontSize: METRICS.text_14,
    color: COLORS.BLUE_SECONDARY,
    fontWeight: 'normal',
  },
  txt_content: {
    fontSize: METRICS.text_14,
    color: COLORS.BLACK,
    fontWeight: 'normal',
  },
  bodyRow: {
    backgroundColor: COLORS.WHITE,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  box: {
    padding: setValue(5),
    width: '100%',
    borderBottomWidth: 1,
    borderColor: COLORS.SILVER,
  },
  row: {
    backgroundColor: COLORS.WHITE,
  },

  dropdownNganhnghe: {
    margin: 16,
    height: 50,
    borderBottomWidth: 1,
    borderColor: COLORS.SILVER,
  },
  imageStyle: {
    width: 24,
    height: 24,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
    marginLeft: 8,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
export default QuickCatchDiary;
