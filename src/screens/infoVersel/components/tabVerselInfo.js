import {COLORS} from 'assets';
import {AppText, CustomTextField, SizeBox} from 'elements';
import React from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import {FONT_TYPE, METRICS} from 'utils';
import {scaleBaseOnScreenHeight, scaleBaseOnScreenWidth} from 'utils/layout';

const TabVerselInfo = ({data}) => {
  const id_tau_ca = data?.id_tau_ca;
  if (id_tau_ca === undefined) {
    return <View />;
  }
  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={{
        borderRadius: 16,
        height: 'auto',
        // overflow: 'hidden',
        borderWidth: 0.5,
        borderColor: COLORS.SOFT_RED,
      }}>
      <View
        style={{
          height: 40,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          backgroundColor: COLORS.SOFT_RED,
          paddingHorizontal: METRICS.nomal,
          justifyContent: 'center',
        }}>
        <AppText font={FONT_TYPE.BOLD} text="HỒ SƠ" color={COLORS.WHITE} />
      </View>
      <CustomTextField
        placeholderText={'SỐ ĐĂNG KÝ'}
        containerStyle={styles.textField}
        editable={false}
        value={id_tau_ca?.so_dang_ky}
      />
      <CustomTextField
        placeholderText={'TÊN TÀU'}
        editable={false}
        value={id_tau_ca?.ten_tau}
      />
      <CustomTextField
        placeholderText={'HÔ HIỆU'}
        editable={false}
        value={id_tau_ca?.ho_hieu || ''}
      />
      {/* <CustomTextField
        placeholderText={'CỠ TÀU'}
        editable={false}
        value={id_tau_ca?.ten_chu_tau || ''}
      /> */}
      <CustomTextField
        placeholderText={'SỐ IMO'}
        editable={false}
        value={id_tau_ca?.imo || ''}
      />
      <CustomTextField
        placeholderText={'NƠI ĐĂNG KÝ'}
        editable={false}
        value={id_tau_ca?.noi_dang_ky}
      />
      <CustomTextField
        placeholderText={'TỈNH/ THÀNH PHỐ'}
        editable={false}
        value={id_tau_ca?.id_tinh_thanh?.ten}
      />
      <CustomTextField
        placeholderText={'QUẬN/HUYỆN'}
        editable={false}
        value={id_tau_ca?.id_quan_huyen?.ten}
      />
      <CustomTextField
        placeholderText={'PHƯỜNG/XÃ'}
        editable={false}
        value={id_tau_ca?.id_phuong_xa?.ten}
      />
      <CustomTextField
        placeholderText={'NHÓM TÀU'}
        editable={false}
        value={id_tau_ca?.id_nhom_tau?.ten}
      />
      <CustomTextField
        placeholderText={'CÔNG DỤNG TÀU'}
        editable={false}
        value={id_tau_ca?.id_cong_dung_tau?.ten}
      />
      <CustomTextField
        placeholderText={'NƠI ĐÓNG TÀU'}
        editable={false}
        value={id_tau_ca?.noi_dong_tau}
      />
      <CustomTextField
        placeholderText={'CẢNG CHÍNH'}
        editable={false}
        value={id_tau_ca?.id_cang_dang_ky?.ten}
      />
      <CustomTextField
        placeholderText={'CẢNG PHỤ'}
        editable={false}
        value={id_tau_ca?.id_cang_phu?.ten}
      />
      <CustomTextField
        placeholderText={'NGHỀ CHÍNH'}
        editable={false}
        value={id_tau_ca?.id_nghe_chinh?.ten}
      />
      <CustomTextField
        placeholderText={'NGHỀ PHỤ'}
        editable={false}
        value={id_tau_ca?.id_nghe_phu?.ten}
      />
      {/* THÔNG TIN CHỦ TÀU */}
      <CustomTextField
        placeholderText={'TÊN CHỦ TÀU'}
        editable={false}
        value={id_tau_ca?.ten_chu_tau}
      />
      <CustomTextField
        placeholderText={'ĐỊA CHỈ CHỦ TÀU'}
        editable={false}
        value={id_tau_ca?.dia_chi_chu_tau}
      />
      <CustomTextField
        placeholderText={'CCCD'}
        editable={false}
        value={id_tau_ca?.so_cmnd}
      />
      <CustomTextField
        placeholderText={'ĐIỆN THOẠI'}
        editable={false}
        value={id_tau_ca?.dien_thoai}
      />
      <CustomTextField
        placeholderText={'FAX'}
        editable={false}
        value={id_tau_ca?.fax}
      />
      <SizeBox h={METRICS.normal_large} />
    </ScrollView>
  );
};

export default TabVerselInfo;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: scaleBaseOnScreenHeight(15),
    paddingHorizontal: 16,
    marginBottom: scaleBaseOnScreenHeight(10),
  },
  textField: {
    marginHorizontal: scaleBaseOnScreenWidth(5),
    marginTop: scaleBaseOnScreenHeight(5),
    fontWeight: 'bold',
  },
});
