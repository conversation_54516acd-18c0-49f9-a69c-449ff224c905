import {CustomTextField} from 'elements';
import React from 'react';
import {Clipboard, StyleSheet} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {scaleBaseOnScreenHeight, scaleBaseOnScreenWidth} from 'utils/layout';

const TabAccountInfo = ({data}) => {
  const {address, email, fullname, device_id, id_tau_ca} = data?.data || {};
  return (
    <ScrollView style={styles.container}>
      <CustomTextField
        containerStyle={styles.textField}
        editable={false}
        placeholderText={'HỌ VÀ TÊN'}
        value={fullname}
      />
      <CustomTextField
        containerStyle={styles.textField}
        editable={false}
        placeholderText={'CCCD'}
        value={id_tau_ca?.so_cmnd}
      />
      <CustomTextField
        containerStyle={styles.textField}
        editable={false}
        placeholderText={'EMAIL'}
        value={email}
      />
      <CustomTextField
        containerStyle={styles.textField}
        editable={false}
        placeholderText={'ĐIỆN THOẠI'}
        value={id_tau_ca?.dien_thoai}
      />
      <CustomTextField
        containerStyle={styles.textField}
        editable={false}
        multiline
        placeholderText={'ĐỊA CHỈ'}
        value={address}
      />
      <TouchableOpacity
        onPress={() => {
          Clipboard.setString(device_id);
        }}>
        <CustomTextField
          containerStyle={styles.textField}
          editable={false}
          placeholderText={'NOTIFICATION TOKEN'}
          value={device_id}
        />
      </TouchableOpacity>
    </ScrollView>
  );
};

export default TabAccountInfo;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: scaleBaseOnScreenHeight(15),
    padding: 16,
  },
  textField: {
    marginHorizontal: scaleBaseOnScreenWidth(5),
    marginTop: scaleBaseOnScreenHeight(5),
  },
});
