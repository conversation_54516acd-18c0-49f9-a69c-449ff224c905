import TabAccountInfo from './components/tabAccountInfo';
import TabVerselInfo from './components/tabVerselInfo';
import {useNetInfoInstance} from '@react-native-community/netinfo';
import {COLORS} from 'assets';
import Tabs from 'components/TabView';
import {AppButton} from 'elements';
import React from 'react';
import {Alert, View} from 'react-native';

import RNRestart from 'react-native-restart';
import {useMutation, useQuery} from 'react-query';
import {apiServices} from 'services';
import useAppStore from 'stores/appStore';
import useDiaryStore from 'stores/diaryStore';
import useStorageStore from 'stores/useStorageStore';
import {setValue} from 'utils/layout';

const routes = [
  {
    key: 'first',
    title: 'THÔNG TIN TÀU',
    iconName: 'clockcircle',
    iconType: 'AntDesign',
  },
  {
    key: 'second',
    title: 'THÔNG TIN TÀI KHOẢN',
    iconName: 'checksquare',
    iconType: 'AntDesign',
  },
];

const InfoVessel = () => {
  const {showLoading} = useAppStore();
  const {
    onClearnStorage,
    credential: {user},
  } = useStorageStore();
  const {onClearAll: _onClearAll} = useDiaryStore();
  const {
    netInfo: {isConnected},
  } = useNetInfoInstance();

  const {data: userData, isLoading: loadingUser} = useQuery(
    '/getuserfromtoken',
    () =>
      apiServices.Post({
        url: '/token/user/getuserfromtoken',
        body: {},
      }),
  );

  const {mutate} = useMutation(
    async (bodyData: any) => {
      const res = await apiServices.Post({
        url: '/token/signout',
        body: bodyData,
      });
      return res;
    },
    {
      onMutate: () => {
        showLoading(true);
      },
      onSuccess: () => {
        onClearnStorage();
        // onClearAll();
        showLoading(false);
        RNRestart.Restart();
      },
      onError: _err => {
        onClearnStorage();
        // onClearAll();
        showLoading(false);
        RNRestart.Restart();
      },
    },
  );

  const onPressLogout = () => {
    if (!isConnected) {
      return;
    }
    // Dialog.show({
    //   type: ALERT_TYPE.WARNING,
    //   title: 'Thông báo',
    //   textBody: 'Bạn có chắc chắn muốn đăng xuất?',
    //   button: 'Đồng ý',
    //   onPressButton() {
    //     mutate({id_tau_ca: user?.id_tau_ca?._id}); // Remove the unnecessary object parameter
    //   },
    // });
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn đăng xuất?', [
      {
        text: 'Huỷ',
        onPress: () => {},
      },
      {
        text: 'Đồng ý',
        onPress: () => {
          mutate({id_tau_ca: user?.id_tau_ca?._id}); // Remove the unnecessary object parameter
        },
      },
    ]);
  };

  const renderScene = ({route, jumpTo: _jumpTo}) => {
    switch (route.key) {
      case 'first':
        return <TabVerselInfo loadingUser={loadingUser} data={user} />;
      case 'second':
        return <TabAccountInfo loadingUser={loadingUser} data={userData} />;
    }
  };
  const _onPressChangePass = () => {
    // Dialog.show({
    //   type: ALERT_TYPE.WARNING,
    //   title: 'Thông báo',
    //   textBody: 'Vui lòng liên hệ IT để thực hiện yêu cầu.',
    //   button: 'Đồng ý',
    //   onPressButton() {
    //     Dialog.hide();
    //   },
    // });
    Alert.alert('Thông báo', 'Vui lòng liên hệ IT để thực hiện yêu cầu.', [
      {
        text: 'Đồng ý',
        onPress: () => {
          mutate({id_tau_ca: user?.id_tau_ca?._id}); // Remove the unnecessary object parameter
        },
      },
    ]);
  };
  return (
    <View style={{flex: 1, backgroundColor: COLORS.WHITE}}>
      <Tabs
        headerTitle="THÔNG TIN"
        renderScene={renderScene}
        routes={routes}
        iconRight={null}
      />
      <AppButton
        text="ĐĂNG XUẤT"
        onPress={onPressLogout}
        // text="ĐỔI MẬT KHẨU"
        // textRight="ĐĂNG XUẤT"
        // onPressLeft={onPressChangePass}
        // onPressRight={onPressLogout}
        // type="secondary"
        customStyle={{margin: setValue(16)}}
      />
    </View>
  );
};

export default InfoVessel;
