import {useNavigation} from '@react-navigation/native';
import {COLORS} from 'assets';
import {Content, DelayRenderComponent, Header, Table} from 'components';
import {
  AppButton,
  AppIcon,
  AppImage,
  AppText,
  BaseScreen,
  CustomTextField,
  FlexView,
  SizeBox,
} from 'elements';
import {IconType} from 'elements/AppIcon';
import {isNaN} from 'lodash';
import SCREENS from 'navigation/Screens';
import React, {useState, useCallback, useMemo, memo} from 'react';
import {
  Alert,
  StyleSheet,
  TouchableOpacity,
  View,
  Platform,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import KeyboardSpacer from 'react-native-keyboard-spacer';
import Modal from 'react-native-modal';
import {ModalContent} from 'react-native-modals';
import useDiaryStore from 'stores/diaryStore';
import useFishStore from 'stores/fishStore';
import ToggleSwitch from 'toggle-switch-react-native';
import {FONT_TYPE, METRICS, globalStyles} from 'utils';
import {formatNumber} from 'utils/dateUtils';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  screenHeight,
  screenWidth,
  setValue,
} from 'utils/layout';

// Memoize the ItemDiary component to prevent unnecessary re-renders
export const ItemDiary = memo(
  ({
    item,
    edit,
    onPressDelete,
    onPressUpdate,
  }: {
    item: any;
    onPressDelete: () => void;
    onPressUpdate: () => void;
    edit?: boolean;
  }) => {
    const styles = StyleSheet.create({
      btnDelete: {
        alignItems: 'center',
        borderColor: COLORS.GREY,
        borderRadius: 15,
        borderWidth: 1,
        height: 25,
        justifyContent: 'center',
        width: 25,
        marginLeft: 8,
      },
      itemContainerStyle: {
        flexDirection: 'row',
        marginHorizontal: 4,
        paddingRight: 8,
        paddingVertical: 4,
        justifyContent: 'space-between',
      },
      tab1: {
        justifyContent: 'flex-start',
        flexDirection: 'row',
        width: screenWidth * 0.45,
      },
      itemTab1: {flex: 1, marginLeft: 16},
      tab2: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        width: screenWidth * 0.45,
      },
      textTab2: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
      },
      image: {width: 40, height: 40, borderRadius: 20, marginLeft: 8},
    });

    const khoi_luong = isNaN(Number(item?.khoi_luong))
      ? '0'
      : formatNumber(Number(item?.khoi_luong));

    // Handle press events with useCallback
    const handleItemPress = useCallback(() => {
      if (onPressUpdate) {
        onPressUpdate();
      }
    }, [onPressUpdate]);

    const handleDeletePress = useCallback(() => {
      if (onPressDelete) {
        onPressDelete();
      }
    }, [onPressDelete]);

    return (
      <TouchableOpacity
        disabled={!edit}
        key={item.ten_dia_phuong}
        style={styles.itemContainerStyle}
        onPress={handleItemPress}>
        <View style={styles.tab1}>
          <AppImage
            customStyle={styles.image}
            resizeMode={'cover'}
            src={{
              uri: item.url,
            }}
          />
          <View style={styles.itemTab1}>
            <AppText
              font={FONT_TYPE.BOLD}
              // text={item.ten_dia_phuong ?? item.ten}
              text={item.ten ?? item.ten_dia_phuong}
            />
            <AppText color={COLORS.BLUE} text={item.ma_code} />
          </View>
        </View>

        <View style={styles.tab2}>
          <AppText
            color={COLORS.MAIN_COLOR}
            text={`${khoi_luong} (KG)`}
            customStyle={styles.textTab2}
          />
          <View style={styles.btnDelete}>
            <AppIcon
              color={COLORS.GREY}
              name="cross"
              size={setValue(20)}
              type={IconType.entypo}
              onPress={handleDeletePress}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  },
);

const CollectInfomationScreen = () => {
  const {navigate, goBack} = useNavigation();
  // State for modal
  const [khoiLuong, setKhoiLuong] = useState<any>(0);
  const [isFav, setIsFav] = useState(true);
  const [openModal, setOpenModal] = useState<any>(false);
  const [fishSelected, setFishSelected] = useState<any>({});

  const {
    setDiaryLocal,
    setDiary,
    setConfigQuickListFish,
    diary: {diaryCurrent, diaryLocal, configQuickDiary},
  } = useDiaryStore();

  const {setFavorite, fishData: storedFishData} = useFishStore();

  // Define setFavFish function before it's used
  const setFavFish = useCallback(
    (value: any) => {
      setIsFav(value);
      const param = {...fishSelected, isFavorite: value};
      setFavorite(param);

      const {listFish} = configQuickDiary;
      const newListFish = listFish.concat([param]);
      // Update config
      setConfigQuickListFish(newListFish);
    },
    [fishSelected, configQuickDiary, setFavorite, setConfigQuickListFish],
  );

  // Memoized callbacks for better performance
  const onPressDeleteFish = useCallback(
    (item: any) => {
      Alert.alert('Thông báo', 'Bạn muốn xóa loài này khỏi danh sách', [
        {
          text: 'Huỷ',
          onPress: () => {},
        },
        {
          text: 'Đồng ý',
          onPress: () => {
            const _diaryCurrent = {...diaryCurrent};
            let _san_luong = _diaryCurrent?.san_luong || [];
            _diaryCurrent.san_luong = _san_luong.filter(
              (ele: any) => ele._id !== item._id,
            );
            setDiary(_diaryCurrent);
          },
        },
      ]);
    },
    [diaryCurrent, setDiary],
  );

  const onPressUpdate = useCallback(
    (item: any) => {
      setFishSelected(item);
      const _item = storedFishData?.find((ele: any) => ele._id === item._id);
      if (_item) {
        setIsFav(_item?.isFavorite);
      }
      setKhoiLuong(item?.khoi_luong || 0);
      setOpenModal(true);
    },
    [storedFishData],
  );

  const onCallback = useCallback(() => {
    goBack();
  }, [goBack]);

  const onFinishedDiary = useCallback(() => {
    // Create a clean copy of the diary current data
    const param = {
      ...diaryCurrent,
      trang_thai: 2,
      thoi_gian_thu: new Date(),
      san_luong:
        diaryCurrent.san_luong?.filter(
          (ele: any) => !isNaN(ele?.khoi_luong) && Number(ele?.khoi_luong) >= 0,
        ) || [],
    };

    // Create a copy of the diaryLocal array
    let _diaryLocal = Array.isArray(diaryLocal) ? [...diaryLocal] : [];

    // Add the new diary at the beginning and sort only once
    const sortedDiaryLocal = [param, ..._diaryLocal].sort(
      (a: any, b: any) =>
        new Date(a?.thoi_gian_tha).getTime() -
        new Date(b?.thoi_gian_tha).getTime(),
    );

    setDiaryLocal(sortedDiaryLocal);
    goBack();
  }, [diaryCurrent, diaryLocal, setDiaryLocal, goBack]);

  const onCloseModal = useCallback(() => {
    setKhoiLuong(0);
    setOpenModal(false);
  }, []);

  const onPressAdd = useCallback(() => {
    if (khoiLuong < 0) {
      return;
    }

    setFavFish(isFav);
    const param = {
      ...fishSelected,
      khoi_luong: khoiLuong,
    };

    // Create a clean copy of the current diary
    const _diaryCurrent = {...diaryCurrent};
    let _san_luong = _diaryCurrent?.san_luong || [];
    console.log('_san_luong: ', _san_luong);

    // Check if the fish already exists
    const exist = _san_luong.filter((ele: any) => ele?._id === param?._id);

    if (exist.length > 0) {
      // Update existing fish with optimized map function
      _san_luong = _san_luong.map((el: any) =>
        el?._id !== param?._id ? el : {...el, khoi_luong: khoiLuong},
      );
    } else {
      // Add new fish
      _san_luong.push(param);
    }

    _diaryCurrent.san_luong = _san_luong;
    setDiary(_diaryCurrent);
    onCloseModal();
  }, [
    khoiLuong,
    isFav,
    fishSelected,
    diaryCurrent,
    setDiary,
    onCloseModal,
    setFavFish,
  ]);

  // Memoize fish data for Table component
  const diaryFishData = useMemo(() => {
    return diaryCurrent.san_luong || [];
  }, [diaryCurrent.san_luong]);

  // Memoize the renderItem function for Table component
  const renderItem = useCallback(
    ({item}: {item: any}) => {
      return (
        <ItemDiary
          edit={true}
          item={item}
          onPressUpdate={() => onPressUpdate(item)}
          onPressDelete={() => onPressDeleteFish(item)}
        />
      );
    },
    [onPressUpdate, onPressDeleteFish],
  );

  // Memoize modal content to prevent unnecessary re-renders
  const modalContent = useMemo(() => {
    if (!openModal) {
      return null;
    }

    return (
      <Modal
        style={{
          backgroundColor: 'transparent',
          width: '100%',
          marginHorizontal: 0,
          height: screenHeight,
        }}
        isVisible={openModal}>
        <ModalContent
          style={{
            padding: 0,
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
            height: screenHeight,
            flex: 1,
            width: '100%',
            backgroundColor: COLORS.WHITE,
            justifyContent: 'flex-start',
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <CustomTextField
              required={true}
              editable={false}
              placeholderText={'LOÀI'}
              styleCustom={{flex: 1}}
              multiline={true}
              value={
                (fishSelected?.ten_dia_phuong ?? fishSelected?.ten) +
                ' (' +
                fishSelected?.ma_code +
                ')'
              }
            />

            <AppIcon
              color={COLORS.LIGHT_GREY}
              name="fish"
              size={scaleBaseOnScreenWidth(20)}
              type={IconType.ionicons}
            />
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <CustomTextField
              autoFocus={openModal}
              keyboardType="numeric"
              containerStyle={{fontFamily: FONT_TYPE.LIGHT, flex: 1}}
              required={true}
              editable={true}
              placeholderText={'SẢN LƯỢNG(KG)'}
              value={khoiLuong.toString()}
              onChangeText={(text: any) => {
                if (isNaN(Number(text))) {
                  setKhoiLuong(0);
                  return;
                }
                if (!isNaN(Number(text)) && Number(text) > 200000) {
                  setKhoiLuong(200000);
                } else {
                  setKhoiLuong(Number(text));
                }
              }}
            />
            <AppIcon
              color={COLORS.LIGHT_GREY}
              name="balance-scale"
              size={scaleBaseOnScreenWidth(20)}
              type={IconType.fontawesome}
            />
          </View>

          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <AppText
              color={COLORS.TEXT_PRIMARY}
              customStyle={{padding: METRICS.small_8}}
              font={FONT_TYPE.LIGHT}
              size={METRICS.text_16}
              text={'DANH MỤC LOÀI ƯU THÍCH'}
            />
            <ToggleSwitch
              isOn={isFav}
              onColor={COLORS.BLUE}
              offColor={COLORS.SMOKE}
              size="medium"
              onToggle={isOn => setFavFish(isOn)}
            />
          </View>
          <SizeBox h={METRICS.large_64} />
          <AppButton
            size={[1, 2]}
            text="HỦY"
            textRight="THÊM"
            type="secondary"
            onPressLeft={onCloseModal}
            onPressRight={onPressAdd}
          />
          <KeyboardSpacer
            topSpacing={
              Platform.OS === 'ios' ? 0 : scaleBaseOnScreenHeight(-200)
            }
          />
        </ModalContent>
      </Modal>
    );
  }, [
    openModal,
    fishSelected,
    khoiLuong,
    isFav,
    onCloseModal,
    onPressAdd,
    setFavFish,
  ]);

  return (
    <BaseScreen>
      <Header
        backgroundColor={COLORS.MAIN_COLOR}
        title={'THU LƯỚI'}
        iconTextRight="Thêm loài"
        onPressTextRight={() => {
          // @ts-ignore - Ignore the type error for navigation params
          navigate(SCREENS.SUGGEST_KIND, {id: diaryCurrent?.id});
        }}
      />
      <Content>
        <DelayRenderComponent style={[globalStyles.flex1, {padding: 16}]}>
          <ScrollView>
            <Table
              // item={diaryCurrent}
              data={diaryFishData}
              renderItem={renderItem}
            />
          </ScrollView>
          <FlexView flex={1} />
          <AppButton
            size={[1, 1]}
            text="HUỶ"
            textRight="HOÀN THÀNH"
            type="secondary"
            onPressLeft={onCallback}
            onPressRight={onFinishedDiary}
          />
        </DelayRenderComponent>
        {modalContent}
      </Content>
    </BaseScreen>
  );
};

export default CollectInfomationScreen;
