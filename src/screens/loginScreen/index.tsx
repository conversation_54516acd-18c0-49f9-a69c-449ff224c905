import useLoginScreen from './useLoginScreen';
import {Picker} from '@react-native-picker/picker';
import {useNavigation} from '@react-navigation/native';
import {COLORS, IMAGES} from 'assets';
import {
  AppButton,
  AppIcon,
  AppImage,
  AppText,
  BaseScreen,
  FlexView,
  SizeBox,
} from 'elements';
import {IconType} from 'elements/AppIcon';
import {isEmpty} from 'lodash';
import SCREENS from 'navigation/Screens';
import React, {useRef, useState} from 'react';
import {Controller} from 'react-hook-form';
import {Alert, TouchableOpacity, Text} from 'react-native';
import {
  ImageBackground,
  StyleSheet,
  TextInput,
  TextInputProps,
  View,
} from 'react-native';
import {ALERT_TYPE, Dialog} from 'react-native-alert-notification';
import {ScrollView} from 'react-native-gesture-handler';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {FONT_TYPE, METRICS} from 'utils';
import {dm_tinh} from 'utils/dm_nganhnghe';
import {screenHeight, setValue} from 'utils/layout';

const LoginInput = ({
  title,
  placeHolder,
  icon,
  errors,
  iconRight,
  iconPressRight,
  ...props
}: {
  title: string;
  placeHolder: string;
  icon: string;
  iconRight: string;
  iconPressRight: any;
  props?: TextInputProps;
  errors?: object;
}) => {
  return (
    <>
      <AppText
        color={COLORS.TEXT_PRIMARY}
        font={FONT_TYPE.BOLD}
        size={METRICS.text_22}
        text={title}
      />
      <SizeBox h={METRICS.small_8} />
      <View
        style={{
          flexDirection: 'row',
          height: 50,
          alignItems: 'center',
        }}>
        <TextInput
          placeholder={placeHolder}
          placeholderTextColor={COLORS.MAIN_COLOR}
          style={loginInputStyle.container}
          {...props}
        />
        <AppIcon
          name={icon}
          size={18}
          style={{padding: 14, position: 'absolute'}}
          type={IconType.antDesign}
        />
        {iconRight && (
          <TouchableOpacity
            style={{
              right: 0,
              top: 0,
              position: 'absolute',
              height: 50,
              width: 50,
            }}
            onPress={iconPressRight}>
            <AppIcon
              // onPress={iconPressRight}
              name={iconRight}
              size={20}
              style={{right: 20, top: 15, position: 'absolute'}}
              type={IconType.antDesign}
            />
          </TouchableOpacity>


        }

      </View>
      {!isEmpty(errors) ? (
        <AppText
          size={METRICS.text_14}
          color={COLORS.RED}
          customStyle={{marginTop: METRICS.tiny_4}}
          text={errors?.message}
        />
      ) : null}
    </>
  );
};

const LoginScreen = () => {
  const {
    onPressLogin,
    control,
    errors,
    reset,
    selectedValue,
    setSelectedValue,
  } = useLoginScreen();

  const [positionImage, setPositionImage] = useState(1);
  const scrollRef = useRef<any>();
  const onFocus = (value = 1) => {
    setPositionImage(METRICS.normal_large);
    scrollRef.current?.scrollTo({
      y: 100 * value,
      animated: true,
    });
  };
  const onBlurs = () => {
    setPositionImage(1);
    scrollRef.current?.scrollTo({
      y: 0,
      animated: true,
    });
  };

  const onPressChangePass = () => {
    // Dialog.show({
    //   type: ALERT_TYPE.WARNING,
    //   title: 'Thông báo',
    //   textBody: 'Vui lòng liên hệ IT để thực hiện yêu cầu.',
    //   button: 'Đồng ý',
    //   onPressButton() {
    //     Dialog.hide();
    //     // mutate({id_tau_ca: user?.id_tau_ca?._id}); // Remove the unnecessary object parameter
    //   },
    // });

    Alert.alert(
      'Thông báo',
      'Vui lòng liên hệ nhân viên hỗ trợ địa phương để lấy lại mật khẩu.',
      [
        {
          text: 'Đồng ý',
          onPress: () => {},
        },
      ],
    );
  };
  const navigation: any = useNavigation();
  const openScanQR = ()=>{
    navigation.navigate(SCREENS.SCAN_QR, {callback: cbQR});
  };
  const cbQR = (data: any)=>{
    try {
      if (data?.province?.toUpperCase() == 'GTO') {
        reset({username: data.province + '-' + data?.username});
      } else {
        if (data?.vesselName) {
          reset({username: data?.username, province: data.province});
          setSelectedValue(data.province);
        } else {
          Alert.alert('Thông báo', 'Mã QR không hợp lệ');
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', 'Mã QR không hợp lệ');
    }
  };

  return (
    <BaseScreen edges={[]}>
      <ImageBackground source={IMAGES.IMG_LOGIN_BG} style={styles.container}>
        <ScrollView ref={scrollRef}>
          <KeyboardAwareScrollView
            bounces={false}
            contentContainerStyle={{flex: 1}}
            extraHeight={0}>
            <View
              style={{
                ...styles.viewLogin,
                height: screenHeight,
              }}>
              <AppText
                color={COLORS.TEXT_PRIMARY}
                customStyle={{textAlign: 'center', height: 'auto', padding: 5}}
                font={FONT_TYPE.BOLD}
                size={METRICS.text_20}
                text="HỖ TRỢ NHẬT KÝ ĐIỆN TỬ"
              />
              {/* <SizeBox h={METRICS.nomal} />
              <AppText
                color={COLORS.TEXT_PRIMARY}
                font={FONT_TYPE.BOLD}
                size={METRICS.text_22}
                text="Đăng nhập"
              /> */}
              {/* <SizeBox h={METRICS.small_8} />
              <AppText
                text="Đăng nhập tài khoản tàu cá để sử dụng hệ thống"
                size={METRICS.text_12}
              /> */}
              <SizeBox h={METRICS.nomal} />
              <Controller
                name="province"
                control={control}
                render={({}) => (
                  <View
                    style={{
                      padding: 10,
                      borderRadius: 10,
                      backgroundColor: COLORS.INPUT_BG,
                      justifyContent: 'space-between',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <AppText
                      color={COLORS.TEXT_PRIMARY}
                      font={FONT_TYPE.BOLD}
                      size={METRICS.text_14}
                      text={'Thành phố/Tỉnh'}
                      customStyle={{flex: 0.5}}
                    />
                    <View style={{flex: 1}}>
                      <Picker

                      selectedValue={selectedValue}
                        onValueChange={(itemValue, itemIndex) => {
                          setSelectedValue(itemValue);
                        }}>
                        {dm_tinh.map((item: any, index: number) => (
                          <Picker.Item
                            key={index}
                            label={`${item.ma_code} - ${item.ten}`}
                            value={item.ma_code}
                            style={{
                              backgroundColor: COLORS.INPUT_BG,
                              color: COLORS.TEXT_PRIMARY,
                            }}
                          />
                        ))}
                      </Picker>
                    </View>

                </View>
                )}
              />

              <Controller
                control={control}
                rules={{
                  required: 'Tài khoản không để trống',
                  minLength: {
                    value: 5,
                    message: 'Tài khoản ít nhất 5 ký tự',
                  },
                }}
                render={({field: {onChange, onBlur, value}}) => (
                  <LoginInput
                    icon={'user'}
                    placeHolder={'Tên đăng nhập'}
                    title={'Tài khoản'}
                    onChangeText={onChange}
                    onFocus={() => onFocus(1.5)}
                    onBlur={onBlurs}
                    value={value}
                    errors={errors.username}
                    iconRight="qrcode"
                    iconPressRight={openScanQR}
                  />
                )}
                name="username"
              />
              <SizeBox h={METRICS.nomal_medium} />
              <Controller
                control={control}
                rules={{
                  required: 'Mật khẩu không để trống',
                  minLength: {
                    value: 4,
                    message: 'Mật khẩu ít nhất 4 ký tự',
                  },
                }}
                render={({field: {onChange, onBlur, value}}) => (
                  <LoginInput
                    icon={'lock'}
                    placeHolder={'Mật khẩu'}
                    title={'Mật khẩu'}
                    secureTextEntry
                    onChangeText={onChange}
                    onFocus={() => onFocus(3)}
                    onBlur={onBlurs}
                    value={value}
                    errors={errors.password}
                  />
                )}
                name="password"
              />
              <SizeBox h={METRICS.nomal} />
              <View
                style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                <AppText
                  customStyle={{textDecorationLine: 'underline'}}
                  text="Chính sách sử dụng"
                />
                <TouchableOpacity onPress={onPressChangePass}>
                  <AppText
                    customStyle={{textDecorationLine: 'underline'}}
                    text="Quên mật khẩu"
                  />
                </TouchableOpacity>
              </View>

              <SizeBox h={METRICS.normal_large} />
              <AppButton
                text={'Đăng nhập'}
                type={'primary'}
                onPress={onPressLogin}
              />
              {positionImage == 1 ? <FlexView flex={0.5} /> :  <SizeBox h={positionImage} /> }
              <AppImage
                resizeMode="contain"
                src={IMAGES.IMG_LOGIN_LOGO}
                customStyle={styles.image}
              />
            </View>
          </KeyboardAwareScrollView>
        </ScrollView>
      </ImageBackground>
    </BaseScreen>
  );
};

const loginInputStyle = StyleSheet.create({
  container: {
    backgroundColor: COLORS.INPUT_BG,
    color: COLORS.TEXT_PRIMARY,
    borderRadius: 12,
    flex: 1,
    height: 50,
    paddingLeft: 35,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  viewLogin: {
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    flex: 1,
    marginTop: '50%',
    padding: METRICS.nomal,
  },
  image: {
    backgroundColor: 'white',
    height: setValue(40),
  },
});

export default LoginScreen;
