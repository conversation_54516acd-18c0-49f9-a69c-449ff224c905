import {useNavigation} from '@react-navigation/native';
import SCREENS from 'navigation/Screens';
import {useState} from 'react';
import {useForm} from 'react-hook-form';
import {Alert, Platform} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {useMutation} from 'react-query';
import {apiServices} from 'services';
import useAppStore from 'stores/appStore';
import useStorageStore from 'stores/useStorageStore';
import {dm_tinh} from 'utils/dm_nganhnghe';

const useLoginScreen = () => {
  const {navigate} = useNavigation();
  const {showLoading, hideLoading} = useAppStore();
  const {setCredential, credential} = useStorageStore();
  const [selectedValue, setSelectedValue] = useState(
    credential?.account?.province ?? dm_tinh[0].ma_code,
  );

  const {
    control,
    handleSubmit,
    formState: {errors},
    reset,
  } = useForm({
    defaultValues: {
      username: credential?.account?.username?.split('-')[1] ?? '',
      // username: 'gto-0002-ts',
      // password: '2579',
      password: credential?.account?.password ?? '',
      province: credential?.account?.province ?? '',
    },
  });

  const {mutate} = useMutation<{data: any}, unknown, void, void>(
    bodyData =>
      apiServices.Post({
        url: '/signup',
        body: bodyData as Record<string, any>,
      }),
    {
      onMutate: () => {
        showLoading(true);
      },
      onSuccess: async ({data}, variables: any) => {
        showLoading(true);
        if (data.token && data.user) {
          setCredential(data.token, data.user, {
            username: variables.username,
            password: variables.password,
            province: selectedValue,
          });
          // thực hiện Gọi Lấy danh sách loài ưa thích
          setTimeout(() => {
            showLoading(false);
            navigate(SCREENS.HOME_SCREEN as never, {init: true});
          }, 1500);
        }
      },
      onError: (error: any) => {
        showLoading(false);
        Alert.alert('Thông báo', error.message, [
          {
            text: 'Đồng ý',
            onPress: () => {},
          },
        ]);
        // Dialog.show({
        //   type: ALERT_TYPE.WARNING,
        //   title: 'Thông báo',
        //   textBody: error.message,
        //   button: 'Đồng ý',
        //   onPressButton() {
        //     Dialog.hide();
        //   },
        // });
      },
    },
  );

  const onSubmit = async (data: {username: string; password: string}) => {
    let username = selectedValue + '-' + data.username + '-ts';
    if (data.username?.toLowerCase().trim().startsWith('gto-')) {
      username = data.username + '-ts';
    }
    const bodyData: Record<string, any> = {
      username: username,
      password: data.password,
      device_id: await DeviceInfo.getUniqueId(),
      device_os: Platform.OS,
      device_name: await DeviceInfo.getDeviceName(),
      device_time: new Date(),
      is_app: true,
    };
    mutate(bodyData);
  };

  const onPressLogin = handleSubmit(onSubmit);

  return {
    onPressLogin,
    control,
    onSubmit,
    errors,
    reset,
    selectedValue,
    setSelectedValue,
  };
};

export default useLoginScreen;
