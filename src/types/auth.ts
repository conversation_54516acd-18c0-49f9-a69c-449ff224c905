export interface User {
  _id: string;
  ho_ten: string;
  ten_dang_nhap: string;
  email?: string;
  sdt?: string;
  id_tau_ca?: {
    _id: string;
    so_dang_ky: string;
    chieu_dai: number;
    chieu_rong: number;
    chieu_cao: number;
    trong_tai: number;
    cong_suat: number;
    nghe_chinh: string;
    nghe_phu: string;
  };
  role?: string;
  avatar?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Credential {
  token: string;
  user: User;
}

export interface AuthState {
  credential: Credential | null;
  isAuthenticated: boolean;
  error: string | null;
}

export interface LoginParams {
  ten_dang_nhap: string;
  mat_khau: string;
}
