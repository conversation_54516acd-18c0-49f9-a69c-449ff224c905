export interface ApiResponse<T = any> {
  status: string;
  data: T;
  message?: string;
  count?: number;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: Record<string, 1 | -1>;
  filter?: Record<string, any>;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

export interface ApiRequestConfig {
  url: string;
  query?: Record<string, any>;
  body?: Record<string, any>;
  headers?: Record<string, string>;
}
