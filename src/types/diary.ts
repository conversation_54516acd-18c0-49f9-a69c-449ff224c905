export interface DiaryEntry {
  _id?: string;
  thoi_gian_tha: string | null;
  thoi_gian_thu: string | null;
  vi_tri_tha: GeoPosition | null;
  vi_tri_thu: GeoPosition | null;
  trang_thai: number;
  san_luong: FishCatch[];
  is_online?: boolean;
  item_id?: string;
  id_item?: string;
  id_tau_ca?: string;
}

export interface GeoPosition {
  latitude: number;
  longitude: number;
  timestamp?: number;
}

export interface FishCatch {
  id: string;
  _id: string;
  id_nhom_loai: string;
  khoi_luong: number;
  ten_nhom?: string;
  ten_loai?: string;
  ma_loai?: string;
}

export interface DiaryConfig {
  autoTp: number;
  listFish: FishCatch[];
  dateDiary: {
    from: string | null;
    to: string | null;
  };
  autoDay: {
    timeDiary: {
      from: {
        start: string | null;
        end: string | null;
      };
      to: {
        start: string | null;
        end: string | null;
      };
    };
    numDayOnDiary: number;
  };
  autoHours: {
    waitTime: {
      from: number | null;
      to: number | null;
    };
    releaseTime: {
      from: number | null;
      to: number | null;
    };
    retrieveTime: {
      from: number | null;
      to: number | null;
    };
    processTime: {
      from: number | null;
      to: number | null;
    };
  };
}

export interface DiaryState {
  lastDiary: {
    id_tau_ca: string | null;
    so_dang_ky: string | null;
    thoi_gian_boc_do: string | null;
    thoi_gian_cap_cang: string | null;
    thoi_gian_xuat_cang: string | null;
    tinh_trang: string | null;
    start_diary: boolean;
  };
  diaryCurrent: DiaryEntry;
  diaryLocal: DiaryEntry[];
  diaryServer: DiaryEntry[];
  diaryDelete: DiaryEntry[];
  configQuickDiary: DiaryConfig;
}
