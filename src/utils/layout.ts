import {Dimensions, Platform, StatusBar} from 'react-native';
import DeviceInfo from 'react-native-device-info';

const {width, height} = Dimensions.get('window');

export function getStatusBarHeight() {
  return Platform.select({
    ios: DeviceInfo.hasNotch() ? 54 : 20,
    android: StatusBar.currentHeight,
    default: 0,
  });
}

export function getlengthfromVw(number: number) {
  return (number / 100) * width;
}

export function getlengthfromVh(number: number) {
  return (number / 100) * height;
}

const pivotWidth = 375;
const pivotHeight = 812;

export const scaleBaseOnScreenHeight = (value: number) => {
  const ratio = height / pivotHeight;
  return ratio > 1 ? value : value * ratio;
};

export const setValue = (value: number) => {
  const ratio = (height * width) / (pivotHeight * pivotWidth);
  return ratio >= 1 ? value : value * ratio;
};

export const scaleBaseOnScreenWidth = (value: number) => {
  const ratio = width / pivotWidth;
  return ratio >= 1 ? value : value * ratio;
};

export function getFooterHeight() {
  return DeviceInfo.hasNotch() ? 24 : 0;
}

export const footerHeight = getFooterHeight();

export const statusBarHeight = getStatusBarHeight();

export const screenWidth = width;

export const screenHeight = height;
