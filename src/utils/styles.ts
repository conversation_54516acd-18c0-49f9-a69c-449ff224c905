import COLORS from '../assets/theme/colors';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  statusBarHeight,
} from './layout';
import {StyleSheet} from 'react-native';

const globalStyles = StyleSheet.create({
  buttonIconStyle: {
    fontFamily: 'Montserrat',
    fontWeight: '400',
    marginRight: scaleBaseOnScreenWidth(5),
  },
  container: {backgroundColor: COLORS.WHITE, flex: 1},
  flex05: {
    flex: 0.5,
  },
  flex1: {
    flex: 1,
  },
  flex125: {
    flex: 1.25,
  },
  flex150: {
    flex: 1.5,
  },
  flex175: {
    flex: 1.75,
  },
  flex2: {
    flex: 2,
  },
  flex3: {
    flex: 3,
  },
  flex75: {
    flex: 0.75,
  },
  full: {
    margin: 0,
    width: '100%',
  },
  gutter: {
    height: scaleBaseOnScreenHeight(10),
    width: scaleBaseOnScreenWidth(10),
  },
  half: {
    margin: 0,
    width: '50%',
  },
  marginTopSmall: {
    marginTop: scaleBaseOnScreenHeight(10),
  },
  noMarginTextField: {
    marginHorizontal: 0,
    marginLeft: 0,
    marginRight: 0,
  },
  row: {
    flexDirection: 'row',
  },
  sectionTitle: {
    backgroundColor: COLORS.LIGHT_GREY,
    color: COLORS.WHITE,
    fontFamily: 'Montserrat-Bold',
    fontSize: scaleBaseOnScreenWidth(14),
    lineHeight: scaleBaseOnScreenHeight(40),
    paddingLeft: scaleBaseOnScreenWidth(10),
    textAlign: 'center',
  },
  shadow: {
    elevation: 1,
    shadowColor: 'rgba(10, 10, 10, 0.1)',
    shadowOffset: {height: 0.5, width: 0.5},
    shadowOpacity: 0.5,
  },
  tableContainer: {
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 8,
    marginHorizontal: scaleBaseOnScreenWidth(4),
    marginVertical: scaleBaseOnScreenHeight(5),
    overflow: 'hidden',
    paddingBottom: scaleBaseOnScreenHeight(6),
  },
  tableFlexWrap: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tableHeader: {
    backgroundColor: COLORS.LIGHT_GREY,
    height: scaleBaseOnScreenHeight(36),
    marginBottom: scaleBaseOnScreenHeight(5),
    overflow: 'hidden',
    paddingLeft: scaleBaseOnScreenWidth(10),
    paddingRight: scaleBaseOnScreenWidth(10),
  },
  textBody: {
    color: COLORS.LIGHT_GREY,
    fontFamily: 'Montserrat-Light',
    fontSize: scaleBaseOnScreenWidth(12),
    fontWeight: '300',
  },
  textButton: {
    color: COLORS.LIGHT_GREY,
    fontFamily: 'Montserrat-Bold',
    fontSize: scaleBaseOnScreenWidth(12),
  },
  textCaption: {
    color: COLORS.LIGHT_GREY,
    fontFamily: 'Montserrat-Medium',
    fontSize: scaleBaseOnScreenWidth(11),
  },
  textDisplayLarge: {
    color: COLORS.LIGHT_GREY,
    fontFamily: 'Montserrat-Black',
    fontSize: scaleBaseOnScreenWidth(21),
  },
  textDisplayMedium: {
    color: COLORS.LIGHT_GREY,
    fontFamily: 'Montserrat-Black',
    fontSize: scaleBaseOnScreenWidth(15),
  },
  textDisplaySmall: {
    color: COLORS.LIGHT_GREY,
    fontFamily: 'Montserrat-Black',
    fontSize: scaleBaseOnScreenWidth(14),
  },
  textDisplayXlarge: {
    color: COLORS.LIGHT_GREY,
    fontFamily: 'Montserrat-Bold',
    fontSize: scaleBaseOnScreenWidth(25),
  },
  textField: {
    color: COLORS.LIGHT_GREY,
    fontFamily: 'Montserrat-Bold',
    fontSize: scaleBaseOnScreenWidth(15),
    fontWeight: '600',
  },
  textHeading: {
    color: COLORS.GREY,
    fontFamily: 'Montserrat-Bold',
    fontSize: scaleBaseOnScreenWidth(14),
  },
  textHeadingLarge: {
    color: COLORS.GREY,
    fontFamily: 'Montserrat-Bold',
    fontSize: scaleBaseOnScreenWidth(18),
  },
  textLabel: {
    color: COLORS.LIGHT_GREY,
    fontFamily: 'Montserrat-Bold',
    fontSize: scaleBaseOnScreenWidth(9),
  },
  textSubHeading: {
    color: COLORS.GREY,
    fontFamily: 'Montserrat-SemiBold',
    fontSize: scaleBaseOnScreenWidth(12),
  },
  topBadge: {
    position: 'absolute',
    right: scaleBaseOnScreenWidth(10),
    top: statusBarHeight + scaleBaseOnScreenHeight(15),
  },
  validGray: {
    backgroundColor: COLORS.LIGHT_GREY,
  },
  validGreen: {
    backgroundColor: COLORS.GREEN,
  },
  validRed: {
    backgroundColor: COLORS.SOFT_RED,
  },
});

export default globalStyles;
