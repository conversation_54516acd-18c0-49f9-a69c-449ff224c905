import moment from 'moment';

export const formatDate = (
  date: any,
  format = 'DD/MM/YYYY',
  defaultValue = '',
) => {
  return date ? moment(date).format(format) : defaultValue;
};

export const formatNumber = (n: any) => {
  try {
    if (n == undefined || n == null) {
      return '0';
    }
    return Number(n)
      ?.toFixed(0)
      .replace(/./g, function (c: any, i: any, a: any) {
        return i > 0 && c !== '.' && (a.length - i) % 3 === 0 ? ',' + c : c;
      });
  } catch (error) {
    return '0';
  }
};
