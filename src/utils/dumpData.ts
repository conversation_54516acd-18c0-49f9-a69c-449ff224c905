import {METRICS} from './metrics';

const DUMPDATA = {
  tableData: {
    header: [
      {
        name: '<PERSON><PERSON><PERSON>',
        style: {textStyle: {fontWeight: 'bold', fontSize: METRICS.text_14}},
      },
      {
        name: '<PERSON> <PERSON><PERSON> b<PERSON> (KG)',
        style: {
          textStyle: {fontWeight: 'bold', fontSize: METRICS.text_14},
          viewStyle: {
            alignItems: 'center',
            justifyContent: 'center',
          },
        },
      },
    ],
    data: [1, 2, 3, 4, 5, 6, 7],
    footer: [
      {
        name: 'Tổng',
        style: {textStyle: {fontWeight: 'bold', fontSize: METRICS.text_14}},
      },
      {
        name: '0 kg',
        style: {
          viewStyle: {
            alignItems: 'center',
            justifyContent: 'center',
          },
          textStyle: {fontWeight: 'bold', fontSize: METRICS.text_14},
        },
      },
    ],
  },
};

export {DUMPDATA};
