import SCREENS from './Screens';
import {useNetInfoInstance} from '@react-native-community/netinfo';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {COLORS} from 'assets';
import {get, isEmpty} from 'lodash';
import React, {useEffect} from 'react';
import {StatusBar, Text, View} from 'react-native';
import {
  CatchDiary,
  CatchDiaryHistory,
  CollectInfomationScreen,
  HistoryMining,
  HomeScreen,
  InfoVessel,
  LoginScreen,
  PaymentInfomation,
  QuickCatchDiary,
  RenewRegistration,
  SuggestedKind,
  ScanCode,
} from 'screens';
import ConfigGPSWifi from 'screens/configGPSwifi';
import DetailHistoryMining from 'screens/detailHistoryMining';
import {logNavi} from 'services/logService';
import useDiaryStore from 'stores/diaryStore';
import useFishStore from 'stores/fishStore';
import useStorageStore from 'stores/useStorageStore';

const navigationOptions = {fullScreenGestureEnabled: true};

const Stack = createNativeStackNavigator();

const getActiveRouteName = state => {
  const route = state.routes[state.index];
  if (route.state) {
    return getActiveRouteName(route.state);
  }

  logNavi(route);
  return route.name;
};

const AppNaviation = () => {
  const navigationRef: any = React.useRef();

  const onStateChange = (state: any) => {
    const currentRouteName = getActiveRouteName(state);
    navigationRef.current = currentRouteName;
  };
  const {
    netInfo: {type, isConnected},
    refresh,
  } = useNetInfoInstance();

  const {getCredential, credential} = useStorageStore();
  const {getDiary} = useDiaryStore();
  const {getFish} = useFishStore();

  useEffect(() => {
    if (isConnected) {
      // get user
      getCredential();
      getDiary(credential?.user?._id ?? '');
      getFish(credential?.user?._id ?? '');
    }
  }, []);

  return (
    <NavigationContainer ref={navigationRef} onStateChange={onStateChange}>
      {!isConnected && (
        <View
          style={{
            height: 30 + (StatusBar.currentHeight ?? 0),
            backgroundColor: COLORS.LEMON,
            justifyContent: 'flex-end',
            padding: 5,
          }}>
          <Text>
            {'Hiện tại không có kết nối Internet. Bạn đang ở chế độ Offline.'}
          </Text>
        </View>
      )}
      <Stack.Navigator screenOptions={{headerShown: false}}>
        {isEmpty(credential?.token?.trim()) ? (
          <Stack.Screen
            component={LoginScreen}
            name={SCREENS.LOGIN_SCREEN}
            options={navigationOptions}
          />
        ) : (
          <Stack.Screen
            component={HomeScreen}
            name={SCREENS.HOME_SCREEN}
            options={navigationOptions}
          />
        )}
        <Stack.Screen
          component={CollectInfomationScreen}
          name={SCREENS.COLLECT_INFORMATION_SCREEN}
          options={navigationOptions}
        />
        <Stack.Screen
          component={SuggestedKind}
          name={SCREENS.SUGGEST_KIND}
          options={navigationOptions}
        />
        <Stack.Screen
          component={RenewRegistration}
          name={SCREENS.REVIEW_REGISTRATION}
          options={navigationOptions}
        />
        <Stack.Screen
          component={InfoVessel}
          name={SCREENS.INFO_VERSEL}
          options={navigationOptions}
        />
        <Stack.Screen
          component={PaymentInfomation}
          name={SCREENS.PAYMENT_INFOMATION}
          options={navigationOptions}
        />
        <Stack.Screen
          component={HistoryMining}
          name={SCREENS.HISTORY_NIMING}
          options={navigationOptions}
        />
        <Stack.Screen
          component={CatchDiary}
          name={SCREENS.CATCH_DIARY}
          options={navigationOptions}
        />
        <Stack.Screen
          component={CatchDiaryHistory}
          name={SCREENS.CATCH_DIARY_HISTORY}
          options={navigationOptions}
        />
        <Stack.Screen
          component={DetailHistoryMining}
          name={SCREENS.DETAIL_HISTORY_MINING}
          options={navigationOptions}
        />

        <Stack.Screen
          component={QuickCatchDiary}
          name={SCREENS.QUICK_CATCH_DIARY}
          options={navigationOptions}
        />
        <Stack.Screen
          component={ScanCode}
          name={SCREENS.SCAN_QR}
          options={navigationOptions}
        />
        <Stack.Screen
          component={ConfigGPSWifi}
          name={SCREENS.CONFIG_GPS_WIFI}
          options={navigationOptions}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNaviation;
