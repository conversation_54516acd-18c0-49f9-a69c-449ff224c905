import {Platform} from 'react-native';
import reactotron from 'reactotron-react-native';

const logServiceDebug = console;

const logServiceRelease = {
  log: () => {},
};

const logService = __DEV__ ? logServiceDebug : logServiceRelease;

const logWithIcon = (icon: any, name: any, payload: any) => {
  console.log(
    `=============== ${icon} = ${name} =================: ${JSON.stringify(
      payload,
    )}`,
  );
  reactotron.log({[icon]: name, ...payload});
};

export const logNavi = ({name, params}: any) => {
  logWithIcon('🖥 ', name, params);
};

export const logRestful = (url: any, payload: any) => {
  logWithIcon('🚀', url, payload);
};

export const logGraphql = ({operationName, variables}: any) => {
  logWithIcon('🚀', operationName, variables);
};

export const logNotification = (action: any, payload: any) => {
  logWithIcon('🔔', action, payload);
};

export const logStringee = (name: any, payload: any) => {
  logWithIcon('📞', Platform.OS + ' ' + name, payload);
};

export default logService;
