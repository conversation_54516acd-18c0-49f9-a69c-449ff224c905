import axios from 'axios';
import {isEmpty} from 'lodash';
import qs from 'query-string';
import {Platform} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import useStorageStore from 'stores/useStorageStore';
import {BASE_URL} from 'utils/constant';

const headers = {
  'Content-Type': 'application/json',
  accept: 'application/json',
  APP_CHANNEL: Platform.OS == 'ios' ? '02' : '01',
  APP_VERSION: DeviceInfo.getReadableVersion(),
};

const server = axios.create({
  baseURL: BASE_URL,
  headers: headers,
});

interface IGet {
  url: string;
  query?: Record<string, any>;
}

interface IPost {
  url: string;
  body?: Record<string, any>;
}

server.interceptors.request.use(
  async config => {
    const {credential} = useStorageStore.getState();
    if (!isEmpty(credential) && !isEmpty(credential.token)) {
      config.headers['access-by-token'] = credential.token;
    } else {
      config.headers['access-by-token'] = null;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  },
);

server.interceptors.response.use(
  response => {
    return {
      ...response,
    };
  },
  error => {
    if (error.response && error.response.data) {
      if (
        error.response.status == 403 &&
        error.response.data.message == 'Lỗi token.'
      ) {
        return 'logout';
      }
      return Promise.reject(error.response.data);
    }
    return Promise.reject(error.message);
  },
  // error => {
  //   return Promise.reject(error);
  // },
);

const Get = async <T>({url, query = {}}: IGet): Promise<T> => {
  const queryString = isEmpty(query) ? '' : `?${qs.stringify(query)}`;
  const response = await server.get(`${url + queryString}`);
  return response.data;
};

const Post = async <T>({url, body = {}}: IPost): Promise<T> => {
  const response = await server.post(url, body);
  return response.data;
};

const Delete = async <T>({url, body = {}}: IPost): Promise<T> => {
  const response = await server.delete(url, body);
  return response.data;
};

const Put = async <T>({url, body = {}}: IPost): Promise<T> => {
  const response = await server.put(url, body);
  return response.data;
};

const apiServices = {Get, Post, Delete, Put};

export default apiServices;
