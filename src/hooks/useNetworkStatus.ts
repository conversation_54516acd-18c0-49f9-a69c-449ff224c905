import {useNetInfoInstance} from '@react-native-community/netinfo';
import {useCallback, useMemo} from 'react';

interface NetworkStatus {
  isConnected: boolean;
  isWifi: boolean;
  isCellular: boolean;
  isOffline: boolean;
  connectionType: string;
  checkConnection: () => Promise<boolean>;
}

export const useNetworkStatus = (): NetworkStatus => {
  const {netInfo, refresh} = useNetInfoInstance();

  const isConnected = useMemo(() => {
    return netInfo.isConnected === true;
  }, [netInfo.isConnected]);

  const isWifi = useMemo(() => {
    return netInfo.type === 'wifi';
  }, [netInfo.type]);

  const isCellular = useMemo(() => {
    return netInfo.type === 'cellular';
  }, [netInfo.type]);

  const isOffline = useMemo(() => {
    return !isConnected;
  }, [isConnected]);

  const connectionType = useMemo(() => {
    return netInfo.type || 'unknown';
  }, [netInfo.type]);

  const checkConnection = useCallback(async (): Promise<boolean> => {
    const result: any = await refresh();
    return result.isConnected === true;
  }, [refresh]);

  return {
    isConnected,
    isWifi,
    isCellular,
    isOffline,
    connectionType,
    checkConnection,
  };
};
