import {useMemo} from 'react';
import useDiaryStore from 'stores/diaryStore';
import {DiaryEntry} from 'types';

interface DiarySummary {
  totalDiaries: number;
  localDiaries: number;
  onlineDiaries: number;
  currentDiaryActive: boolean;
  pendingUploadDiaries: DiaryEntry[];
  hasCompletedDiaries: boolean;
}

export const useDiarySummary = (): DiarySummary => {
  const {diary} = useDiaryStore();
  const {diaryCurrent, diaryLocal, diaryServer} = diary;

  return useMemo(() => {
    // Tính toán xem có nhật ký hiện tại đang hoạt động không
    const currentDiaryActive = diaryCurrent?.thoi_gian_tha !== null;

    // Tính số lượng nhật ký local (chưa đồng bộ)
    const pendingUploadDiaries =
      diaryLocal?.filter((ele: DiaryEntry) => !ele?.is_online) || [];
    const localDiaries =
      pendingUploadDiaries.length + (currentDiaryActive ? 1 : 0);

    // Tính số lượng nhật ký online (đã đồng bộ)
    const onlineDiaries =
      (diaryLocal?.filter((ele: DiaryEntry) => ele?.is_online)?.length || 0) +
      (diaryServer?.length || 0);

    // Tổng số nhật ký
    const totalDiaries = localDiaries + onlineDiaries;

    // Có nhật ký đã hoàn thành chưa
    const hasCompletedDiaries = diaryLocal.length > 0 || diaryServer.length > 0;

    return {
      totalDiaries,
      localDiaries,
      onlineDiaries,
      currentDiaryActive,
      pendingUploadDiaries,
      hasCompletedDiaries,
    };
  }, [diaryCurrent, diaryLocal, diaryServer]);
};
