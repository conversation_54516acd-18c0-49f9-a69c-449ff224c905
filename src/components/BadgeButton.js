import COLORS from '../assets/theme/colors';
import {AppIcon} from '../elements';
import StatefulComponent from '../lib/StatefulComponent';
import {globalStyles} from '../utils';
import {scaleBaseOnScreenWidth, setValue} from '../utils/layout';
import {isEmpty} from 'lodash';
import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

export default class BadgeButton extends StatefulComponent {
  render() {
    const {
      title,
      onPress,
      iconSize,
      style,
      name,
      iconColor,
      type,
      titleColor,
      disabled,
      color = COLORS.WHITE,
      outline,
      size = setValue(30),
    } = this.props;
    return (
      <TouchableOpacity
        disabled={disabled}
        style={[
          styles.addButton,
          outline && styles.outlineContainer,
          style,
          {
            borderRadius: size / 2,
            height: size,
            opacity: disabled ? 0.5 : 1,
          },
        ]}
        onPress={onPress}>
        <View
          style={[
            styles.addIcon,
            outline && styles.outlineIcon,
            {
              width: size - 4,
              height: size - 4,
              lineHeight: size - 4,
              borderRadius: size / 2 - 2,
            },
            color && {backgroundColor: color},
          ]}>
          <AppIcon
            color={
              iconColor ? iconColor : outline ? COLORS.WHITE : COLORS.PRIMARY
            }
            name={name}
            size={iconSize || scaleBaseOnScreenWidth(12)}
            type={type}
          />
        </View>
        {!isEmpty(title) ? (
          <Text
            style={[
              styles.add,
              outline && styles.outlineTitle,
              color && {color: titleColor || color},
            ]}>
            {title}
          </Text>
        ) : null}
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  add: {
    alignSelf: 'center',
    ...globalStyles.textSubHeading,
    color: COLORS.WHITE,
    marginHorizontal: scaleBaseOnScreenWidth(5),
  },
  addButton: {
    alignSelf: 'center',
    backgroundColor: COLORS.BITTER_GRAY,
    borderRadius: setValue(17),
    flexDirection: 'row',
    padding: 2,
  },
  addIcon: {
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: setValue(15),
    height: setValue(30),
    justifyContent: 'center',
    lineHeight: setValue(30),
    overflow: 'hidden',
    textAlign: 'center',
    width: setValue(30),
  },
  outlineContainer: {
    backgroundColor: COLORS.LIGHT_GREY,
  },
  outlineIcon: {
    backgroundColor: COLORS.BITTER_GRAY,
  },
  outlineTitle: {
    color: COLORS.BITTER_GRAY,
  },
});
