import {SelectTextField} from '.';
import {IconType} from '../elements/AppIcon';
import StatefulComponent from '../lib/StatefulComponent';
import {formatDate} from '../utils/dateUtils';
import moment from 'moment';
import React from 'react';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

export default class SelectDate extends StatefulComponent {
  componentDidMount() {
    this.fixData();
  }

  fixData = () => {
    if (
      typeof this.props?.value === 'string' &&
      this.props?.value.includes('/')
    ) {
      const {onSubmit = () => {}} = this.props;
      this.setState({
        value: moment(this.props?.value, 'DD/MM/YYYY'),
      });
      onSubmit(moment(this.props?.value, 'DD/MM/YYYY'));
    }
  };

  onSummit = value => {
    const {onSubmit = () => {}} = this.props;
    this.toggleBooleanState('showModal', false)();
    setTimeout(() => {
      onSubmit(value);
    }, 200);
  };

  render() {
    const {
      value,
      placeholderText,
      style,
      required,
      shadow,
      mode,
      editable = true,
      ...rest
    } = this.props;
    const {showModal} = this.state;
    let date = this.props?.value;
    if (typeof date === 'string' && date.includes('/')) {
      date = moment(this.props?.value, 'DD/MM/YYYY');
    }

    return (
      <React.Fragment>
        <SelectTextField
          appendIcon="calendar"
          appendIconType={IconType.antDesign}
          disabled={!editable}
          placeholderText={placeholderText}
          required={required}
          shadow={shadow}
          style={style}
          value={formatDate(
            date,
            mode === 'date'
              ? 'DD/MM/YYYY'
              : mode === 'time'
              ? 'HH:mm'
              : 'HH:mm:ss - DD/MM/YYYY',
            '',
          )}
          onPress={this.toggleBooleanState('showModal', true)}
          {...rest}
        />
        {editable ? (
          <DateTimePickerModal
            date={value ? new Date(value) : new Date()}
            isVisible={showModal}
            locale="vi"
            maximumDate={this.props?.maximumDate}
            minimumDate={this.props?.minimumDate}
            mode={mode || 'datetime'}
            onCancel={this.toggleBooleanState('showModal', false)}
            onConfirm={this.onSummit}
          />
        ) : null}
      </React.Fragment>
    );
  }
}
