import COLORS from '../../assets/theme/colors';
import StatefulComponent from '../../lib/StatefulComponent';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
} from '../../utils/layout';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import * as Animated from 'react-native-animatable';

export default class Content extends StatefulComponent {
  static defaultProps = {
    block: true,
    topIcon: false,
    delayRender: true,
    duration: 300,
  };

  componentDidMount() {
    const {delayRender} = this.props;
    if (delayRender) {
      this.delayRender(10);
    }
  }

  get containerStyle() {
    const {block, containerStyle} = this.props;
    return [styles.container, block && styles.block, containerStyle];
  }
  render() {
    const {isFocus} = this.state;
    const {children, topIcon, delayRender, duration} = this.props;
    return isFocus || !delayRender ? (
      <Animated.View
        animation="slideInUp"
        duration={duration}
        style={this.containerStyle}
        useNativeDriver>
        {children}
        {topIcon ? <View style={styles.topIcon} /> : null}
      </Animated.View>
    ) : (
      <View />
    );
  }
}

const styles = StyleSheet.create({
  block: {
    flex: 1,
  },
  container: {
    // elevation: 4,
    backgroundColor: COLORS.WHITE,
    marginTop: 2,
    overflow: 'hidden',
    borderTopLeftRadius: scaleBaseOnScreenHeight(24),
    borderTopRightRadius: scaleBaseOnScreenHeight(24),
    // paddingBottom: footerHeight,
  },
  topIcon: {
    alignSelf: 'center',
    backgroundColor: COLORS.GREY,
    borderRadius: 100,
    height: scaleBaseOnScreenHeight(5),
    marginTop: scaleBaseOnScreenHeight(8),
    position: 'absolute',
    width: scaleBaseOnScreenWidth(54),
  },
});
