import React, {ReactElement} from 'react';
import {Platform} from 'react-native';
import {Modalize} from 'react-native-modalize';

type AppModal = {
  children: ReactElement;
  modalRef: React.RefObject<Modalize>;
  modalHeight?: number | undefined;
  adjustToContentHeight?: boolean;
};

export const AppModal = ({
  children,
  adjustToContentHeight = true,
  modalHeight = undefined,
  modalRef,
}: AppModal) => {
  return (
    <Modalize
      adjustToContentHeight={adjustToContentHeight}
      modalHeight={modalHeight}
      keyboardAvoidingBehavior={Platform.OS === 'ios' ? 'padding' : 'position'}
      ref={modalRef}>
      {children}
    </Modalize>
  );
};

export default AppModal;
