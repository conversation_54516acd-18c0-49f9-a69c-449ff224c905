import React from 'react';
import {StyleSheet, Text, TouchableOpacity} from 'react-native';

export type MyButtonProps = {
  onPress: () => void;
  text: string;
};

export const MyButton = ({onPress, text}: MyButtonProps) => {
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.container}
      onPress={onPress}>
      <Text style={styles.text}>{text}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'purple',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  text: {color: 'white'},
});
