import {COLORS} from 'assets';
import {Content, DelayRenderComponent, Header} from 'components';
import {AppText, BaseScreen} from 'elements';
import React, {useState} from 'react';
import {StyleSheet, View, ViewStyle} from 'react-native';
import {TabView, TabBar} from 'react-native-tab-view';
import {FONT_TYPE, globalStyles} from 'utils';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  screenWidth,
} from 'utils/layout';

const initialLayout = {width: screenWidth};

type TabsProps = {
  renderAsoluteComponent?: () => React.ReactNode;
  onPressRight?: () => void;
  lazy?: boolean;
  tabBarStyle?: ViewStyle;
  titleStyle?: ViewStyle;
  routes: any[];
  renderScene: any;
  headerTitle?: string;
  iconRight?: string;
};

const Tabs = ({
  renderAsoluteComponent,
  lazy,
  tabBarStyle,
  titleStyle,
  outerStyle,
  routes,
  renderScene,
  headerTitle,
  iconRight,
  onPressRight,
}: TabsProps) => {
  const [_ready, _setReady] = useState();

  const renderTabBar = props => {
    return (
      <TabBar
        indicatorStyle={[styles.indicatorStyle]}
        style={[styles.tabBar, tabBarStyle]}
        renderLabel={renderTitle}
        {...props}
      />
    );
  };

  const renderTitle = ({route, focused}) => {
    return (
      <AppText
        font={FONT_TYPE.BOLD}
        color={focused ? COLORS.TEXT_PRIMARY : COLORS.WHITE}
        text={route.title}
        numberOfLines={1}
        customStyle={{
          ...styles.title,
          ...titleStyle,
          color: focused ? COLORS.TEXT_PRIMARY : COLORS.WHITE,
          fontSize: scaleBaseOnScreenWidth(12),
          fontWeight: 'bold',
        }}
      />
    );
  };

  const renderBody = () => {
    return (
      <>
        <View style={[styles.outerView, outerStyle]} />
        <TabView
          renderTabBar={renderTabBar}
          swipeEnabled
          renderScene={renderScene}
          lazy={lazy}
          onIndexChange={() => {}}
          initialLayout={initialLayout}
          navigationState={{
            index: 0,
            routes: routes,
          }}
        />
      </>
    );
  };

  return (
    <BaseScreen>
      <Header
        backgroundColor={COLORS.MAIN_COLOR}
        title={headerTitle}
        iconRight={iconRight ? 'cw' : null}
        onPressRight={onPressRight ? onPressRight : () => {}}
      />
      <Content backgroundColor={COLORS.RED}>
        <DelayRenderComponent style={[globalStyles.flex1]}>
          {renderBody()}
        </DelayRenderComponent>
      </Content>
      {renderAsoluteComponent && renderAsoluteComponent()}
    </BaseScreen>
  );
};

export default Tabs;

const styles = StyleSheet.create({
  tabBar: {
    marginTop: scaleBaseOnScreenHeight(4),
    marginHorizontal: scaleBaseOnScreenWidth(4),
    backgroundColor: COLORS.MAIN_COLOR,
    height: scaleBaseOnScreenHeight(44),
    borderRadius: scaleBaseOnScreenHeight(22),
    padding: 0,
  },
  outerView: {
    position: 'absolute',
    left: scaleBaseOnScreenWidth(2),
    right: scaleBaseOnScreenHeight(2),
    borderRadius: scaleBaseOnScreenHeight(24),
    top: scaleBaseOnScreenHeight(2),
    height: scaleBaseOnScreenHeight(48),
    // backgroundColor: COLORS.GREY,
  },
  title: {
    ...globalStyles.textButton,
    // color: 'rgba(255,255,255,0.7)',
    fontSize: scaleBaseOnScreenWidth(11),
    marginBottom: scaleBaseOnScreenHeight(18),
    width: '100%',
    padding: 0,
    lineHeight: scaleBaseOnScreenHeight(20),
  },
  titleFocued: {
    color: '#373F5B',
  },
  indicatorStyle: {
    height: '100%',
    borderRadius: scaleBaseOnScreenHeight(24),
    backgroundColor: COLORS.WHITE,
  },
});
