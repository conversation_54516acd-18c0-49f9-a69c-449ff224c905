import COLORS from '../../assets/theme/colors';
import {AppIcon, AppText} from '../../elements';
import {FONT_TYPE, METRICS} from '../../utils';
import {
  scaleBaseOnScreenHeight,
  scaleBaseOnScreenWidth,
  setValue,
} from '../../utils/layout';
import globalStyles from '../../utils/styles';
import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';

type HeaderType = {
  title: string;
  leftAction?: () => void;
  onPressRight?: () => void;
  iconLeft?: string;
  iconRight?: string;
  iconTextRight?: string;
  backgroundColor?: string;
  onPressTextRight?: () => void;
};

const Header = ({
  title,
  leftAction,
  onPressRight,
  iconLeft,
  iconRight,
  iconTextRight,
  backgroundColor,
  onPressTextRight,
}: HeaderType) => {
  const {goBack} = useNavigation();

  const onPressLeft = () => {
    if (leftAction) {
      leftAction();
    } else {
      goBack();
    }
  };
  // const onPressRight = () => {
  //   if (typeof rightAction() === 'function') {
  //     rightAction();
  //   }
  // };

  return (
    <View style={[styles.safe]}>
      <View style={[styles.extend, {backgroundColor}]} />
      <View style={styles.container}>
        <TouchableOpacity style={styles.backButton} onPress={onPressLeft}>
          <AppIcon
            color={COLORS.WHITE}
            name={iconLeft || 'chevron-left'}
            size={30}
            style={styles.icon}
            type={'Entypo'}
          />
        </TouchableOpacity>
        <AppText
          color={COLORS.WHITE}
          customStyle={styles.title}
          font={FONT_TYPE.BOLD}
          size={METRICS.text_16}
          text={title}
        />
        {iconRight && (
          <TouchableOpacity style={styles.backButton} onPress={onPressRight}>
            <AppIcon
              color={COLORS.WHITE}
              name={iconRight || 'cw'}
              size={25}
              style={styles.icon}
              type={'entypo'}
            />
          </TouchableOpacity>
        )}
        {iconTextRight && (
          <TouchableOpacity style={styles.backText} onPress={onPressTextRight}>
            <AppText
              color={COLORS.WHITE}
              customStyle={{
                marginBottom: setValue(4),
                padding: 0,
                height: 'auto',
              }}
              font={FONT_TYPE.BOLD}
              size={METRICS.text_14}
              text={iconTextRight}
            />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  backButton: {
    alignItems: 'center',
    height: scaleBaseOnScreenHeight(50),
    justifyContent: 'center',
    marginRight: scaleBaseOnScreenWidth(5),
    paddingLeft: scaleBaseOnScreenWidth(10),
    width: scaleBaseOnScreenHeight(50),
  },
  backText: {
    borderColor: COLORS.WHITE,
    borderWidth: setValue(1),
    borderRadius: setValue(20),
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
    height: scaleBaseOnScreenHeight(36),
    marginRight: scaleBaseOnScreenWidth(10),
    // padding: scaleBaseOnScreenWidth(2),
    width: scaleBaseOnScreenHeight(120),
  },
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    height: scaleBaseOnScreenHeight(54),
  },
  extend: {
    backgroundColor: COLORS.PRIMARY,
    borderBottomLeftRadius: setValue(24),
    borderBottomRightRadius: setValue(24),
    height: scaleBaseOnScreenHeight(105),
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
  },
  icon: {},
  prependButton: {
    backgroundColor: COLORS.BLUE,
    borderColor: COLORS.WHITE,
    borderRadius: setValue(20),
    borderWidth: setValue(1),
    flexDirection: 'row',
    marginRight: scaleBaseOnScreenWidth(10),
    padding: setValue(5),
  },
  prependText: {
    ...globalStyles.textHeading,
    alignSelf: 'center',
    color: COLORS.WHITE,
    fontSize: scaleBaseOnScreenWidth(12),
    marginHorizontal: scaleBaseOnScreenWidth(5),
  },
  reloadButton: {
    marginRight: scaleBaseOnScreenWidth(10),
  },
  safe: {
    backgroundColor: COLORS.MAIN_COLOR,
  },
  title: {
    color: COLORS.WHITE,
    flex: 1,
  },
});

export default Header;
