import Col from './Col';
import React from 'react';
import {View} from 'react-native';
import {formatNumber} from 'utils/dateUtils';

export const Row: any = ({rowStyle, rowData = [], total = 0}) => {
  return (
    <View style={{flexDirection: 'row'}}>
      {rowData.map((item: any, index: number) => (
        <Col
          key={index}
          style={item?.style}
          text={
            item?.name === '0 kg' ? formatNumber(total) + ' KG' : item?.name
          }
        />
      ))}
    </View>
  );
};
