import {DUMPDATA, METRICS, globalStyles} from '../../utils';
import {Row} from './Row';
import {COLORS} from 'assets';
import {AppText} from 'elements';
import moment from 'moment';
import React, {useState} from 'react';
import {FlatList, TouchableOpacity, StyleSheet, View} from 'react-native';
import {ALERT_TYPE, Toast} from 'react-native-alert-notification';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import reactotron from 'reactotron-react-native';
import useDiaryStore from 'stores/diaryStore';
import uuid from 'uuid-random';

export const Table = ({data, renderItem, item, isSync, setDataDetail}: any) => {
  let total = 0;

  if (data?.length <= 0) {
    total = 0;
  } else if (data?.length === 1) {
    total =
      data
        ?.map((el: any) => el?.khoi_luong)
        ?.filter((el: any) => el)
        ?.find((el: any) => el >= 0) || 0;
  } else {
    if (
      data
        ?.map((el: any) => el?.khoi_luong)
        ?.filter((el: any) => el)
        ?.filter((el: any) => el >= 0).length > 0
    ) {
      total =
        data
          ?.map((el: any) => el?.khoi_luong)
          ?.filter((el: any) => el)
          ?.filter((el: any) => el >= 0)
          ?.reduce((a: number, b: number) => Number(a) + Number(b)) || 0;
    }
  }

  // xử lý popup
  const {
    setDiary,
    setDiaryLocal,
    setDiaryDelete,
    diary: {diaryCurrent, diaryLocal = [], diaryDelete},
  } = useDiaryStore();
  const [open, setOpen] = useState(0); // != 0 là open
  const [releaseTime, setReleaseTime] = useState(item?.thoi_gian_tha);
  const [retrieveTime, setRetrieveTime] = useState(item?.thoi_gian_thu);
  const onChangeTime = (ele: any) => {
    if (
      diaryCurrent.thoi_gian_tha !== null &&
      diaryCurrent.thoi_gian_thu === null
    ) {
      //lưu current
      setDiary({
        ...diaryCurrent,
        thoi_gian_tha: new Date(ele?.thoi_gian_tha),
        thoi_gian_thu: new Date(ele?.thoi_gian_thu),
      });
    } else {
      // dạng list
      const _diaryLocal = diaryLocal.map((el: any) => {
        if (el.id === item.id) {
          return {
            ...el,
            thoi_gian_tha: new Date(ele?.thoi_gian_tha),
            thoi_gian_thu: new Date(ele?.thoi_gian_thu),
            is_online: false,
          };
        }
        return el;
      });
      // tạo item mới
      if (item?._id) {
        const cp_item = _diaryLocal.find((ele: any) => item._id === ele.id);
        const cp_item_id = uuid();
        const cp_final = {
          id: cp_item_id,
          item_id: cp_item_id,
          trang_thai: 2,
          is_online: false,
          vi_tri_tha: cp_item?.vi_tri_tha,
          vi_tri_thu: cp_item?.vi_tri_thu,
          thoi_gian_tha: cp_item?.thoi_gian_tha,
          thoi_gian_thu: cp_item?.thoi_gian_thu,
          san_luong: cp_item?.san_luong,
        };
        setDataDetail(cp_final);
        const list = diaryLocal.concat([cp_final]);
        let __diaryLocal = list.filter((ele: any) => ele.id != item?._id);
        setDiaryLocal(
          __diaryLocal?.sort(
            (a: any, b: any) =>
              new Date(a?.thoi_gian_tha) - new Date(b?.thoi_gian_tha),
          ),
        );
        setDiaryDelete(diaryDelete.concat([item?._id]));
      } else {
        const cp_item = _diaryLocal.find((ele: any) => item.id === ele.id);
        setDataDetail(cp_item);
        setDiaryLocal(
          _diaryLocal?.sort(
            (a: any, b: any) =>
              new Date(a?.thoi_gian_tha) - new Date(b?.thoi_gian_tha),
          ),
        );
      }
    }
  };

  const onConfirm = (datetime: any) => {
    if (isSync) {
      setOpen(0);
      return;
    }
    if (open === 1) {
      if (moment(retrieveTime).isBefore(moment(datetime))) {
        setOpen(0);
        // Toast.show({
        //   type: ALERT_TYPE.WARNING,
        //   title: 'Thông báo: ',
        //   textBody: 'Thời gian thả lưới LỚN hơn thời gian thu lưới',
        // });
        setRetrieveTime(
          moment(datetime)
            .clone()
            .add(moment.duration({days: 1})),
        );
        // return;
      }
      setReleaseTime(datetime);
      setOpen(0);
      const param = {thoi_gian_thu: retrieveTime, thoi_gian_tha: datetime};
      onChangeTime(param);
    } else if (open === 2) {
      if (moment(releaseTime).isAfter(moment(datetime))) {
        setOpen(0);
        Toast.show({
          type: ALERT_TYPE.WARNING,
          title: 'Thông báo: ',
          textBody: 'Thời gian thu lưới LỚN hơn thời gian thả lưới',
        });
        return;
      }
      setRetrieveTime(datetime);
      const param = {thoi_gian_tha: releaseTime, thoi_gian_thu: datetime};
      onChangeTime(param);
      setOpen(0);
    }
  };

  return (
    <View>
      {item && (
        <View style={[globalStyles.row, {marginBottom: 10}]}>
          <View style={globalStyles.flex1}>
            <TouchableOpacity
              activeOpacity={isSync || !releaseTime ? 1 : 0.5}
              onPress={() => {
                if (isSync) {
                  setOpen(0);
                  return;
                }
                setOpen(1);
              }}>
              <RenderTextDate
                datetime={releaseTime}
                placeholderText={'THỜI GIAN THẢ LƯỚI'}
              />
            </TouchableOpacity>
          </View>

          <View style={globalStyles.flex1}>
            <TouchableOpacity
              activeOpacity={isSync || !retrieveTime ? 1 : 0.5}
              onPress={() => {
                if (isSync) {
                  setOpen(0);
                  return;
                }
                setOpen(2);
              }}>
              <RenderTextDate
                datetime={retrieveTime}
                placeholderText={'THỜI GIAN THU LƯỚI'}
              />
            </TouchableOpacity>
          </View>
        </View>
      )}
      <Row rowData={DUMPDATA.tableData.header} />
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={(item: any, index: number) => index.toString()}
      />
      <Row rowData={DUMPDATA.tableData.footer} total={total} />

      {open === 1 && (
        <DateTimePickerModal
          date={releaseTime ? new Date(releaseTime) : new Date()}
          maximumDate={new Date()}
          isVisible={true}
          mode="datetime"
          onConfirm={onConfirm}
          onCancel={() => {
            setOpen(0);
          }}
        />
      )}
      {open === 2 && retrieveTime && (
        <DateTimePickerModal
          date={retrieveTime ? new Date(retrieveTime) : new Date()}
          maximumDate={new Date()}
          isVisible={true}
          mode="datetime"
          onConfirm={onConfirm}
          onCancel={() => {
            setOpen(0);
          }}
        />
      )}
    </View>
  );
};

const RenderTextDate = (itemProps: any) => {
  const {placeholderText, datetime} = itemProps;
  return (
    <View style={{margin: 2}}>
      <AppText
        customStyle={{fontSize: METRICS.text_12, color: COLORS.LIGHT_GREY}}
        text={placeholderText}
      />
      <View style={styles.txtDatetime}>
        <AppText
          customStyle={styles.txtDate}
          text={datetime ? moment(datetime).format('HH:mm') : ''}
        />
        <AppText
          customStyle={styles.txtTime}
          text={datetime ? ` - ${moment(datetime).format('DD/MM/YYYY')}` : ''}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  txtDatetime: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderBottomWidth: 0.5,
    borderColor: COLORS.LIGHT_GREY,
    marginBottom: 2,
  },
  txtDate: {
    fontSize: METRICS.text_20,
    padding: 0,
    fontWeight: 'bold',
    color: COLORS.BLACK,
  },
  txtTime: {
    fontSize: METRICS.text_14,
    paddingBottom: 2,
    fontWeight: 'bold',
    color: COLORS.BLACK,
  },
});
