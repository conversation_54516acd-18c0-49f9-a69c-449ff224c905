import COLORS from '../../assets/theme/colors';
import {AppText} from '../../elements';
import React from 'react';
import {View} from 'react-native';

export const Col = ({text, style}) => {
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: COLORS.GREY_F8,
        borderWidth: 1,
        borderColor: COLORS.WHITE,
        padding: 16,
        ...style.viewStyle,
      }}>
      <AppText customStyle={style.textStyle} text={text} />
    </View>
  );
};

export default Col;
