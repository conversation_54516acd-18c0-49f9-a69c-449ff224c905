import StatefulComponent from '../lib/StatefulComponent';
import React from 'react';
import * as Animated from 'react-native-animatable';

export default class DelayRenderComponent extends StatefulComponent {
  static defaultProps = {
    animation: 'slideInUp',
  };
  componentDidMount() {
    this.delayRender(this.props?.delay || 300);
  }

  render() {
    const {isFocus} = this.state;
    const {animation, ...rest} = this.props;
    return isFocus ? (
      <Animated.View
        animation={animation}
        duration={this.props?.duration || 300}
        useNativeDriver={true}
        {...rest}
      />
    ) : null;
  }
}
