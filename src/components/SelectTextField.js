import COLORS from '../assets/theme/colors';
import {IconType} from '../elements/AppIcon';
import StatefulComponent from '../lib/StatefulComponent';
import {globalStyles, METRICS} from '../utils';
import {scaleBaseOnScreenHeight, scaleBaseOnScreenWidth} from '../utils/layout';
import {AppIcon, AppText} from 'elements';
import {isEmpty} from 'lodash';
import moment from 'moment';
import React from 'react';
import {
  Animated,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';

export default class SelectTextField extends StatefulComponent {
  static defaultProps = {
    lightLabel: false,
    required: false,
    shadow: false,
    labelFontSize: scaleBaseOnScreenWidth(12),
    fontSize: scaleBaseOnScreenWidth(15),
  };

  state = {
    showPassword: false,
  };

  get inputStyle() {
    const {value, placeholderText, fontSize} = this.props;
    return [
      styles.inputStyle,
      !isEmpty(value) && isEmpty(placeholderText) && styles.marginTop,
    ];
  }

  animation = new Animated.Value(0);

  doActive = () => {
    const {value} = this.props;
    const isActive = !isEmpty(value);
    Animated.timing(this.animation, {
      toValue: scaleBaseOnScreenHeight(isActive ? 0 : 20),
      useNativeDriver: true,
      duration: 200,
    }).start();
  };

  componentDidMount() {
    this.doActive();
  }

  componentDidUpdate(prevProps) {
    if (isEmpty(this.props?.value) !== isEmpty(prevProps?.value)) {
      this.doActive();
    }
  }

  get label() {
    const {placeholderText, required, labelFontSize, prependIcon} = this.props;
    return (
      <Animated.View
        pointerEvents="none"
        style={[
          styles.labelContainer,
          prependIcon && {left: scaleBaseOnScreenWidth(38)},
          {
            transform: [
              {
                translateY: this.animation,
              },
            ],
          },
          {
            color: COLORS.LIGHT_GREY,
          },
        ]}>
        <AppText
          customStyle={{
            ...styles.label,
            fontSize: 12,
            color: COLORS.LIGHT_GREY,
          }}
          text={placeholderText}
        />
      </Animated.View>
    );
  }

  get append() {
    const {
      iconName,
      iconType,
      iconSize,
      iconColor,
      append = (
        <AppIcon
          color={iconColor || COLORS.GREY}
          name={iconName || 'select-arrows'}
          size={iconSize || scaleBaseOnScreenWidth(15)}
          style={styles.append}
          type={iconType || IconType.entypo}
        />
      ),
    } = this.props;
    if (this.props.disabled) {
      return null;
    }
    if (this.props?.clearable && !isEmpty(this.props.value)) {
      return (
        <AppIcon
          color={COLORS.GREY}
          name={'closecircle'}
          size={scaleBaseOnScreenWidth(15)}
          style={styles.append}
          type={IconType.antDesign}
          onPress={this.props.onClear}
        />
      );
    }
    return append;
  }

  render() {
    const {
      containerStyle,

      value,
      style,
      width,
      onPress,
      appendIcon,
      onPressAppend,
      appendIconType,
      shadow,
      disabled,
      prependIcon,
      prependIconType,
      prependIconSize,
      editable = true,
      appendIconSize,
      prependIconColor,
      area,
      unit,
      isDate,
      showDate,
    } = this.props;
    return (
      <TouchableOpacity
        disabled={disabled || !editable}
        style={[
          styles.containerStyle,
          {width},
          containerStyle,
          shadow
            ? styles.shadow
            : value
            ? styles.bottomBorder
            : styles.bottomBorderNull,
          style,
          containerStyle,
          area && styles.ereaContainer,
          disabled && (this.props?.disabledStyle || {}),
          prependIcon && {paddingLeft: scaleBaseOnScreenWidth(38)},
        ]}
        onPress={onPress}>
        {prependIcon ? (
          <AppIcon
            color={prependIconColor || '#7a8691'}
            name={prependIcon}
            size={prependIconSize || scaleBaseOnScreenWidth(20)}
            style={styles.prependIcon}
            type={prependIconType}
          />
        ) : null}
        {this.label}
        {isDate && value ? (
          <Text
            numberOfLines={area ? 2 : 1}
            style={[
              this.inputStyle,
              {fontSize: scaleBaseOnScreenWidth(METRICS.text_14)},
            ]}>
            <Text style={{fontSize: scaleBaseOnScreenWidth(20)}}>
              {moment(value, 'HH:mm:SS - DD/MM/YYYY').format('HH:mm')}
            </Text>{' '}
            {showDate &&
              moment(value, 'HH:mm:SS - DD/MM/YYYY').format('- DD/MM/YYYY')}
          </Text>
        ) : (
          <Text numberOfLines={area ? 2 : 1} style={this.inputStyle}>
            {value}
          </Text>
        )}

        {editable && isEmpty(appendIcon) ? this.append : null}
        {unit ? <Text style={styles.unit}>{unit}</Text> : null}
        {!isDate && !isEmpty(appendIcon) ? (
          <TouchableOpacity style={styles.append} onPress={onPressAppend}>
            <AppIcon
              color={'#7a8691'}
              name={appendIcon}
              size={appendIconSize || 20}
              type={appendIconType}
            />
          </TouchableOpacity>
        ) : null}
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  append: {
    position: 'absolute',
    right: 15,
  },
  bottomBorder: {
    borderBottomWidth: 1,
    borderColor: COLORS.SILVER,
    borderRadius: scaleBaseOnScreenHeight(0),
  },
  bottomBorderNull: {
    borderBottomWidth: 1,
    borderColor: COLORS.RED,
    borderRadius: scaleBaseOnScreenHeight(0),
  },
  containerStyle: {
    // backgroundColor: COLORS.WHITE,
    marginHorizontal: scaleBaseOnScreenWidth(4),
    marginBottom: scaleBaseOnScreenHeight(8),
    marginTop: scaleBaseOnScreenHeight(2),
    height: scaleBaseOnScreenHeight(54),
    justifyContent: 'center',
    borderRadius: scaleBaseOnScreenHeight(27),
    paddingLeft: scaleBaseOnScreenWidth(4),
    paddingRight: scaleBaseOnScreenWidth(34),
    overflow: 'hidden',
  },
  ereaContainer: {
    height: scaleBaseOnScreenHeight(78),
  },
  inputStyle: {
    color: COLORS.BLACK,
    fontSize: scaleBaseOnScreenWidth(METRICS.text_14),
    fontWeight: 'bold',
    marginTop: Platform.select({
      ios: scaleBaseOnScreenHeight(20),
      android: scaleBaseOnScreenHeight(24),
    }),
    paddingLeft: 0,
    textAlignVertical: 'bottom',
    // flex: 1,
  },
  label: {
    ...globalStyles.textCaption,
    color: COLORS.LIGHT_GREY,
    fontSize: scaleBaseOnScreenWidth(12),
  },
  labelContainer: {
    backgroundColor: 'transparent',
    height: scaleBaseOnScreenHeight(30),
    left: scaleBaseOnScreenWidth(4),
    position: 'absolute',
    right: scaleBaseOnScreenWidth(34),
    top: scaleBaseOnScreenHeight(0),
    zIndex: 2,
  },
  marginTop: {
    paddingTop: scaleBaseOnScreenHeight(22),
  },
  prependIcon: {
    bottom: 10,
    left: 10,
    position: 'absolute',
  },
  required: {
    color: COLORS.BITTER_GREY,
  },
  shadow: {
    borderColor: COLORS.SILVER,
    borderWidth: 2,
  },
  unit: {
    ...globalStyles.textCaption,
    position: 'absolute',
    right: 25,
  },
});
