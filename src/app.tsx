import StorybookUI from '../.storybook';
import AppNaviation from './navigation/AppNavigation';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from 'components/Loading';
import React, {useEffect, useState} from 'react';
import {DevSettings, StatusBar} from 'react-native';
import {AlertNotificationRoot} from 'react-native-alert-notification';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {ModalPortal} from 'react-native-modals';
import {QueryClient, QueryClientProvider} from 'react-query';
import useAppStore from 'stores/appStore';

const queryClient = new QueryClient();

function App() {
  const {loading} = useAppStore();
  if (__DEV__) {
    require('../ReactotronConfig');
  }
  const [openStoryBook, setOpenStoryBook] = useState(false);

  useEffect(() => {
    if (__DEV__) {
      AsyncStorage.getItem('openStoryBook').then(value => {
        setOpenStoryBook(value === 'true');
      });

      DevSettings.addMenuItem('StoryBook', () => {
        const newOpenStoryBook = !openStoryBook;
        setOpenStoryBook(newOpenStoryBook);
        AsyncStorage.setItem('openStoryBook', String(newOpenStoryBook));
      });
    }
  }, [openStoryBook]);

  if (openStoryBook) {
    return <StorybookUI />;
  }

  return (
    <>
      <GestureHandlerRootView>
        <QueryClientProvider client={queryClient}>
          <StatusBar backgroundColor="transparent" translucent />
          <AlertNotificationRoot>
            <Loading isLoading={loading} />
            <AppNaviation />
          </AlertNotificationRoot>
          <ModalPortal />
        </QueryClientProvider>
      </GestureHandlerRootView>
    </>
  );
}

export default App;
