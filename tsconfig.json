{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"jsx": "react", "baseUrl": "./src", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "paths": {"@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@utils/*": ["src/utils/*"], "@assets/*": ["src/assets/*"], "@elements/*": ["src/elements/*"], "@modal/*": ["src/modal/*"], "@services/*": ["src/services/*"], "@hooks/*": ["src/hooks/*"]}}}