

// Memoized fish list component
// const FishList = memo(
//   ({
//     fishList,
//     onRemoveFish,
//   }: {
//     fishList: any[];
//     onRemoveFish: (id: string) => void;
//   }) => {
//     if (!fishList || fishList.length === 0) {
//       return (
//         <TouchableOpacity
//           style={[styles.view_content, {justifyContent: 'center'}]}
//           onPress={() => {}}>
//           <AppText
//             color={COLORS.LIGHT_GREY}
//             font="REGULAR"
//             text={'CHỌN LOÀI CÁ'}
//           />
//         </TouchableOpacity>
//       );
//     }

//     return (
//       <View style={styles.bodyRow}>
//         {fishList.map((fish, idx) => (
//           <View key={fish._id || idx} style={styles.box}>
//             <FishItem item={fish} onRemove={onRemoveFish} />
//           </View>
//         ))}
//       </View>
//     );
//   },
// );
const distributeOneFish = (
  totalWeight: number,
  batchCount: number,
  thap: number,
  cao: number
): number[] => {
  const result = Array(batchCount).fill(0);

  if (totalWeight === 0) return result;

  // Nếu không đủ để phân phối tối thiểu cho tất cả → chỉ gán cho một vài batch
  const maxAssignable = Math.min(batchCount, Math.floor(totalWeight / (thap || 1)));

  if (maxAssignable === 0) {
    // Gán hết vào 1 batch random
    const randIndex = Math.floor(Math.random() * batchCount);
    result[randIndex] = totalWeight;
    return result;
  }

  // Gán thap cho những batch được chọn
  const indexes = _.shuffle([...Array(batchCount).keys()]).slice(0, maxAssignable);
  indexes.forEach(i => result[i] = thap);
  let remaining = totalWeight - thap * maxAssignable;

  // Phân phối phần dư ngẫu nhiên
  const availableIndexes = indexes.filter(i => result[i] < cao);
  while (remaining > 0 && availableIndexes.length > 0) {
    const randIndex = _.sample(availableIndexes);
    if (randIndex !== undefined && result[randIndex] + 1 <= cao) {
      result[randIndex]++;
      remaining--;
    }
  }

  return result;
};

const distributeAllFishToBatches = (batches: any[], listFish: any[]) => {
  const batchCount = batches.length;
  if (batchCount === 0) return [];

  const distributionMap: number[][] = [];

  // Tạo phân phối trước cho từng loài cá
  listFish.forEach((fish, fishIndex) => {
    let totalWeight = Math.round(fish.khoi_luong || 0);
    const thap = fish.khoi_luong_thap ?? 0;
    const cao = fish.khoi_luong_cao === 0 ? Infinity : (fish.khoi_luong_cao ?? Infinity);

    // Nếu tổng không nằm trong khoảng cho phép → gán toàn bộ 0

    if (totalWeight < thap * batchCount || totalWeight > cao * batchCount) {
      console.log('totalWeight < thap * batchCount: ', totalWeight < thap * batchCount);
      // distributionMap[fishIndex] = Array(batchCount).fill(0);
      const result = Array(batchCount).fill(0);
      if (totalWeight <= 0 || thap === 0) {
        distributionMap[fishIndex] = result;
        return;
      }

      // Bước 1: Chọn ngẫu nhiên số lượng batch có thể cấp `thap`
      const assignableBatches = Math.min(batchCount, Math.floor(totalWeight / thap));

      console.log('assignableBatches: ', assignableBatches);
      const shuffledIndexes = _.shuffle([...Array(batchCount).keys()]);
      const selectedIndexes = shuffledIndexes.slice(0, assignableBatches);
      console.log('selectedIndexes: ', selectedIndexes);
      // Bước 2: Gán `thap` cho từng batch được chọn
      selectedIndexes.forEach(i => {
        result[i] = thap;
        totalWeight -= thap;
      });

      // Bước 3: Phân phần dư còn lại (random) cho các batch đã chọn
      // trường hợp nhỏ hơn tổng > 0 và không đủ để gán cho tất cả batch
      if(selectedIndexes.length == 0 ){
        result[0] = totalWeight;
        distributionMap[fishIndex] = result;
        return 
      }

      while (totalWeight > 0 && selectedIndexes.length > 0) {
        const i = _.sample(selectedIndexes)!;
        const maxEach = isFinite(cao) ? cao : totalWeight;
        if (result[i] + 1 <= maxEach) {
          result[i]++;
          totalWeight--;
        }
      }

      distributionMap[fishIndex] = result;
    } else {
      distributionMap[fishIndex] = distributeOneFish(totalWeight, batchCount, thap, cao);
    }
  });

  // Gán sản lượng cho từng batch từ map
  return batches.map((batch, batchIndex) => {
    const san_luong = listFish.map((fish, fishIndex) => ({
      ...fish,
      khoi_luong: distributionMap[fishIndex][batchIndex],
    }));

    return {
      ...batch,
      san_luong,
    };
  });
};
