{"generatedAt": 1713693536716, "builder": {"name": "webpack5"}, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "packageManager": {"type": "yarn", "version": "1.22.19"}, "storybookVersion": "6.5.16", "language": "typescript", "storybookPackages": {"storybook": {"version": "8.0.8"}, "@chromatic-com/storybook": {"version": "1.3.3"}, "@storybook/addon-ondevice-actions": {"version": "7.6.18"}, "@storybook/addon-ondevice-backgrounds": {"version": "7.6.18"}, "@storybook/addon-ondevice-controls": {"version": "7.6.18"}, "@storybook/addon-ondevice-notes": {"version": "7.6.18"}, "@storybook/addon-webpack5-compiler-babel": {"version": "3.0.3"}, "@storybook/builder-webpack5": {"version": "6.5.16"}, "@storybook/manager-webpack5": {"version": "6.5.16"}, "@storybook/react": {"version": "6.5.16"}, "@storybook/react-native": {"version": "7.6.18"}, "@storybook/react-webpack5": {"version": "8.0.8"}}, "framework": {"name": "react"}, "addons": {"@storybook/addon-essentials": {"version": "6.5.16"}, "@storybook/addon-react-native-web": {"version": "0.0.23"}}}