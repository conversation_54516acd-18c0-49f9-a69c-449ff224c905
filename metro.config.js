const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const config = {
  transformer: {
    unstable_allowRequireContext: true,
  },
  resolver: {
    extraNodeModules: {
      '@components': './src/components',
      '@screens': './src/screens',
      '@utils': './src/utils',
      '@assets': './src/assets',
      '@elements': './src/elements',
      '@services': './src/services',
      '@hooks': './src/hooks',
    },
    resolveRequest: (context, moduleName, platform) => {
      const defaultResolveResult = context.resolveRequest(
        context,
        moduleName,
        platform,
      );

      if (
        process.env.STORYBOOK_ENABLED !== 'true' &&
        defaultResolveResult?.filePath?.includes?.('.ondevice/')
      ) {
        return {
          type: 'empty',
        };
      }

      return defaultResolveResult;
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
