import LoginScreen from '../../../src/screens/loginScreen';
import type {Meta, StoryObj} from '@storybook/react';
import React from 'react';

const LoginScreenStory: Meta<typeof LoginScreen> = {
  title: 'LoginScreen',
  component: LoginScreen,
  argTypes: {
    onPress: {action: 'pressed the button'},
  },
  args: {
    text: 'Hello world',
  },
  decorators: [Story => <Story />],
};

export default LoginScreenStory;

export const Basic: StoryObj<typeof LoginScreen> = {};
