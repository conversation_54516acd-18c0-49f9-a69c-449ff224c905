🩺 The doctor is checking the health of your Storybook..
╭ Incompatible packages found ────────────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                                                     │
│   The following packages are incompatible with Storybook 6.5.16 as they depend on different major versions of Storybook packages:   │
│   - storybook@8.0.8                                                                                                                 │
│    Repo: https://github.com/storybookjs/storybook/tree/next/code/lib/cli                                                            │
│   - @storybook/addon-ondevice-actions@7.6.18                                                                                        │
│    Repo: https://storybook.js.org/                                                                                                  │
│   - @storybook/addon-ondevice-backgrounds@7.6.18                                                                                    │
│    Repo: https://storybook.js.org                                                                                                   │
│   - @storybook/addon-ondevice-controls@7.6.18                                                                                       │
│   - @storybook/addon-ondevice-notes@7.6.18                                                                                          │
│   - @storybook/react-native@7.6.18                                                                                                  │
│    Repo: https://storybook.js.org/                                                                                                  │
│   - @storybook/react-webpack5@8.0.8                                                                                                 │
│    Repo: https://github.com/storybookjs/storybook/tree/next/code/frameworks/react-webpack5                                          │
│                                                                                                                                     │
│                                                                                                                                     │
│   Please consider updating your packages or contacting the maintainers for compatibility details.                                   │
│   For more on Storybook 8 compatibility, see the linked GitHub issue:                                                               │
│   https://github.com/storybookjs/storybook/issues/26031                                                                             │
│                                                                                                                                     │
╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯

You can always recheck the health of your project by running:
npx storybook doctor

Full logs are available in /Users/<USER>/Desktop/cdtvn_v2/doctor-storybook.log

